<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * 🔥 ENHANCED VALIDATION TRAIT - BEAST MODE VALIDATION 🔥
 * 
 * Comprehensive validation helpers for robust form handling
 */
trait EnhancedValidation
{
    /**
     * Validate date range with comprehensive checks
     */
    protected function validateDateRange(?string $startDate, ?string $endDate): array
    {
        $errors = [];
        
        try {
            if (empty($startDate)) {
                $errors['startDate'] = 'Start date is required';
                return $errors;
            }
            
            if (empty($endDate)) {
                $errors['endDate'] = 'End date is required';
                return $errors;
            }
            
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
            
            // Check if start date is before end date
            if ($start->gt($end)) {
                $errors['startDate'] = 'Start date must be before end date';
            }
            
            // Check if dates are not too far in the future
            if ($start->gt(Carbon::now())) {
                $errors['startDate'] = 'Start date cannot be in the future';
            }
            
            if ($end->gt(Carbon::now())) {
                $errors['endDate'] = 'End date cannot be in the future';
            }
            
            // Check if date range is reasonable (not more than 5 years)
            if ($start->diffInYears($end) > 5) {
                $errors['dateRange'] = 'Date range cannot exceed 5 years';
            }
            
        } catch (\Exception $e) {
            Log::error('Date validation error: ' . $e->getMessage());
            $errors['dateRange'] = 'Invalid date format provided';
        }
        
        return $errors;
    }

    /**
     * Validate export parameters
     */
    protected function validateExportParameters(array $data): array
    {
        $errors = [];
        
        // Validate required fields
        $requiredFields = ['startDate', 'endDate'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[$field] = ucfirst($field) . ' is required for export';
            }
        }
        
        // Validate date range if dates are provided
        if (isset($data['startDate']) && isset($data['endDate'])) {
            $dateErrors = $this->validateDateRange($data['startDate'], $data['endDate']);
            $errors = array_merge($errors, $dateErrors);
        }
        
        // Validate sort parameters if provided
        if (isset($data['sortBy'])) {
            $validSortFields = [
                'total_invoice_amount',
                'total_payments',
                'outstanding_amount',
                'payment_ratio',
                'total_invoices',
                'total_amount',
                'total_quantity',
                'invoice_count',
                'avg_price'
            ];
            
            if (!in_array($data['sortBy'], $validSortFields)) {
                $errors['sortBy'] = 'Invalid sort field specified';
            }
        }
        
        if (isset($data['sortDirection'])) {
            if (!in_array($data['sortDirection'], ['asc', 'desc'])) {
                $errors['sortDirection'] = 'Sort direction must be either "asc" or "desc"';
            }
        }
        
        return $errors;
    }

    /**
     * Validate report type parameters
     */
    protected function validateReportType(?string $reportType): array
    {
        $errors = [];
        
        if (empty($reportType)) {
            $errors['reportType'] = 'Report type is required';
            return $errors;
        }
        
        $validReportTypes = [
            'summary',
            'revenue_trends',
            'tax_summary',
            'payment_methods',
            'status_distribution',
            'overdue_analysis',
            'payment_trends',
            'invoice_aging',
            'performance',
            'trends',
            'comparison'
        ];
        
        if (!in_array($reportType, $validReportTypes)) {
            $errors['reportType'] = 'Invalid report type specified';
        }
        
        return $errors;
    }

    /**
     * Validate analytics type parameters
     */
    protected function validateAnalyticsType(?string $analyticsType): array
    {
        $errors = [];
        
        if (empty($analyticsType)) {
            $errors['analyticsType'] = 'Analytics type is required';
            return $errors;
        }
        
        $validAnalyticsTypes = [
            'status_distribution',
            'overdue_analysis',
            'payment_trends',
            'invoice_aging',
            'performance',
            'trends',
            'comparison'
        ];
        
        if (!in_array($analyticsType, $validAnalyticsTypes)) {
            $errors['analyticsType'] = 'Invalid analytics type specified';
        }
        
        return $errors;
    }

    /**
     * Sanitize and validate form data
     */
    protected function sanitizeFormData(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Trim whitespace
                $value = trim($value);
                
                // Remove null bytes
                $value = str_replace("\0", '', $value);
                
                // Handle empty strings
                if ($value === '') {
                    $value = null;
                }
            }
            
            $sanitized[$key] = $value;
        }
        
        return $sanitized;
    }

    /**
     * Get comprehensive validation rules for date fields
     */
    protected function getDateValidationRules(string $prefix = ''): array
    {
        $startDateField = $prefix ? "{$prefix}.startDate" : 'startDate';
        $endDateField = $prefix ? "{$prefix}.endDate" : 'endDate';
        
        return [
            $startDateField => [
                'required',
                'date',
                'before_or_equal:' . $endDateField,
                'before_or_equal:today'
            ],
            $endDateField => [
                'required',
                'date',
                'after_or_equal:' . $startDateField,
                'before_or_equal:today'
            ]
        ];
    }

    /**
     * Get validation attributes for better error messages
     */
    protected function getValidationAttributes(string $prefix = ''): array
    {
        $attributes = [
            'startDate' => 'Start Date',
            'endDate' => 'End Date',
            'sortBy' => 'Sort By',
            'sortDirection' => 'Sort Direction',
            'reportType' => 'Report Type',
            'analyticsType' => 'Analytics Type',
        ];
        
        if ($prefix) {
            $prefixedAttributes = [];
            foreach ($attributes as $key => $value) {
                $prefixedAttributes["{$prefix}.{$key}"] = $value;
            }
            return $prefixedAttributes;
        }
        
        return $attributes;
    }

    /**
     * Validate and prepare data for safe processing
     */
    protected function validateAndPrepareData(array $data, array $requiredFields = []): array
    {
        // Sanitize input data
        $data = $this->sanitizeFormData($data);
        
        // Check required fields
        $errors = [];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || $data[$field] === null) {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }
        
        if (!empty($errors)) {
            throw new \Illuminate\Validation\ValidationException(
                validator($data, []), 
                response()->json(['errors' => $errors], 422)
            );
        }
        
        return $data;
    }

    /**
     * Log validation errors for monitoring
     */
    protected function logValidationError(string $context, array $errors, array $data = []): void
    {
        Log::warning("Validation failed in {$context}", [
            'errors' => $errors,
            'data' => $data,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ]);
    }
}
