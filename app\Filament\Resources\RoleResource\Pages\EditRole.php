<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to delete this role? This action cannot be undone and will remove the role from all users.'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterSave(): void
    {
        // Clear role cache after update
        cache()->forget('permissions_by_category');
        cache()->forget('spatie.permission.cache');
        
        Notification::make()
            ->title('Role updated successfully')
            ->body("The role '{$this->record->name}' has been updated with " . $this->record->permissions()->count() . " permissions.")
            ->success()
            ->send();
    }
}
