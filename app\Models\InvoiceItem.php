<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\InvoiceItem
 *
 * @property int $id
 * @property int $invoice_id
 * @property int|null $service_id
 * @property int|null $product_id (deprecated, for backward compatibility)
 * @property string|null $product_name
 * @property int $quantity
 * @property float $price
 * @property float $total
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\InvoiceItemTax[] $invoiceItemTax
 * @property-read int|null $invoice_item_tax_count
 * @property-read \App\Models\Service|null $service
 * @property-read \App\Models\Product|null $product (deprecated, for backward compatibility)
 *
 * @method static Builder|InvoiceItem newModelQuery()
 * @method static Builder|InvoiceItem newQuery()
 * @method static Builder|InvoiceItem query()
 * @method static Builder|InvoiceItem whereCreatedAt($value)
 * @method static Builder|InvoiceItem whereId($value)
 * @method static Builder|InvoiceItem whereInvoiceId($value)
 * @method static Builder|InvoiceItem wherePrice($value)
 * @method static Builder|InvoiceItem whereProductId($value)
 * @method static Builder|InvoiceItem whereProductName($value)
 * @method static Builder|InvoiceItem whereQuantity($value)
 * @method static Builder|InvoiceItem whereTotal($value)
 * @method static Builder|InvoiceItem whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class InvoiceItem extends Model
{
    use HasFactory;

    /**
     * Validation rules
     *
     * @var array
     */
    public static $rules = [
        'service_id' => 'nullable|integer',
        'product_id' => 'nullable|integer', // Backward compatibility
        'product_name' => 'required_without:service_id,product_id|string|max:255',
        'description' => 'nullable|string',
        'quantity' => 'required|regex:/^\d*(\.\d{1,2})?$/',
        'price' => 'required|regex:/^\d+(\.\d{1,2})?$/',
    ];

    protected $table = 'invoice_items';

    public $fillable = [
        'invoice_id',
        'service_id',
        'product_id', // Backward compatibility
        'product_name',
        'description',
        'quantity',
        'price',
        'total',
    ];

    protected $casts = [
        'invoice_id' => 'integer',
        'service_id' => 'integer',
        'product_id' => 'integer', // Backward compatibility
        'product_name' => 'string',
        'description' => 'string',
        'quantity' => 'double',
        'price' => 'double',
        'total' => 'double',
    ];

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    // Backward compatibility: Keep product relationship
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function invoiceItemTax(): HasMany
    {
        return $this->hasMany(InvoiceItemTax::class);
    }

    // Helper method to get the service/product name
    public function getItemNameAttribute(): string
    {
        if (!empty($this->product_name)) {
            return $this->product_name;
        }

        if ($this->service) {
            return $this->service->name;
        }

        if ($this->product) {
            return $this->product->name;
        }

        return 'Unknown Service';
    }
}
