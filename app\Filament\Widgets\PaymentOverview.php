<?php

namespace App\Filament\Widgets;

use App\Models\Invoice;
use App\Models\Payment;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;

class PaymentOverview extends ChartWidget
{
    protected static ?int $sort = 3;
    protected static ?array $options = [
        'responsive' => true,
        'maintainAspectRatio' => false,
        'plugins' => [
            'legend' => [
                'display' => true,
                'position' => 'bottom',
                'labels' => [
                    'usePointStyle' => true,
                    'padding' => 20,
                    'font' => [
                        'size' => 12,
                    ],
                ],
            ],
            'tooltip' => [
                'enabled' => true,
                'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                'titleColor' => '#fff',
                'bodyColor' => '#fff',
                'borderColor' => '#fff',
                'borderWidth' => 1,
            ],
        ],
        'animation' => [
            'animateRotate' => true,
            'animateScale' => true,
            'duration' => 1000,
        ],
        'cutout' => '60%',
    ];
    protected static ?string $maxHeight = '377px';

    public function getHeading(): string|Htmlable|null
    {
        return __('messages.admin_dashboard.payment_overview');
    }

    protected function getData(): array
    {
        $data = [];
        $invoice = Invoice::toBase()->get();

        // Only include approved payments in received amount calculation
        $data['received_amount'] = Payment::where('is_approved', Payment::APPROVED)->sum('amount');
        $data['invoice_amount'] = $invoice->where('status', '!=', Invoice::DRAFT)->sum('final_amount');
        $data['due_amount'] = max(0, $data['invoice_amount'] - $data['received_amount']);

        // Ensure we have data to display
        if ($data['received_amount'] == 0 && $data['due_amount'] == 0) {
            $data['received_amount'] = 0;
            $data['due_amount'] = 1; // Show minimal slice for empty state
        }

        $data['labels'] = [
            __('messages.received_amount'),
            __('messages.invoice.due_amount'),
        ];
        $data['dataPoints'] = [
            floatval($data['received_amount']),
            floatval($data['due_amount'])
        ];

        return [
            'labels' => $data['labels'],
            'datasets' => [
                [
                    'label' => __('messages.admin_dashboard.payment_overview'),
                    'data' => $data['dataPoints'],
                    'backgroundColor' => [
                        'rgba(76, 175, 80, 0.8)',   // Green for received
                        'rgba(255, 138, 128, 0.8)', // Red for due
                    ],
                    'borderColor' => [
                        'rgba(76, 175, 80, 1)',
                        'rgba(255, 138, 128, 1)',
                    ],
                    'borderWidth' => 2,
                    'hoverBackgroundColor' => [
                        'rgba(76, 175, 80, 0.9)',
                        'rgba(255, 138, 128, 0.9)',
                    ],
                    'hoverBorderColor' => [
                        'rgba(76, 175, 80, 1)',
                        'rgba(255, 138, 128, 1)',
                    ],
                ]
            ],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
