<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleResource\Pages;
use App\Services\RoleManagementService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\CheckboxList;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationLabel = 'Roles & Permissions';
    protected static ?string $navigationGroup = 'User Management';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Role Information')
                    ->description('Basic role details and description')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (string $context, $state, callable $set) {
                                        if ($context === 'create') {
                                            $set('name', str($state)->slug()->toString());
                                        }
                                    }),
                                
                                TextInput::make('guard_name')
                                    ->default('web')
                                    ->required()
                                    ->disabled(),
                            ]),
                        
                        Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Section::make('Permissions')
                    ->description('Select permissions for this role')
                    ->schema([
                        static::getPermissionsFormField(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->color('primary'),
                
                TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                
                BadgeColumn::make('users_count')
                    ->label('Users')
                    ->counts('users')
                    ->color('success'),
                
                BadgeColumn::make('permissions_count')
                    ->label('Permissions')
                    ->counts('permissions')
                    ->color('info'),
                
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                Action::make('manage_users')
                    ->label('Manage Users')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->modalHeading(fn (Role $record): string => "Manage Users for {$record->name}")
                    ->modalDescription('Assign or remove users from this role')
                    ->modalWidth(MaxWidth::FourExtraLarge)
                    ->form([
                        CheckboxList::make('users')
                            ->relationship('users', 'name')
                            ->searchable()
                            ->bulkToggleable()
                            ->columns(3)
                            ->gridDirection('row'),
                    ])
                    ->action(function (array $data, Role $record): void {
                        $record->users()->sync($data['users']);
                        
                        Notification::make()
                            ->title('Users updated successfully')
                            ->success()
                            ->send();
                    }),
                
                Action::make('clone')
                    ->label('Clone Role')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('gray')
                    ->form([
                        TextInput::make('name')
                            ->required()
                            ->unique('roles', 'name')
                            ->default(fn (Role $record): string => $record->name . ' Copy'),
                        
                        Textarea::make('description')
                            ->default(fn (Role $record): string => $record->description ?? ''),
                    ])
                    ->action(function (array $data, Role $record): void {
                        $newRole = Role::create([
                            'name' => $data['name'],
                            'guard_name' => $record->guard_name,
                            'description' => $data['description'],
                        ]);
                        
                        $newRole->syncPermissions($record->permissions);
                        
                        Notification::make()
                            ->title('Role cloned successfully')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    protected static function getPermissionsFormField(): CheckboxList
    {
        $roleManagementService = app(RoleManagementService::class);
        $permissionsByCategory = $roleManagementService->getPermissionsByCategory();
        
        $options = [];
        $descriptions = [];
        
        foreach ($permissionsByCategory as $category => $permissions) {
            foreach ($permissions as $permission) {
                $options[$permission->name] = "{$category}: {$permission->name}";
                $descriptions[$permission->name] = $permission->description ?? $permission->name;
            }
        }

        return CheckboxList::make('permissions')
            ->relationship('permissions', 'name')
            ->options($options)
            ->descriptions($descriptions)
            ->searchable()
            ->bulkToggleable()
            ->columns(2)
            ->gridDirection('row')
            ->columnSpanFull();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'primary';
    }
}
