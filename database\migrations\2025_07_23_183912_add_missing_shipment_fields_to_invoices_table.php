<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add missing shipment fields as requested in PRIORITY 4
            $table->string('mawb_bl_number', 100)->nullable()->after('tracking_number')
                ->comment('MAWB/B/L: Master Air Waybill/Bill of Lading number');
            $table->decimal('weight_expected', 10, 2)->nullable()->after('mawb_bl_number')
                ->comment('Weight Expected: Expected shipment weight');
            $table->decimal('weight_recorded', 10, 2)->nullable()->after('weight_expected')
                ->comment('Weight Recorded: Actual recorded weight');
            $table->date('date_of_arrival')->nullable()->after('weight_recorded')
                ->comment('Date of Arrival: Shipment arrival date');
            $table->string('bl_mawb_reference', 100)->nullable()->after('date_of_arrival')
                ->comment('B/L/MAWB: Bill of Lading/Master Air Waybill reference');

            // Add indexes for searchability
            $table->index(['mawb_bl_number']);
            $table->index(['bl_mawb_reference']);
            $table->index(['date_of_arrival']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['mawb_bl_number']);
            $table->dropIndex(['bl_mawb_reference']);
            $table->dropIndex(['date_of_arrival']);

            // Drop columns
            $table->dropColumn([
                'mawb_bl_number',
                'weight_expected',
                'weight_recorded',
                'date_of_arrival',
                'bl_mawb_reference'
            ]);
        });
    }
};
