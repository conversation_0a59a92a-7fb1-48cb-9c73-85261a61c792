<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProductAnalyticsExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $productData;
    protected string $startDate;
    protected string $endDate;

    public function __construct(array $productData, string $startDate, string $endDate)
    {
        $this->productData = $productData;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->productData as $product) {
            $data[] = [
                $product['product_name'] ?? 'Unknown Product',
                $product['product_code'] ?? 'N/A',
                $product['total_quantity'] ?? 0,
                number_format($product['total_amount'] ?? 0, 2),
                number_format($product['avg_price'] ?? 0, 2),
                $product['total_invoices'] ?? 0,
                number_format($product['revenue_percentage'] ?? 0, 2) . '%',
                $product['last_sold_date'] ?? 'N/A',
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Product Name',
            'Product Code',
            'Total Quantity Sold',
            'Total Revenue',
            'Average Price',
            'Total Invoices',
            'Revenue Percentage',
            'Last Sold Date',
        ];
    }

    public function title(): string
    {
        return 'Product Analytics';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
