<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add advanced shipment tracking fields
            $table->string('mawb_bl_number', 100)->nullable()->after('tracking_number')
                ->comment('Master Air Waybill / Bill of Lading number');

            $table->decimal('weight_expected', 10, 2)->nullable()->after('mawb_bl_number')
                ->comment('Expected shipment weight');

            $table->decimal('weight_recorded', 10, 2)->nullable()->after('weight_expected')
                ->comment('Actual recorded shipment weight');

            $table->string('bl_mawb_reference', 100)->nullable()->after('weight_recorded')
                ->comment('Bill of Lading / Master Air Waybill reference');

            $table->date('date_of_arrival')->nullable()->after('bl_mawb_reference')
                ->comment('Expected or actual date of arrival');

            // Add indexes for searchability
            $table->index(['mawb_bl_number']);
            $table->index(['bl_mawb_reference']);
            $table->index(['date_of_arrival']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['mawb_bl_number']);
            $table->dropIndex(['bl_mawb_reference']);
            $table->dropIndex(['date_of_arrival']);

            // Drop columns
            $table->dropColumn([
                'mawb_bl_number',
                'weight_expected',
                'weight_recorded',
                'bl_mawb_reference',
                'date_of_arrival'
            ]);
        });
    }
};
