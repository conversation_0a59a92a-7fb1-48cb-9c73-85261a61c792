<div id="defaultTemplate" class="p-6 bg-white shadow-lg rounded-lg">
    
    <div class="w-full mx-auto pdf">
        <div class="w-full">
            <div class="logo-img mb-8 h-full w-full">
                <img src="<?php echo e(getLogoUrl()); ?>" alt="images" class="h-full w-full object-cover" />
            </div>
            <div>
                <div class="overflow-auto w-full mb-60px">
                    <table class="table w-full  table-auto border-collapse border border-gray-200 align-top">
                        <thead>
                            <tr>
                                <th class="text-start bg-white-100 border border-gray-200  py-1 px-21px align-top">
                                    <strong
                                        class="text-gray-100 text-sm font-bold"><?php echo e(__('messages.common.from')); ?></strong>
                                </th>
                                <th
                                    class="text-start bg-white-100 border border-gray-200 text-gray-100 text-sm font-bold py-1 px-21px align-top">
                                    <strong
                                        class="text-gray-100 text-sm font-bold"><?php echo e(__('messages.common.to')); ?></strong>
                                </th>
                                <th
                                    class="text-start bg-white-100 border border-gray-200 text-gray-100 text-sm font-bold py-1 px-21px uppercase align-top">
                                    <strong
                                        class="text-gray-100 text-sm font-bold"><?php echo e(__('messages.common.invoice')); ?></strong>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="py-1 px-21px border border-gray-200 align-top">
                                    <p class="mb-5px text-nowrap"><strong
                                            class="text-sm text-gray-100 leading-18px"><?php echo e(__('messages.common.name')); ?>:</strong>
                                        <span class="text-sm text-gray-100 leading-18px"><?php echo e($companyName); ?></span>
                                    </p>
                                    <p class="mb-5px text-nowrap"><strong
                                            class="text-sm text-gray-100 leading-18px"><?php echo e(__('messages.common.address')); ?>:</strong>
                                        <span class="text-sm text-gray-100 leading-18px"><?php echo e($companyAddress); ?></span>
                                    </p>
                                    <p class="mb-5px text-nowrap"><strong
                                            class="text-sm text-gray-100 leading-18px"><?php echo e(__('messages.user.phone')); ?>:</strong>
                                        <span class="text-sm text-gray-100 leading-18px"><?php echo e($companyPhone); ?></span>
                                    </p>
                                    <p class="mb-5px text-nowrap"><strong
                                            class="text-sm text-gray-100 leading-18px"><?php echo e(getVatNoLabel()); ?>:</strong>
                                        <span class="text-sm text-gray-100 leading-18px"><?php echo e($gstNo); ?></span>
                                    </p>
                                </td>
                                <td class="py-1 px-21px border border-gray-200 align-top">
                                    <p class="mb-3 text-sm text-gray-100 leading-18px text-nowrap">
                                        &lt;<?php echo e(__('messages.invoice.client_name')); ?>&gt;
                                    </p>
                                    <p class="text-sm text-gray-100 leading-18px mb-3 text-nowrap">
                                        &lt;<?php echo e(__('messages.invoice.client_email')); ?>

                                        Email&gt;</p>
                                    <p class="text-sm text-gray-100 leading-18px mb-3 text-nowrap">
                                        &lt;<?php echo e(__('messages.client_address')); ?>

                                        Address&gt;</p>
                                    <p class="text-sm text-gray-100 leading-18px mb-3 text-nowrap">
                                        &lt;<?php echo e(getVatNoLabel()); ?>&gt;</p>
                                </td>
                                <td class="py-1 px-21px border border-gray-200 align-top">
                                    <p class="text-sm text-gray-300 leading-18px mb-3 text-nowrap">
                                        <strong><?php echo e(__('messages.invoice.invoice_id')); ?>:</strong>
                                        #9CQ5X7
                                    </p>
                                    <p class="text-sm text-gray-300 leading-18px mb-3 text-nowrap">
                                        <strong><?php echo e(__('messages.invoice.invoice_date')); ?>:
                                        </strong>25/09/2020
                                    </p>
                                    <p class="text-sm text-gray-300 leading-18px mb-3 text-nowrap">
                                        <strong><?php echo e(__('messages.invoice.due_date')); ?>:
                                        </strong>
                                        26/09/2020
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="overflow-auto shadow-1 mb-1">
                    <table class="w-full ">
                        <thead>
                            <tr>
                                <th class="py-3 ps-30px pe-1 border-b border-gray-200 bg-white-100 text-start leading-14 rounded-tl-10px text-nowrap"
                                    style="width: 6%;">
                                    <strong class="text-gray-100">#</strong>
                                </th>
                                <th class="py-3 ps-30px pe-1 border-b border-gray-200 uppercase text-start bg-white-100 leading-14 text-nowrap"
                                    style="width: 40%;">
                                    <strong class="text-gray-100"><?php echo e(__('messages.item')); ?></strong>
                                </th>
                                <th class="py-3 ps-30px pe-1 border-b border-gray-200 uppercase bg-white-100 leading-14 text-nowrap"
                                    style="width:12%;">
                                    <strong class="text-gray-100"><?php echo e(__('messages.invoice.qty')); ?></strong>
                                </th>
                                <th class="py-3 ps-30px pe-1 border-b border-gray-200 uppercase bg-white-100 leading-14 text-nowrap"
                                    style="width:14%">
                                    <strong class="text-gray-100"><?php echo e(__('messages.product.unit_price')); ?></strong>
                                </th>
                                <th class="py-3 ps-30px pe-1 border-b border-gray-200 uppercase bg-white-100 leading-14 text-nowrap"
                                    style="width:14%">
                                    <strong class="text-gray-100"><?php echo e(__('messages.invoice.tax')); ?>(%)</strong>
                                </th>
                                <th class="py-3 ps-30px pe-1 border-b border-gray-200 uppercase text-start bg-white-100 leading-14 rounded-tr-10px text-nowrap"
                                    style="width:14%">
                                    <strong class="text-gray-100"><?php echo e(__('messages.invoice.amount')); ?></strong>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 3; $i++): ?>
                                <tr>
                                    <td class="py-2.5 ps-30px pe-1 border-b border-gray-200 align-middle">
                                        <span class="text-sm text-gray-100 !leading-normal"><?php echo e($i); ?></span>
                                    </td>
                                    <td
                                        class="py-2.5 ps-30px pe-1 border-b border-gray-200 align-middle text-sm text-gray-100">
                                        <p class="text-sm text-gray-100 !leading-normal"><?php echo e(__('messages.item')); ?>

                                            <?php echo e($i); ?></p>
                                    </td>
                                    <td
                                        class="border-b border-gray-200 !leading-normal py-2.5 ps-30px pe-1 text-center text-sm text-gray-100 align-middle">
                                        1</td>
                                    <td
                                        class="border-b border-gray-200 !leading-normal py-2.5 ps-30px pe-1 text-center text-sm text-gray-100 align-middle">
                                        <?php echo e(getCurrencyAmount(100, true)); ?></td>
                                    <td
                                        class="border-b border-gray-200 !leading-normal py-2.5 ps-30px pe-1 text-center text-sm text-gray-100 align-middle">
                                        N/A</td>
                                    <td
                                        class="border-b border-gray-200 !leading-normal py-2.5 ps-30px pe-1 text-sm text-gray-100 align-middle">
                                        <?php echo e(getCurrencyAmount(100, true)); ?></td>
                                </tr>
                            <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                        </tbody>
                    </table>
                </div>
                <div class="mt-5 mb-9">
                    <table class="w-full">
                        <tbody>
                            <tr>
                                <td style="width: 75%;">
                                    <div>
                                        <div>
                                            <p class="text-15px mb-3px text-gray-700">
                                                <b><?php echo e(__('messages.payment_qr_codes.payment_qr_code')); ?></b>
                                            </p>
                                            <div class="h-full w-full qr-img">
                                                <img src="../assets/images/qrcode.png" alt="qr-code"
                                                    class="h-full w-full object-cover mt-2">
                                            </div>
                                        </div>
                                    </div>

                                </td>
                                <td class="text-end" style="width: 25%;">
                                    <table class="ms-auto w-full">
                                        <tbody class="text-end">
                                            <tr>
                                                <td>
                                                    <strong
                                                        class="text-sm text-gray-700 text-nowrap"><?php echo e(__('messages.invoice.amount')); ?>:</strong>
                                                </td>
                                                <td class="text-sm text-gray-700 text-nowrap">
                                                    <?php echo e(getCurrencyAmount(300, true)); ?> </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong
                                                        class="text-sm text-gray-700 text-nowrap"><?php echo e(__('messages.invoice.discount')); ?>:</strong>
                                                </td>
                                                <td class="text-sm text-gray-700 text-nowrap">
                                                    <?php echo e(getCurrencyAmount(50, true)); ?> </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong
                                                        class="text-sm text-gray-700 text-nowrap"><?php echo e(__('messages.invoice.tax')); ?>:</strong>
                                                </td>
                                                <td class="text-sm text-gray-700 text-nowrap">0%</td>
                                            </tr>
                                            <tr>
                                                <td><strong
                                                        class="text-sm text-gray-700 text-nowrap"><?php echo e(__('messages.invoice.total')); ?>:</strong>
                                                </td>
                                                <td class="text-nowrap text-sm text-gray-700">
                                                    <?php echo e(getCurrencyAmount(250, true)); ?> </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                </div>
                <div class="py-3 px-15px mb-10 bg-gray-400 border-transparent text-gray-100 border rounded-10px">
                    <h4 class="text-base text-gray-100 mb-5px font-medium"><?php echo e(__('messages.client.notes')); ?>:</h4>
                    <p class="text-sm text-gray-300 mb-4 font-normal">
                        Paypal , Stripe &amp; manual payment method accept.<br>
                        Net 10 – Payment due in 10 days from invoice date.<br>
                        Net 30 – Payment due in 30 days from invoice date.
                    </p>
                </div>
                <div>
                    <h4 class="text-gray-100 mb-5px txt-sm font-medium"><?php echo e(__('messages.invoice.terms')); ?>:</h4>
                    <p class="text-gray-300 text-sm mb-4 font-normal"><?php echo e(__('messages.invoice.total')); ?> ; 1% 10 Net
                        30, 1% discount
                        if payment
                        received within 10 days otherwise payment 30 days after invoice date.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/forms/components/invoiceTemplates/defaultTemplate.blade.php ENDPATH**/ ?>