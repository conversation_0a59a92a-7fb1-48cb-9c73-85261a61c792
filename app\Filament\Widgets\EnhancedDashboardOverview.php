<?php

namespace App\Filament\Widgets;

use App\Models\Invoice;
use App\Models\Client;
use App\Models\Payment;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EnhancedDashboardOverview extends BaseWidget
{
    protected static ?int $sort = 1;
    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $previousMonth = Carbon::now()->subMonth()->startOfMonth();
        
        // Revenue calculations
        $currentRevenue = Invoice::where('status', 'paid')
            ->whereBetween('created_at', [$currentMonth, Carbon::now()])
            ->sum('total');
            
        $previousRevenue = Invoice::where('status', 'paid')
            ->whereBetween('created_at', [$previousMonth, $currentMonth])
            ->sum('total');
            
        $revenueGrowth = $previousRevenue > 0 
            ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 
            : 0;

        // Invoice calculations
        $totalInvoices = Invoice::count();
        $paidInvoices = Invoice::where('status', 'paid')->count();
        $pendingInvoices = Invoice::where('status', 'sent')->count();
        $overdueInvoices = Invoice::where('status', 'overdue')->count();

        // Client calculations
        $totalClients = Client::count();
        $activeClients = Client::whereHas('invoices', function ($query) use ($currentMonth) {
            $query->where('created_at', '>=', $currentMonth);
        })->count();

        // Payment calculations
        $totalPayments = Payment::sum('amount');
        $monthlyPayments = Payment::whereBetween('created_at', [$currentMonth, Carbon::now()])
            ->sum('amount');

        return [
            Stat::make('Total Revenue', '$' . number_format($currentRevenue, 2))
                ->description($revenueGrowth >= 0 ? '+' . number_format($revenueGrowth, 1) . '% from last month' : number_format($revenueGrowth, 1) . '% from last month')
                ->descriptionIcon($revenueGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($revenueGrowth >= 0 ? 'success' : 'danger')
                ->chart($this->getRevenueChartData()),

            Stat::make('Total Invoices', number_format($totalInvoices))
                ->description($paidInvoices . ' paid, ' . $pendingInvoices . ' pending')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info')
                ->chart($this->getInvoiceChartData()),

            Stat::make('Active Clients', number_format($activeClients))
                ->description('Out of ' . number_format($totalClients) . ' total clients')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart($this->getClientChartData()),

            Stat::make('Monthly Payments', '$' . number_format($monthlyPayments, 2))
                ->description('Total: $' . number_format($totalPayments, 2))
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart($this->getPaymentChartData()),

            Stat::make('Payment Rate', number_format($totalInvoices > 0 ? ($paidInvoices / $totalInvoices) * 100 : 0, 1) . '%')
                ->description($overdueInvoices . ' overdue invoices')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($overdueInvoices > 0 ? 'warning' : 'success'),

            Stat::make('Average Invoice', '$' . number_format($totalInvoices > 0 ? $currentRevenue / $paidInvoices : 0, 2))
                ->description('Current month average')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('gray'),
        ];
    }

    private function getRevenueChartData(): array
    {
        return Invoice::where('status', 'paid')
            ->selectRaw('DATE(created_at) as date, SUM(total) as total')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('total')
            ->toArray();
    }

    private function getInvoiceChartData(): array
    {
        return Invoice::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count')
            ->toArray();
    }

    private function getClientChartData(): array
    {
        return Client::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count')
            ->toArray();
    }

    private function getPaymentChartData(): array
    {
        return Payment::selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('total')
            ->toArray();
    }
}
