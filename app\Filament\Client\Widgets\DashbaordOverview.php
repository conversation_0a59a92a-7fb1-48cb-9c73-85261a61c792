<?php

namespace App\Filament\Client\Widgets;

use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DashbaordOverview extends BaseWidget
{
    protected static string $view = 'client.widgets.dashboard';

    public static function canView(): bool
    {
        return auth()->user()->hasRole('client');
    }
    protected function getViewData(): array
    {
        try {
            $user = getLogInUser();

            // Add null check for user and client
            if (!$user || !$user->client) {
                throw new \Exception('User or client not found');
            }

            $invoices = Invoice::where('client_id', $user->client->id)
                ->where('status', '!=', Invoice::DRAFT)
                ->get();

            $totalInvoices = $invoices->count();
            $paidInvoices = $invoices->where('status', Invoice::PAID)->count();
            $unpaidInvoices = $invoices->where('status', Invoice::UNPAID)->count();
            $partiallyPaidInvoices = $invoices->where('status', Invoice::PARTIALLY)->count();
            $overdueInvoices = $invoices->where('status', Invoice::OVERDUE)->count();
            $processingInvoices = $invoices->where('status', Invoice::PROCESSING)->count();

            // Calculate total amounts for better insights
            $totalAmount = $invoices->sum('final_amount');
            $paidAmount = $invoices->where('status', Invoice::PAID)->sum('final_amount');
            $unpaidAmount = $invoices->whereIn('status', [Invoice::UNPAID, Invoice::PARTIALLY, Invoice::OVERDUE])->sum('final_amount');

            return [
                'totalInvoices' => formatTotalAmount($totalInvoices),
                'paidInvoices' => formatTotalAmount($paidInvoices),
                'unpaidInvoices' => formatTotalAmount($unpaidInvoices),
                'partiallyPaidInvoices' => formatTotalAmount($partiallyPaidInvoices),
                'overdueInvoices' => formatTotalAmount($overdueInvoices),
                'processingInvoices' => formatTotalAmount($processingInvoices),
                'totalAmount' => formatTotalAmount($totalAmount),
                'paidAmount' => formatTotalAmount($paidAmount),
                'unpaidAmount' => formatTotalAmount($unpaidAmount),
            ];
        } catch (\Exception $e) {
            \Log::error('Client DashboardOverview widget error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'totalInvoices' => formatTotalAmount(0),
                'paidInvoices' => formatTotalAmount(0),
                'unpaidInvoices' => formatTotalAmount(0),
                'partiallyPaidInvoices' => formatTotalAmount(0),
                'overdueInvoices' => formatTotalAmount(0),
                'processingInvoices' => formatTotalAmount(0),
                'totalAmount' => formatTotalAmount(0),
                'paidAmount' => formatTotalAmount(0),
                'unpaidAmount' => formatTotalAmount(0),
            ];
        }
    }
}
