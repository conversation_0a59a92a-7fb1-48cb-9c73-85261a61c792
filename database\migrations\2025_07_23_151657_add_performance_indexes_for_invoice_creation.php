<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Add performance indexes for invoice creation optimization
     */
    public function up(): void
    {
        // Use raw SQL to add indexes safely
        try {
            // Users table indexes
            DB::statement('CREATE INDEX IF NOT EXISTS idx_users_names ON users (first_name, last_name)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)');

            // Clients table indexes
            DB::statement('CREATE INDEX IF NOT EXISTS idx_clients_company ON clients (company_name)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_clients_user ON clients (user_id)');

            // Products table indexes
            DB::statement('CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)');

            // Taxes table indexes
            DB::statement('CREATE INDEX IF NOT EXISTS idx_taxes_name ON taxes (name)');

            // Invoices table indexes
            DB::statement('CREATE INDEX IF NOT EXISTS idx_invoices_client_status ON invoices (client_id, status)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices (invoice_date)');

            // Invoice items table indexes
            DB::statement('CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON invoice_items (invoice_id)');
            DB::statement('CREATE INDEX IF NOT EXISTS idx_invoice_items_product ON invoice_items (product_id)');

        } catch (\Exception $e) {
            // Ignore duplicate key errors - indexes may already exist
            if (!str_contains($e->getMessage(), 'Duplicate key name')) {
                throw $e;
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['first_name', 'last_name']);
            $table->dropIndex(['email']);
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex(['company_name']);
            $table->dropIndex(['user_id']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['name']);
        });

        Schema::table('taxes', function (Blueprint $table) {
            $table->dropIndex(['name']);
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['client_id', 'status']);
            $table->dropIndex(['invoice_date']);
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropIndex(['invoice_id']);
            $table->dropIndex(['product_id']);
        });
    }
};
