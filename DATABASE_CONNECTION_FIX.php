<?php
/**
 * 🔧 DATABASE CONNECTION FIX SCRIPT
 * 
 * This script diagnoses and fixes MySQL driver issues
 * Usage: php DATABASE_CONNECTION_FIX.php
 */

echo "🔧 DATABASE CONNECTION FIX SCRIPT\n";
echo str_repeat("=", 50) . "\n\n";

// Bootstrap Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ Laravel application: BOOTSTRAPPED\n";
} catch (Exception $e) {
    echo "❌ Laravel bootstrap failed: " . $e->getMessage() . "\n";
    exit(1);
}

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Providers\DatabaseServiceProvider;

echo "\n🔍 STEP 1: DIAGNOSE CURRENT STATE\n";
echo str_repeat("-", 40) . "\n";

// Check PHP extensions
$pdo_loaded = extension_loaded('pdo');
$pdo_mysql_loaded = extension_loaded('pdo_mysql');
$mysql_drivers = $pdo_loaded ? PDO::getAvailableDrivers() : [];

echo "   PDO Extension: " . ($pdo_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "\n";
echo "   PDO MySQL: " . ($pdo_mysql_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "\n";
echo "   Available Drivers: " . implode(', ', $mysql_drivers) . "\n";

// Check current database configuration
$currentConnection = Config::get('database.default');
echo "   Current Connection: {$currentConnection}\n";

echo "\n🔧 STEP 2: ATTEMPT CONNECTION FIXES\n";
echo str_repeat("-", 40) . "\n";

$connectionFixed = false;

// Try 1: Direct MySQL connection test
if ($pdo_mysql_loaded && in_array('mysql', $mysql_drivers)) {
    echo "   Attempting direct MySQL connection...\n";
    try {
        $host = env('DB_HOST', '127.0.0.1');
        $dbname = env('DB_DATABASE', 'invoicemod');
        $username = env('DB_USERNAME', 'root');
        $password = env('DB_PASSWORD', '');
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "   ✅ Direct MySQL connection: SUCCESS\n";
        
        // Test Laravel connection
        DB::connection('mysql')->getPdo();
        echo "   ✅ Laravel MySQL connection: SUCCESS\n";
        $connectionFixed = true;
        
    } catch (Exception $e) {
        echo "   ❌ MySQL connection failed: " . $e->getMessage() . "\n";
    }
}

// Try 2: Force MySQL connection through service provider
if (!$connectionFixed && $pdo_mysql_loaded) {
    echo "   Attempting to force MySQL connection...\n";
    try {
        if (DatabaseServiceProvider::forceMysqlConnection()) {
            echo "   ✅ Forced MySQL connection: SUCCESS\n";
            $connectionFixed = true;
        } else {
            echo "   ❌ Force MySQL connection: FAILED\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Force MySQL error: " . $e->getMessage() . "\n";
    }
}

// Try 3: Fallback to SQLite
if (!$connectionFixed) {
    echo "   Attempting SQLite fallback...\n";
    try {
        if (DatabaseServiceProvider::migrateToSqlite()) {
            echo "   ✅ SQLite fallback: SUCCESS\n";
            $connectionFixed = true;
        } else {
            echo "   ❌ SQLite fallback: FAILED\n";
        }
    } catch (Exception $e) {
        echo "   ❌ SQLite fallback error: " . $e->getMessage() . "\n";
    }
}

echo "\n🧪 STEP 3: VALIDATE CONNECTION\n";
echo str_repeat("-", 40) . "\n";

if ($connectionFixed) {
    try {
        // Test basic query
        $result = DB::select('SELECT 1 as test');
        echo "   ✅ Basic query test: SUCCESS\n";
        
        // Test settings table (the one causing the original error)
        $settings = DB::table('settings')->where('key', 'app_name')->first();
        if ($settings) {
            echo "   ✅ Settings table access: SUCCESS\n";
            echo "   📊 App name: " . ($settings->value ?? 'Not set') . "\n";
        } else {
            echo "   ⚠️  Settings table: Empty or missing app_name\n";
        }
        
        // Test other core tables
        $userCount = DB::table('users')->count();
        $invoiceCount = DB::table('invoices')->count();
        $clientCount = DB::table('clients')->count();
        
        echo "   📊 Database stats: {$userCount} users, {$invoiceCount} invoices, {$clientCount} clients\n";
        
    } catch (Exception $e) {
        echo "   ❌ Connection validation failed: " . $e->getMessage() . "\n";
        $connectionFixed = false;
    }
}

echo "\n🔧 STEP 4: APPLY PERMANENT FIXES\n";
echo str_repeat("-", 40) . "\n";

if ($connectionFixed) {
    try {
        // Clear all caches
        echo "   Clearing application caches...\n";
        \Illuminate\Support\Facades\Artisan::call('config:clear');
        \Illuminate\Support\Facades\Artisan::call('cache:clear');
        \Illuminate\Support\Facades\Artisan::call('route:clear');
        \Illuminate\Support\Facades\Artisan::call('view:clear');
        echo "   ✅ Caches cleared\n";
        
        // Optimize configuration
        echo "   Optimizing configuration...\n";
        \Illuminate\Support\Facades\Artisan::call('config:cache');
        echo "   ✅ Configuration optimized\n";
        
        // Test the web route
        echo "   Testing web accessibility...\n";
        
        // Create a simple test to verify web access
        $testContent = "<?php\n";
        $testContent .= "// Database connection test for web context\n";
        $testContent .= "try {\n";
        $testContent .= "    require_once '../vendor/autoload.php';\n";
        $testContent .= "    \$app = require_once '../bootstrap/app.php';\n";
        $testContent .= "    \$kernel = \$app->make(Illuminate\\Contracts\\Console\\Kernel::class);\n";
        $testContent .= "    \$kernel->bootstrap();\n";
        $testContent .= "    \n";
        $testContent .= "    \$connection = \\Illuminate\\Support\\Facades\\DB::connection();\n";
        $testContent .= "    \$pdo = \$connection->getPdo();\n";
        $testContent .= "    \n";
        $testContent .= "    echo json_encode(['status' => 'success', 'connection' => get_class(\$pdo)]);\n";
        $testContent .= "} catch (Exception \$e) {\n";
        $testContent .= "    echo json_encode(['status' => 'error', 'message' => \$e->getMessage()]);\n";
        $testContent .= "}\n";
        
        file_put_contents('public/db_test.php', $testContent);
        echo "   ✅ Web test endpoint created: /db_test.php\n";
        
    } catch (Exception $e) {
        echo "   ❌ Permanent fix application failed: " . $e->getMessage() . "\n";
    }
}

echo "\n📊 FINAL STATUS REPORT\n";
echo str_repeat("=", 50) . "\n";

if ($connectionFixed) {
    echo "🎉 DATABASE CONNECTION: FIXED!\n";
    echo "✅ Status: OPERATIONAL\n";
    echo "🔗 Connection: " . Config::get('database.default') . "\n";
    
    $status = DatabaseServiceProvider::getDatabaseStatus();
    echo "📋 Connection Details:\n";
    foreach ($status['connections_tested'] as $conn => $result) {
        $icon = strpos($result, 'success') !== false ? '✅' : '❌';
        echo "   {$icon} {$conn}: {$result}\n";
    }
    
    echo "\n🌐 NEXT STEPS:\n";
    echo "   1. Access admin panel: http://127.0.0.1:8000/admin\n";
    echo "   2. Test database connection: http://127.0.0.1:8000/db_test.php\n";
    echo "   3. Check diagnostic info: http://127.0.0.1:8000/phpinfo_diagnostic.php\n";
    echo "   4. Verify all features are working\n";
    
} else {
    echo "❌ DATABASE CONNECTION: FAILED\n";
    echo "🔧 MANUAL INTERVENTION REQUIRED\n";
    
    echo "\n🛠️  MANUAL FIX STEPS:\n";
    echo "   1. Check XAMPP MySQL service is running\n";
    echo "   2. Verify php.ini has extension=pdo_mysql enabled\n";
    echo "   3. Restart Apache web server\n";
    echo "   4. Check database exists: invoicemod\n";
    echo "   5. Verify database credentials in .env file\n";
}

echo "\n✅ Database connection fix script completed!\n";
