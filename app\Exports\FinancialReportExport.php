<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class FinancialReportExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $financialData;
    protected string $startDate;
    protected string $endDate;

    public function __construct(array $financialData, string $startDate, string $endDate)
    {
        $this->financialData = $financialData;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function array(): array
    {
        return [
            ['Metric', 'Value'],
            ['Total Invoices', $this->financialData['total_invoices'] ?? 0],
            ['Total Invoice Amount', number_format($this->financialData['total_invoice_amount'] ?? 0, 2)],
            ['Total Payments Received', number_format($this->financialData['total_payments_received'] ?? 0, 2)],
            ['Outstanding Amount', number_format($this->financialData['outstanding_amount'] ?? 0, 2)],
            ['Paid Invoices', $this->financialData['paid_invoices'] ?? 0],
            ['Unpaid Invoices', $this->financialData['unpaid_invoices'] ?? 0],
            ['Partially Paid Invoices', $this->financialData['partially_paid_invoices'] ?? 0],
            ['Overdue Invoices', $this->financialData['overdue_invoices'] ?? 0],
            ['Processing Invoices', $this->financialData['processing_invoices'] ?? 0],
            ['Average Invoice Value', number_format($this->financialData['average_invoice_value'] ?? 0, 2)],
            ['Payment Success Rate', number_format($this->financialData['payment_success_rate'] ?? 0, 2) . '%'],
            ['Collection Efficiency', number_format($this->financialData['collection_efficiency'] ?? 0, 2) . '%'],
        ];
    }

    public function headings(): array
    {
        return ['Financial Report - ' . $this->startDate . ' to ' . $this->endDate];
    }

    public function title(): string
    {
        return 'Financial Report';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 14,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
            2 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '70AD47'],
                ],
            ],
        ];
    }
}
