<x-filament-widgets::widget>
    <x-filament::section>
        <!-- Ultra-Modern Activity Feed Container with Glassmorphism -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Revolutionary Activity Feed Design -->
            <div class="lg:col-span-2">
                <!-- Glassmorphism Header with Floating Effect -->
                <div class="relative mb-8 p-6 bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-3xl border border-white/20 dark:border-gray-700/30 shadow-2xl">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl"></div>
                    <div class="relative flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <!-- Animated Icon Container -->
                            <div class="relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur-lg opacity-75 animate-pulse"></div>
                                <div class="relative p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-xl">
                                    <x-heroicon-o-clock class="h-7 w-7 text-white" />
                                </div>
                            </div>
                            <div>
                                <h3 class="text-2xl font-black bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                                    Recent Activity
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">Real-time system events & updates</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <!-- Enhanced Live Badge -->
                            <div class="relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full blur-sm opacity-75 animate-pulse"></div>
                                <span class="relative inline-flex items-center px-4 py-2 rounded-full text-xs font-bold bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/40 dark:to-emerald-900/40 text-green-800 dark:text-green-300 border border-green-200/50 dark:border-green-700/50">
                                    <span class="w-2.5 h-2.5 bg-green-500 rounded-full mr-2 animate-ping"></span>
                                    <span class="w-2.5 h-2.5 bg-green-500 rounded-full mr-2 absolute animate-pulse"></span>
                                    LIVE
                                </span>
                            </div>
                            <!-- Modern Refresh Button -->
                            <button class="group relative p-3 text-gray-400 hover:text-white transition-all duration-300 rounded-2xl hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 hover:shadow-lg hover:scale-105">
                                <x-heroicon-o-arrow-path class="h-5 w-5 group-hover:rotate-180 transition-transform duration-500" />
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Revolutionary Activity Timeline with Glassmorphism -->
                <div class="space-y-6">
                    @foreach($this->getActivities() as $index => $activity)
                        <div class="group relative">
                            <!-- Ultra-Modern Glassmorphism Activity Card -->
                            <div class="relative overflow-hidden">
                                <!-- Glassmorphism Background -->
                                <div class="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30"></div>

                                <!-- Gradient Overlay -->
                                <div class="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-gray-100/20 dark:from-gray-800/20 dark:to-gray-900/20 rounded-3xl"></div>

                                <!-- Hover Glow Effect -->
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-purple-500/0 to-pink-500/0 group-hover:from-blue-500/10 group-hover:via-purple-500/10 group-hover:to-pink-500/10 rounded-3xl transition-all duration-500"></div>

                                <!-- Card Content -->
                                <div class="relative p-6 group-hover:scale-[1.02] transition-transform duration-300">
                                    <div class="flex items-start space-x-5">
                                        <!-- Revolutionary Icon Design -->
                                        <div class="flex-shrink-0">
                                            <div class="relative">
                                                <!-- Animated Glow Ring -->
                                                <div class="absolute inset-0 rounded-3xl
                                                    {{ $activity['color'] === 'success' ? 'bg-gradient-to-r from-green-400 to-emerald-500' :
                                                       ($activity['color'] === 'info' ? 'bg-gradient-to-r from-blue-400 to-cyan-500' :
                                                       ($activity['color'] === 'danger' ? 'bg-gradient-to-r from-red-400 to-pink-500' :
                                                       ($activity['color'] === 'warning' ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gradient-to-r from-gray-400 to-slate-500'))) }}
                                                    blur-lg opacity-75 group-hover:opacity-100 group-hover:scale-110 transition-all duration-500"></div>

                                                <!-- Main Icon Container -->
                                                <div class="relative h-14 w-14 rounded-3xl flex items-center justify-center shadow-2xl
                                                    {{ $activity['color'] === 'success' ? 'bg-gradient-to-br from-green-400 via-green-500 to-emerald-600' :
                                                       ($activity['color'] === 'info' ? 'bg-gradient-to-br from-blue-400 via-blue-500 to-cyan-600' :
                                                       ($activity['color'] === 'danger' ? 'bg-gradient-to-br from-red-400 via-red-500 to-pink-600' :
                                                       ($activity['color'] === 'warning' ? 'bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-600' : 'bg-gradient-to-br from-gray-400 via-gray-500 to-slate-600'))) }}">
                                                    <x-dynamic-component :component="$activity['icon']" class="h-7 w-7 text-white drop-shadow-lg" />
                                                </div>

                                                <!-- Floating Type Badge -->
                                                <div class="absolute -bottom-2 -right-2 h-6 w-6 rounded-full border-3 border-white dark:border-gray-900 shadow-lg
                                                    {{ $activity['type'] === 'invoice' ? 'bg-gradient-to-br from-blue-400 to-blue-600' :
                                                       ($activity['type'] === 'payment' ? 'bg-gradient-to-br from-green-400 to-green-600' :
                                                       ($activity['type'] === 'client' ? 'bg-gradient-to-br from-purple-400 to-purple-600' : 'bg-gradient-to-br from-gray-400 to-gray-600')) }}">
                                                    <div class="h-2 w-2 bg-white rounded-full mx-auto mt-2"></div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Revolutionary Content Design -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <!-- Ultra-Modern Title with Gradient Text -->
                                                    <h4 class="text-lg font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 dark:from-white dark:via-gray-100 dark:to-gray-200 bg-clip-text text-transparent group-hover:from-blue-600 group-hover:via-purple-600 group-hover:to-pink-600 transition-all duration-500">
                                                        <a href="{{ $activity['url'] ?? '#' }}" class="hover:drop-shadow-lg transition-all duration-300">
                                                            {{ $activity['title'] }}
                                                        </a>
                                                    </h4>

                                                    <!-- Enhanced Description with Better Typography -->
                                                    <p class="mt-3 text-sm text-gray-600 dark:text-gray-300 leading-relaxed font-medium">
                                                        {{ $activity['description'] }}
                                                    </p>

                                                    <!-- Modern Metadata Row -->
                                                    <div class="mt-4 flex items-center space-x-4">
                                                        <!-- Glassmorphism Timestamp -->
                                                        <div class="flex items-center space-x-2 px-3 py-1.5 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-full border border-white/30 dark:border-gray-700/30">
                                                            <x-heroicon-o-clock class="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                                                            <time datetime="{{ $activity['time']->toISOString() }}" class="text-xs font-semibold text-gray-600 dark:text-gray-300">
                                                                {{ $activity['time']->diffForHumans() }}
                                                            </time>
                                                        </div>

                                                        <!-- Ultra-Modern Type Badge -->
                                                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold shadow-lg
                                                            {{ $activity['type'] === 'invoice' ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white' :
                                                               ($activity['type'] === 'payment' ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white' :
                                                               ($activity['type'] === 'client' ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' : 'bg-gradient-to-r from-gray-500 to-slate-500 text-white')) }}">
                                                            <span class="w-1.5 h-1.5 bg-white/80 rounded-full mr-2"></span>
                                                            {{ strtoupper($activity['type']) }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Floating Action Button -->
                                                <div class="flex-shrink-0 ml-6">
                                                    <a href="{{ $activity['url'] ?? '#' }}" class="group/btn relative inline-flex items-center justify-center w-12 h-12 bg-white/60 dark:bg-gray-800/60 backdrop-blur-xl rounded-2xl border border-white/30 dark:border-gray-700/30 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 hover:border-transparent transition-all duration-300 hover:scale-110 hover:shadow-2xl">
                                                        <x-heroicon-o-arrow-top-right-on-square class="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover/btn:text-white transition-colors duration-300" />
                                                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/0 to-purple-600/0 group-hover/btn:from-blue-500/20 group-hover/btn:to-purple-600/20 rounded-2xl transition-all duration-300"></div>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Revolutionary View All Button -->
                <div class="mt-10 text-center">
                    <div class="relative inline-block">
                        <!-- Animated Background Glow -->
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-3xl blur-xl opacity-75 animate-pulse"></div>

                        <!-- Main Button -->
                        <a href="#" class="relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 text-white text-sm font-bold rounded-3xl hover:from-blue-600 hover:via-purple-700 hover:to-pink-700 transition-all duration-500 shadow-2xl hover:shadow-3xl transform hover:scale-105 hover:-translate-y-1 border border-white/20">
                            <!-- Button Content -->
                            <div class="flex items-center space-x-3">
                                <div class="p-1 bg-white/20 rounded-full">
                                    <x-heroicon-o-eye class="h-4 w-4" />
                                </div>
                                <span class="font-black tracking-wide">VIEW ALL ACTIVITY</span>
                                <div class="p-1 bg-white/20 rounded-full">
                                    <x-heroicon-o-arrow-right class="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                                </div>
                            </div>

                            <!-- Shimmer Effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 rounded-3xl"></div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Revolutionary Glassmorphism Stats Sidebar -->
            <div class="space-y-8">
                <div>
                    <!-- Ultra-Modern Floating Header -->
                    <div class="relative mb-8 p-6 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30 shadow-2xl">
                        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl"></div>
                        <div class="relative flex items-center space-x-4">
                            <!-- Animated Chart Icon -->
                            <div class="relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl blur-lg opacity-75 animate-pulse"></div>
                                <div class="relative p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-xl">
                                    <x-heroicon-o-chart-bar class="h-6 w-6 text-white" />
                                </div>
                            </div>
                            <div>
                                <h3 class="text-xl font-black bg-gradient-to-r from-gray-900 to-indigo-600 dark:from-white dark:to-indigo-300 bg-clip-text text-transparent">
                                    Today's Metrics
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 font-semibold">Live performance data</p>
                            </div>
                        </div>
                    </div>

                    @php $stats = $this->getQuickStats(); @endphp

                    <div class="space-y-6">
                        <!-- Revolutionary Glassmorphism Stat Card 1 -->
                        <div class="group relative overflow-hidden">
                            <!-- Glassmorphism Background -->
                            <div class="absolute inset-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30"></div>

                            <!-- Animated Gradient Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 rounded-3xl group-hover:from-blue-500/20 group-hover:via-indigo-500/20 group-hover:to-purple-500/20 transition-all duration-500"></div>

                            <!-- Content -->
                            <div class="relative p-6 group-hover:scale-105 transition-transform duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <!-- Ultra-Modern Icon -->
                                        <div class="relative">
                                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 group-hover:scale-110 transition-all duration-500"></div>
                                            <div class="relative p-3 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-2xl shadow-2xl">
                                                <x-heroicon-o-document-text class="h-6 w-6 text-white drop-shadow-lg" />
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-xs font-black text-blue-700 dark:text-blue-300 uppercase tracking-widest mb-1">INVOICES</p>
                                            <p class="text-3xl font-black bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 bg-clip-text text-transparent">{{ $stats['today_invoices'] }}</p>
                                        </div>
                                    </div>
                                    <!-- Floating Badge -->
                                    <div class="px-3 py-1 bg-blue-500/20 backdrop-blur-sm rounded-full border border-blue-300/30">
                                        <span class="text-xs font-bold text-blue-700 dark:text-blue-300">TODAY</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Revolutionary Glassmorphism Stat Card 2 -->
                        <div class="group relative overflow-hidden">
                            <div class="absolute inset-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30"></div>
                            <div class="absolute inset-0 bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 rounded-3xl group-hover:from-green-500/20 group-hover:via-emerald-500/20 group-hover:to-teal-500/20 transition-all duration-500"></div>

                            <div class="relative p-6 group-hover:scale-105 transition-transform duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="relative">
                                            <div class="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 group-hover:scale-110 transition-all duration-500"></div>
                                            <div class="relative p-3 bg-gradient-to-br from-green-500 via-emerald-500 to-teal-600 rounded-2xl shadow-2xl">
                                                <x-heroicon-o-banknotes class="h-6 w-6 text-white drop-shadow-lg" />
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-xs font-black text-green-700 dark:text-green-300 uppercase tracking-widest mb-1">PAYMENTS</p>
                                            <p class="text-3xl font-black bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 bg-clip-text text-transparent">{{ $stats['today_payments'] }}</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1 bg-green-500/20 backdrop-blur-sm rounded-full border border-green-300/30">
                                        <span class="text-xs font-bold text-green-700 dark:text-green-300">RECEIVED</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Revolutionary Glassmorphism Stat Card 3 -->
                        <div class="group relative overflow-hidden">
                            <div class="absolute inset-0 bg-white/50 dark:bg-gray-900/50 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30"></div>
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-violet-500/10 to-pink-500/10 rounded-3xl group-hover:from-purple-500/20 group-hover:via-violet-500/20 group-hover:to-pink-500/20 transition-all duration-500"></div>

                            <div class="relative p-6 group-hover:scale-105 transition-transform duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="relative">
                                            <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-violet-600 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 group-hover:scale-110 transition-all duration-500"></div>
                                            <div class="relative p-3 bg-gradient-to-br from-purple-500 via-violet-500 to-pink-600 rounded-2xl shadow-2xl">
                                                <x-heroicon-o-currency-dollar class="h-6 w-6 text-white drop-shadow-lg" />
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-xs font-black text-purple-700 dark:text-purple-300 uppercase tracking-widest mb-1">REVENUE</p>
                                            <p class="text-3xl font-black bg-gradient-to-r from-purple-600 to-violet-600 dark:from-purple-400 dark:to-violet-400 bg-clip-text text-transparent">${{ number_format($stats['today_revenue'], 2) }}</p>
                                        </div>
                                    </div>
                                    <div class="px-3 py-1 bg-purple-500/20 backdrop-blur-sm rounded-full border border-purple-300/30">
                                        <span class="text-xs font-bold text-purple-700 dark:text-purple-300">TODAY</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts Section -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Alerts</h3>
                    
                    <div class="space-y-3">
                        @if($stats['overdue_invoices'] > 0)
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-exclamation-triangle class="h-5 w-5 text-red-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                            Overdue Invoices
                                        </h3>
                                        <div class="mt-1 text-sm text-red-700 dark:text-red-300">
                                            {{ $stats['overdue_invoices'] }} invoices are overdue
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($stats['pending_invoices'] > 5)
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-clock class="h-5 w-5 text-yellow-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                            Pending Invoices
                                        </h3>
                                        <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                            {{ $stats['pending_invoices'] }} invoices awaiting payment
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($stats['overdue_invoices'] == 0 && $stats['pending_invoices'] <= 5)
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-check-circle class="h-5 w-5 text-green-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                            All Good!
                                        </h3>
                                        <div class="mt-1 text-sm text-green-700 dark:text-green-300">
                                            No urgent issues to address
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Actions -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                    
                    <div class="space-y-2">
                        <a href="{{ route('filament.admin.resources.invoices.create') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-plus class="h-4 w-4 mr-2" />
                            Create Invoice
                        </a>
                        
                        <a href="{{ route('filament.admin.resources.clients.create') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-user-plus class="h-4 w-4 mr-2" />
                            Add Client
                        </a>
                        
                        <a href="{{ route('filament.admin.pages.advanced-reporting') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-chart-bar class="h-4 w-4 mr-2" />
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
