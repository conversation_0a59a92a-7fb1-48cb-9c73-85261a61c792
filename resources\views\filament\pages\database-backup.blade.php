<x-filament-panels::page>
    <!-- Ultra-Modern Database Backup Interface -->
    <div class="space-y-8">
        <!-- Revolutionary Hero Section -->
        <div class="relative overflow-hidden">
            <!-- Animated Background -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-green-500/10 animate-gradient-x"></div>
            
            <!-- Glassmorphism Container -->
            <div class="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30 shadow-2xl p-8">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4">
                        <!-- Animated Icon -->
                        <div class="relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-green-600 rounded-2xl blur-lg opacity-75 animate-pulse"></div>
                            <div class="relative p-4 bg-gradient-to-br from-blue-500 to-green-600 rounded-2xl shadow-xl">
                                <x-heroicon-o-archive-box class="h-8 w-8 text-white" />
                            </div>
                        </div>
                        <div>
                            <h1 class="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 dark:from-blue-400 dark:via-purple-400 dark:to-green-400 bg-clip-text text-transparent">
                                Database Backup System
                            </h1>
                            <p class="text-lg text-gray-600 dark:text-gray-300 font-semibold">Secure backup & export management</p>
                        </div>
                    </div>
                    
                    <!-- Status Indicator -->
                    <div class="flex items-center space-x-3">
                        <div class="px-4 py-2 bg-green-500/20 backdrop-blur-sm rounded-full border border-green-300/30">
                            <span class="text-sm font-bold text-green-700 dark:text-green-300 flex items-center">
                                <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                                SYSTEM READY
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Backup Configuration Form -->
                <div class="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-white/30 dark:border-gray-700/30 p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <x-heroicon-o-cog-6-tooth class="h-5 w-5 mr-2" />
                        Backup Configuration
                    </h3>
                    {{ $this->form }}
                </div>
            </div>
        </div>

        <!-- Revolutionary Backup List -->
        <div class="relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-orange-500/10"></div>
            
            <div class="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30 shadow-2xl p-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-black bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                        Available Backups
                    </h2>
                    <div class="px-3 py-1 bg-purple-500/20 backdrop-blur-sm rounded-full border border-purple-300/30">
                        <span class="text-xs font-bold text-purple-700 dark:text-purple-300">{{ count($this->getBackups()) }} BACKUPS</span>
                    </div>
                </div>

                @if(count($this->getBackups()) > 0)
                    <div class="grid gap-4">
                        @foreach($this->getBackups() as $backup)
                            <div class="group relative overflow-hidden">
                                <!-- Glassmorphism Background -->
                                <div class="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-2xl border border-white/30 dark:border-gray-700/30"></div>
                                
                                <!-- Hover Gradient -->
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-purple-500/0 to-pink-500/0 group-hover:from-blue-500/10 group-hover:via-purple-500/10 group-hover:to-pink-500/10 rounded-2xl transition-all duration-500"></div>
                                
                                <!-- Content -->
                                <div class="relative p-6 group-hover:scale-[1.02] transition-transform duration-300">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <!-- File Icon -->
                                            <div class="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                                                <x-heroicon-o-archive-box-arrow-down class="h-6 w-6 text-white" />
                                            </div>
                                            
                                            <!-- Backup Info -->
                                            <div>
                                                <h3 class="text-lg font-bold text-gray-900 dark:text-white">{{ $backup['name'] }}</h3>
                                                <div class="flex items-center space-x-4 mt-1">
                                                    <span class="text-sm text-gray-600 dark:text-gray-300">
                                                        <x-heroicon-o-calendar class="h-4 w-4 inline mr-1" />
                                                        {{ $backup['created_at']->format('M d, Y H:i') }}
                                                    </span>
                                                    <span class="text-sm text-gray-600 dark:text-gray-300">
                                                        <x-heroicon-o-document class="h-4 w-4 inline mr-1" />
                                                        {{ number_format($backup['size'] / 1024 / 1024, 2) }} MB
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Action Buttons -->
                                        <div class="flex items-center space-x-3">
                                            <!-- Download Button -->
                                            <button 
                                                wire:click="downloadBackup('{{ $backup['file'] }}')"
                                                class="group/btn relative inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-sm font-bold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                                                <x-heroicon-o-arrow-down-tray class="h-4 w-4 mr-2" />
                                                Download
                                            </button>
                                            
                                            <!-- Delete Button -->
                                            <button 
                                                wire:click="deleteBackup('{{ $backup['file'] }}')"
                                                wire:confirm="Are you sure you want to delete this backup? This action cannot be undone."
                                                class="group/btn relative inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white text-sm font-bold rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                                                <x-heroicon-o-trash class="h-4 w-4 mr-2" />
                                                Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <!-- Empty State -->
                    <div class="text-center py-12">
                        <div class="relative inline-block">
                            <div class="absolute inset-0 bg-gradient-to-r from-gray-400 to-gray-600 rounded-2xl blur-lg opacity-50"></div>
                            <div class="relative p-6 bg-gradient-to-br from-gray-400 to-gray-600 rounded-2xl shadow-xl">
                                <x-heroicon-o-archive-box-x-mark class="h-12 w-12 text-white mx-auto" />
                            </div>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mt-4">No Backups Available</h3>
                        <p class="text-gray-600 dark:text-gray-300 mt-2">Create your first backup to get started</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions Panel -->
        <div class="relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-orange-500/10 via-red-500/10 to-pink-500/10"></div>
            
            <div class="relative bg-white/70 dark:bg-gray-900/70 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/30 shadow-2xl p-8">
                <h2 class="text-2xl font-black bg-gradient-to-r from-orange-600 to-red-600 dark:from-orange-400 dark:to-red-400 bg-clip-text text-transparent mb-6">
                    Quick Actions
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Database Only -->
                    <div class="group relative overflow-hidden cursor-pointer" wire:click="$set('data.include_database', true); $set('data.include_config', false); $set('data.include_storage', false)">
                        <div class="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-2xl border border-white/30 dark:border-gray-700/30"></div>
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 group-hover:from-blue-500/20 group-hover:to-cyan-500/20 rounded-2xl transition-all duration-500"></div>
                        
                        <div class="relative p-6 text-center">
                            <div class="p-3 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl shadow-lg mx-auto w-fit mb-4">
                                <x-heroicon-o-circle-stack class="h-6 w-6 text-white" />
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Database Only</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">Export database structure and data</p>
                        </div>
                    </div>
                    
                    <!-- Full System -->
                    <div class="group relative overflow-hidden cursor-pointer" wire:click="$set('data.include_database', true); $set('data.include_config', true); $set('data.include_storage', true)">
                        <div class="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-2xl border border-white/30 dark:border-gray-700/30"></div>
                        <div class="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 group-hover:from-green-500/20 group-hover:to-emerald-500/20 rounded-2xl transition-all duration-500"></div>
                        
                        <div class="relative p-6 text-center">
                            <div class="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg mx-auto w-fit mb-4">
                                <x-heroicon-o-server-stack class="h-6 w-6 text-white" />
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Full System</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">Complete system backup with all files</p>
                        </div>
                    </div>
                    
                    <!-- Configuration -->
                    <div class="group relative overflow-hidden cursor-pointer" wire:click="$set('data.include_database', false); $set('data.include_config', true); $set('data.include_storage', false)">
                        <div class="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-2xl border border-white/30 dark:border-gray-700/30"></div>
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 group-hover:from-purple-500/20 group-hover:to-pink-500/20 rounded-2xl transition-all duration-500"></div>
                        
                        <div class="relative p-6 text-center">
                            <div class="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg mx-auto w-fit mb-4">
                                <x-heroicon-o-cog-6-tooth class="h-6 w-6 text-white" />
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Configuration</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">Export settings and configuration files</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Auto-refresh on backup events -->
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('backup-created', () => {
                setTimeout(() => {
                    Livewire.dispatch('$refresh');
                }, 1000);
            });
            
            Livewire.on('backup-deleted', () => {
                setTimeout(() => {
                    Livewire.dispatch('$refresh');
                }, 500);
            });
        });
    </script>
</x-filament-panels::page>
