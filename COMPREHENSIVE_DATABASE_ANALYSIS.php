<?php
/**
 * 🔍 COMPREHENSIVE DATABASE ANALYSIS & FIX SCRIPT
 * 
 * This script performs deep analysis of database issues and implements fixes
 * Usage: php COMPREHENSIVE_DATABASE_ANALYSIS.php
 */

echo "🔍 COMPREHENSIVE DATABASE ANALYSIS & FIX\n";
echo str_repeat("=", 60) . "\n\n";

// Bootstrap Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ Laravel application: BOOTSTRAPPED\n";
} catch (Exception $e) {
    echo "❌ Laravel bootstrap failed: " . $e->getMessage() . "\n";
    exit(1);
}

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;

echo "\n🔍 STEP 1: PHP EXTENSION ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

// Check all database-related extensions
$extensions = [
    'pdo' => extension_loaded('pdo'),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'pdo_sqlite' => extension_loaded('pdo_sqlite'),
    'mysqli' => extension_loaded('mysqli'),
    'sqlite3' => extension_loaded('sqlite3'),
];

foreach ($extensions as $ext => $loaded) {
    $status = $loaded ? '✅ LOADED' : '❌ NOT LOADED';
    echo "   {$ext}: {$status}\n";
}

// Check PDO drivers
if ($extensions['pdo']) {
    $drivers = PDO::getAvailableDrivers();
    echo "   PDO Drivers: " . implode(', ', $drivers) . "\n";
} else {
    echo "   ❌ PDO not available - cannot check drivers\n";
}

echo "\n🔍 STEP 2: DATABASE CONFIGURATION ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

// Current configuration
$currentConnection = Config::get('database.default');
echo "   Default Connection: {$currentConnection}\n";

// Test each connection type
$connections = ['mysql', 'sqlite'];
$workingConnections = [];

foreach ($connections as $connection) {
    echo "   Testing {$connection} connection...\n";
    try {
        $config = Config::get("database.connections.{$connection}");
        if (!$config) {
            echo "     ❌ Configuration missing\n";
            continue;
        }
        
        // Special handling for SQLite
        if ($connection === 'sqlite') {
            $dbPath = $config['database'];
            if ($dbPath === ':memory:') {
                echo "     ⚠️  Using in-memory database\n";
            } elseif (!file_exists($dbPath)) {
                echo "     ⚠️  Database file missing: {$dbPath}\n";
                // Try to create the database file
                $dir = dirname($dbPath);
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                }
                touch($dbPath);
                echo "     ✅ Created database file: {$dbPath}\n";
            }
        }
        
        DB::connection($connection)->getPdo();
        echo "     ✅ Connection successful\n";
        $workingConnections[] = $connection;
        
    } catch (Exception $e) {
        echo "     ❌ Connection failed: " . $e->getMessage() . "\n";
    }
}

echo "\n🔍 STEP 3: DATABASE SCHEMA ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

if (!empty($workingConnections)) {
    $primaryConnection = in_array('mysql', $workingConnections) ? 'mysql' : $workingConnections[0];
    Config::set('database.default', $primaryConnection);
    
    echo "   Using connection: {$primaryConnection}\n";
    
    try {
        // Check critical tables
        $criticalTables = [
            'users', 'invoices', 'clients', 'settings', 'roles', 'permissions',
            'audit_logs', 'system_notifications', 'sessions'
        ];
        
        $existingTables = [];
        $missingTables = [];
        
        foreach ($criticalTables as $table) {
            if (Schema::hasTable($table)) {
                $existingTables[] = $table;
                echo "     ✅ {$table}: EXISTS\n";
            } else {
                $missingTables[] = $table;
                echo "     ❌ {$table}: MISSING\n";
            }
        }
        
        // Check for column issues in existing tables
        if (Schema::hasTable('invoices')) {
            $columns = Schema::getColumnListing('invoices');
            echo "   Invoice table columns: " . implode(', ', $columns) . "\n";
            
            // Check for the 'total' column that was causing issues
            if (!in_array('total', $columns)) {
                echo "     ❌ 'total' column missing in invoices table\n";
            } else {
                echo "     ✅ 'total' column exists in invoices table\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ Schema analysis failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ No working database connections available\n";
}

echo "\n🔧 STEP 4: IMPLEMENTING FIXES\n";
echo str_repeat("-", 50) . "\n";

// Fix 1: Ensure SQLite driver is available
if (!$extensions['pdo_sqlite']) {
    echo "   ⚠️  SQLite PDO driver not available - fallback limited\n";
} else {
    echo "   ✅ SQLite PDO driver available\n";
}

// Fix 2: Create robust database configuration
echo "   Creating robust database configuration...\n";

$robustConfig = [
    'default' => env('DB_CONNECTION', 'mysql'),
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'url' => env('DB_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'invoicemod'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => 60,
            ] : [],
        ],
        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DB_URL'),
            'database' => database_path('database.sqlite'),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
            'busy_timeout' => null,
            'journal_mode' => null,
            'synchronous' => null,
        ],
        'sqlite_memory' => [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
            'foreign_key_constraints' => false,
        ],
    ],
];

// Write the robust configuration
file_put_contents('config/database_robust.php', "<?php\n\nreturn " . var_export($robustConfig, true) . ";\n");
echo "   ✅ Robust database configuration created\n";

// Fix 3: Test and set optimal connection
$optimalConnection = null;

if (in_array('mysql', $workingConnections)) {
    try {
        Config::set('database.default', 'mysql');
        DB::connection('mysql')->getPdo();
        $optimalConnection = 'mysql';
        echo "   ✅ MySQL set as primary connection\n";
    } catch (Exception $e) {
        echo "   ⚠️  MySQL connection unstable: " . $e->getMessage() . "\n";
    }
}

if (!$optimalConnection && $extensions['pdo_sqlite']) {
    try {
        // Ensure SQLite database exists
        $sqliteDb = database_path('database.sqlite');
        if (!file_exists($sqliteDb)) {
            touch($sqliteDb);
        }
        
        Config::set('database.default', 'sqlite');
        Config::set('database.connections.sqlite.database', $sqliteDb);
        DB::connection('sqlite')->getPdo();
        $optimalConnection = 'sqlite';
        echo "   ✅ SQLite set as fallback connection\n";
    } catch (Exception $e) {
        echo "   ❌ SQLite fallback failed: " . $e->getMessage() . "\n";
    }
}

echo "\n📊 STEP 5: FINAL VALIDATION\n";
echo str_repeat("-", 50) . "\n";

if ($optimalConnection) {
    try {
        // Test basic operations
        $result = DB::select('SELECT 1 as test');
        echo "   ✅ Basic query test: SUCCESS\n";
        
        // Test settings table if it exists
        if (Schema::hasTable('settings')) {
            $settings = DB::table('settings')->first();
            echo "   ✅ Settings table access: SUCCESS\n";
        }
        
        // Test user count
        if (Schema::hasTable('users')) {
            $userCount = DB::table('users')->count();
            echo "   📊 User count: {$userCount}\n";
        }
        
        echo "   🎉 Database system: OPERATIONAL\n";
        echo "   🔗 Active connection: {$optimalConnection}\n";
        
    } catch (Exception $e) {
        echo "   ❌ Final validation failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ❌ No working database connection available\n";
    echo "   🛠️  Manual intervention required\n";
}

echo "\n✅ Comprehensive database analysis completed!\n";
