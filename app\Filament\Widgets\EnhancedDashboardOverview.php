<?php

namespace App\Filament\Widgets;

use App\Models\Invoice;
use App\Models\Client;
use App\Models\Payment;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EnhancedDashboardOverview extends BaseWidget
{
    protected static ?int $sort = 1;
    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        try {
            $currentMonth = Carbon::now()->startOfMonth();
            $previousMonth = Carbon::now()->subMonth()->startOfMonth();

            // Revenue calculations with proper status constants
            $currentRevenue = Invoice::where('status', Invoice::PAID)
                ->whereBetween('created_at', [$currentMonth, Carbon::now()])
                ->sum('final_amount') ?? 0; // Use final_amount instead of total

            $previousRevenue = Invoice::where('status', Invoice::PAID)
                ->whereBetween('created_at', [$previousMonth, $currentMonth])
                ->sum('final_amount') ?? 0; // Use final_amount instead of total

            // Safe division with additional validation
            $revenueGrowth = ($previousRevenue > 0 && is_numeric($previousRevenue) && is_numeric($currentRevenue))
                ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100
                : 0;

        // Invoice calculations with correct status constants
        $totalInvoices = Invoice::where('status', '!=', Invoice::DRAFT)->count();
        $paidInvoices = Invoice::where('status', Invoice::PAID)->count();
        $pendingInvoices = Invoice::where('status', Invoice::UNPAID)->count();
        $overdueInvoices = Invoice::where('status', Invoice::OVERDUE)->count();

        // Client calculations
        $totalClients = Client::count();
        $activeClients = Client::whereHas('invoices', function ($query) use ($currentMonth) {
            $query->where('created_at', '>=', $currentMonth);
        })->count();

        // Payment calculations
        $totalPayments = Payment::sum('amount');
        $monthlyPayments = Payment::whereBetween('created_at', [$currentMonth, Carbon::now()])
            ->sum('amount');

        return [
            Stat::make('Total Revenue', '$' . number_format($currentRevenue, 2))
                ->description($revenueGrowth >= 0 ? '+' . number_format($revenueGrowth, 1) . '% from last month' : number_format($revenueGrowth, 1) . '% from last month')
                ->descriptionIcon($revenueGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($revenueGrowth >= 0 ? 'success' : 'danger')
                ->chart($this->getRevenueChartData()),

            Stat::make('Total Invoices', number_format($totalInvoices))
                ->description($paidInvoices . ' paid, ' . $pendingInvoices . ' pending')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info')
                ->chart($this->getInvoiceChartData()),

            Stat::make('Active Clients', number_format($activeClients))
                ->description('Out of ' . number_format($totalClients) . ' total clients')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart($this->getClientChartData()),

            Stat::make('Monthly Payments', '$' . number_format($monthlyPayments, 2))
                ->description('Total: $' . number_format($totalPayments, 2))
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart($this->getPaymentChartData()),

            Stat::make('Payment Rate', number_format($totalInvoices > 0 ? ($paidInvoices / $totalInvoices) * 100 : 0, 1) . '%')
                ->description($overdueInvoices . ' overdue invoices')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($overdueInvoices > 0 ? 'warning' : 'success'),

            Stat::make('Average Invoice', '$' . number_format($paidInvoices > 0 ? $currentRevenue / $paidInvoices : 0, 2))
                ->description($paidInvoices > 0 ? 'Based on ' . $paidInvoices . ' paid invoices' : 'No paid invoices yet')
                ->descriptionIcon('heroicon-m-calculator')
                ->color($paidInvoices > 0 ? 'success' : 'gray'),
        ];

        } catch (\Exception $e) {
            \Log::error('EnhancedDashboardOverview error: ' . $e->getMessage());

            // Return safe fallback stats
            return [
                Stat::make('Total Revenue', '$0.00')
                    ->description('Error loading data')
                    ->descriptionIcon('heroicon-m-exclamation-triangle')
                    ->color('danger'),

                Stat::make('Total Invoices', '0')
                    ->description('Error loading data')
                    ->descriptionIcon('heroicon-m-exclamation-triangle')
                    ->color('danger'),

                Stat::make('Active Clients', '0')
                    ->description('Error loading data')
                    ->descriptionIcon('heroicon-m-exclamation-triangle')
                    ->color('danger'),

                Stat::make('Average Invoice', '$0.00')
                    ->description('Error loading data')
                    ->descriptionIcon('heroicon-m-exclamation-triangle')
                    ->color('danger'),
            ];
        }
    }

    private function getRevenueChartData(): array
    {
        try {
            return Invoice::where('status', Invoice::PAID)
                ->selectRaw('DATE(created_at) as date, SUM(final_amount) as total')
                ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
                ->groupBy('date')
                ->orderBy('date')
                ->pluck('total')
                ->map(fn($value) => (float) $value)
                ->toArray();
        } catch (\Exception $e) {
            \Log::error('Revenue chart data error: ' . $e->getMessage());
            return [0, 0, 0, 0, 0]; // Safe fallback
        }
    }

    private function getInvoiceChartData(): array
    {
        return Invoice::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count')
            ->toArray();
    }

    private function getClientChartData(): array
    {
        return Client::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count')
            ->toArray();
    }

    private function getPaymentChartData(): array
    {
        return Payment::selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->whereBetween('created_at', [Carbon::now()->subDays(30), Carbon::now()])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('total')
            ->toArray();
    }
}
