<?php
/**
 * 🚨 EMERGENCY FIX SCRIPT - CRITICAL ERRORS RESOLUTION
 * 
 * This script resolves:
 * 1. FilamentLanguageSwitch locale_get_display_name() error
 * 2. MySQL driver "could not find driver" error
 * 3. Session and authentication issues
 * 
 * Usage: php EMERGENCY_FIX_CRITICAL_ERRORS.php
 */

echo "🚨 EMERGENCY FIX SCRIPT - CRITICAL ERRORS RESOLUTION\n";
echo str_repeat("=", 60) . "\n\n";

// Bootstrap Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ <PERSON><PERSON> bootstrapped successfully\n";
} catch (Exception $e) {
    echo "❌ Laravel bootstrap failed: " . $e->getMessage() . "\n";
    echo "Continuing with basic PHP checks...\n";
}

echo "\n🔍 STEP 1: PHP EXTENSIONS DIAGNOSTIC\n";
echo str_repeat("-", 40) . "\n";

// Check critical extensions
$criticalExtensions = [
    'intl' => 'International extension (required for locale functions)',
    'pdo' => 'PDO extension (required for database)',
    'pdo_mysql' => 'PDO MySQL driver (required for MySQL)',
    'mysqli' => 'MySQLi extension (backup MySQL driver)',
    'mbstring' => 'Multibyte string extension',
    'fileinfo' => 'File information extension',
    'openssl' => 'OpenSSL extension',
    'tokenizer' => 'Tokenizer extension',
    'xml' => 'XML extension',
    'ctype' => 'Character type extension',
    'json' => 'JSON extension'
];

$missingExtensions = [];
foreach ($criticalExtensions as $ext => $description) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext}: Loaded\n";
    } else {
        echo "   ❌ {$ext}: MISSING - {$description}\n";
        $missingExtensions[] = $ext;
    }
}

echo "\n🔍 STEP 2: LOCALE FUNCTION TEST\n";
echo str_repeat("-", 40) . "\n";

// Test locale_get_display_name function
try {
    if (function_exists('locale_get_display_name')) {
        $testResult = locale_get_display_name('en', 'en');
        echo "   ✅ locale_get_display_name() function: Working\n";
        echo "   📝 Test result: {$testResult}\n";
    } else {
        echo "   ❌ locale_get_display_name() function: NOT FOUND\n";
        echo "   💡 Solution: Enable intl extension in php.ini\n";
    }
} catch (Exception $e) {
    echo "   ❌ locale_get_display_name() error: " . $e->getMessage() . "\n";
}

echo "\n🔍 STEP 3: DATABASE CONNECTION TEST\n";
echo str_repeat("-", 40) . "\n";

// Test MySQL connection
try {
    // Try PDO MySQL connection
    $host = '127.0.0.1';
    $dbname = 'invoicemod';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "   ✅ PDO MySQL connection: Successful\n";
    
    // Test sessions table
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() > 0) {
        echo "   ✅ Sessions table: Exists\n";
        
        // Test session read
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM sessions");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "   📊 Current sessions: {$result['count']}\n";
    } else {
        echo "   ❌ Sessions table: Missing\n";
        echo "   💡 Solution: Run 'php artisan session:table && php artisan migrate'\n";
    }
    
} catch (PDOException $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
    
    // Check if MySQL service is running
    echo "   🔍 Checking MySQL service status...\n";
    
    // Try to connect without database name
    try {
        $pdo = new PDO("mysql:host={$host}", $username, $password);
        echo "   ✅ MySQL server: Running\n";
        echo "   ❌ Database '{$dbname}': Does not exist\n";
        echo "   💡 Solution: Create database '{$dbname}'\n";
    } catch (PDOException $e2) {
        echo "   ❌ MySQL server: Not running or connection failed\n";
        echo "   💡 Solution: Start XAMPP MySQL service\n";
    }
}

echo "\n🔧 STEP 4: AUTOMATIC FIXES\n";
echo str_repeat("-", 40) . "\n";

// Fix 1: Clear all caches
echo "   🧹 Clearing Laravel caches...\n";
try {
    if (function_exists('artisan')) {
        Artisan::call('config:clear');
        Artisan::call('cache:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        echo "   ✅ Caches cleared successfully\n";
    } else {
        echo "   ⚠️  Artisan not available, skipping cache clear\n";
    }
} catch (Exception $e) {
    echo "   ❌ Cache clear failed: " . $e->getMessage() . "\n";
}

// Fix 2: Check and fix .env configuration
echo "\n   ⚙️  Checking .env configuration...\n";
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    // Check critical settings
    $criticalSettings = [
        'APP_KEY' => 'Application encryption key',
        'DB_CONNECTION' => 'Database connection type',
        'DB_HOST' => 'Database host',
        'DB_DATABASE' => 'Database name',
        'SESSION_DRIVER' => 'Session storage driver'
    ];
    
    foreach ($criticalSettings as $setting => $description) {
        if (strpos($envContent, $setting . '=') !== false) {
            preg_match('/' . $setting . '=(.*)/', $envContent, $matches);
            $value = isset($matches[1]) ? trim($matches[1]) : 'empty';
            echo "   📝 {$setting}: {$value}\n";
        } else {
            echo "   ❌ {$setting}: Missing\n";
        }
    }
} else {
    echo "   ❌ .env file not found\n";
    echo "   💡 Solution: Copy .env.example to .env\n";
}

echo "\n🔧 STEP 5: FILAMENT LANGUAGE SWITCH FIX\n";
echo str_repeat("-", 40) . "\n";

// Check if the package is properly installed
$packagePath = 'vendor/bezhansalleh/filament-language-switch';
if (is_dir($packagePath)) {
    echo "   ✅ FilamentLanguageSwitch package: Installed\n";
    
    // Check the problematic file
    $switchFile = $packagePath . '/src/LanguageSwitch.php';
    if (file_exists($switchFile)) {
        $content = file_get_contents($switchFile);
        if (strpos($content, 'locale_get_display_name') !== false) {
            echo "   ✅ LanguageSwitch.php: Contains locale_get_display_name function\n";
            
            // Check if intl extension is loaded
            if (extension_loaded('intl')) {
                echo "   ✅ intl extension: Loaded\n";
                echo "   💡 The error might be context-specific. Try clearing caches.\n";
            } else {
                echo "   ❌ intl extension: Not loaded\n";
                echo "   💡 Solution: Enable intl extension in php.ini\n";
            }
        }
    }
} else {
    echo "   ❌ FilamentLanguageSwitch package: Not found\n";
    echo "   💡 Solution: Run 'composer install'\n";
}

echo "\n🚀 STEP 6: RECOMMENDED IMMEDIATE ACTIONS\n";
echo str_repeat("-", 40) . "\n";

$actions = [];

if (!empty($missingExtensions)) {
    $actions[] = "Enable missing PHP extensions: " . implode(', ', $missingExtensions);
}

if (!file_exists('.env')) {
    $actions[] = "Create .env file from .env.example";
}

$actions[] = "Start XAMPP MySQL service if not running";
$actions[] = "Create database 'invoicemod' if it doesn't exist";
$actions[] = "Run: php artisan config:clear";
$actions[] = "Run: php artisan cache:clear";
$actions[] = "Run: php artisan migrate --force";
$actions[] = "Run: composer install (if packages missing)";

foreach ($actions as $i => $action) {
    echo "   " . ($i + 1) . ". {$action}\n";
}

echo "\n🎯 STEP 7: QUICK FIX COMMANDS\n";
echo str_repeat("-", 40) . "\n";

echo "Run these commands in order:\n\n";
echo "1. Start XAMPP Control Panel and start MySQL\n";
echo "2. Create database:\n";
echo "   mysql -u root -p -e \"CREATE DATABASE IF NOT EXISTS invoicemod;\"\n\n";
echo "3. Clear Laravel caches:\n";
echo "   php artisan config:clear\n";
echo "   php artisan cache:clear\n";
echo "   php artisan route:clear\n";
echo "   php artisan view:clear\n\n";
echo "4. Run migrations:\n";
echo "   php artisan migrate --force\n\n";
echo "5. Install/update packages:\n";
echo "   composer install\n\n";
echo "6. Test the application:\n";
echo "   php artisan serve\n\n";

echo "✅ Emergency diagnostic completed!\n";
echo "📋 Check the actions above and run the quick fix commands.\n";
