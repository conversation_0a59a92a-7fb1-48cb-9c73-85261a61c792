<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use App\Services\RoleManagementService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;
use App\Models\User;
use Spatie\Permission\Models\Role;

class ListRoles extends ListRecords
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            
            Action::make('bulk_assign_roles')
                ->label('Bulk Assign Roles')
                ->icon('heroicon-o-user-group')
                ->color('info')
                ->modalHeading('Bulk Assign Roles to Users')
                ->modalDescription('Select users and roles to assign in bulk')
                ->modalWidth(MaxWidth::FourExtraLarge)
                ->form([
                    Select::make('users')
                        ->label('Select Users')
                        ->multiple()
                        ->searchable()
                        ->options(User::all()->pluck('name', 'id'))
                        ->required(),
                    
                    CheckboxList::make('roles')
                        ->label('Select Roles')
                        ->options(Role::all()->pluck('name', 'name'))
                        ->required()
                        ->columns(2),
                ])
                ->action(function (array $data): void {
                    $roleManagementService = app(RoleManagementService::class);
                    $results = $roleManagementService->bulkAssignRoles($data['users'], $data['roles']);
                    
                    if ($results['success'] > 0) {
                        Notification::make()
                            ->title("Successfully assigned roles to {$results['success']} users")
                            ->success()
                            ->send();
                    }
                    
                    if ($results['failed'] > 0) {
                        Notification::make()
                            ->title("Failed to assign roles to {$results['failed']} users")
                            ->body(implode(', ', array_slice($results['errors'], 0, 3)))
                            ->warning()
                            ->send();
                    }
                }),
            
            Action::make('create_default_roles')
                ->label('Create Default Roles')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->requiresConfirmation()
                ->modalHeading('Create Default System Roles')
                ->modalDescription('This will create the default system roles with predefined permissions. Existing roles will not be affected.')
                ->action(function (): void {
                    $roleManagementService = app(RoleManagementService::class);
                    
                    try {
                        $roleManagementService->createDefaultPermissions();
                        $roleManagementService->createDefaultRoles();
                        
                        Notification::make()
                            ->title('Default roles and permissions created successfully')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Failed to create default roles')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
            
            Action::make('role_analytics')
                ->label('Role Analytics')
                ->icon('heroicon-o-chart-bar')
                ->color('success')
                ->modalHeading('Role & Permission Analytics')
                ->modalWidth(MaxWidth::FiveExtraLarge)
                ->modalContent(function (): string {
                    $roleManagementService = app(RoleManagementService::class);
                    $analytics = $roleManagementService->getUserRoleAnalytics();
                    $hierarchy = $roleManagementService->getRoleHierarchy();
                    
                    return view('filament.pages.role-analytics', compact('analytics', 'hierarchy'))->render();
                }),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            RoleResource\Widgets\RoleStatsWidget::class,
        ];
    }
}
