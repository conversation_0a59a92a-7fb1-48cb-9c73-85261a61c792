<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SystemNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'priority',
        'action_url',
        'action_text',
        'data',
        'read_at',
        'expires_at',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function markAsRead(): void
    {
        $this->update(['read_at' => now()]);
    }

    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getTypeColorAttribute(): string
    {
        return match ($this->type) {
            'success' => 'success',
            'warning' => 'warning',
            'error' => 'danger',
            'info' => 'info',
            default => 'gray',
        };
    }

    public function getTypeIconAttribute(): string
    {
        return match ($this->type) {
            'success' => 'heroicon-o-check-circle',
            'warning' => 'heroicon-o-exclamation-triangle',
            'error' => 'heroicon-o-x-circle',
            'info' => 'heroicon-o-information-circle',
            default => 'heroicon-o-bell',
        };
    }

    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'high' => 'danger',
            'medium' => 'warning',
            'low' => 'success',
            default => 'gray',
        };
    }

    public static function createForUser(
        User $user,
        string $title,
        string $message,
        string $type = 'info',
        string $priority = 'medium',
        string $actionUrl = null,
        string $actionText = null,
        array $data = [],
        Carbon $expiresAt = null
    ): self {
        return static::create([
            'user_id' => $user->id,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'priority' => $priority,
            'action_url' => $actionUrl,
            'action_text' => $actionText,
            'data' => $data,
            'expires_at' => $expiresAt,
        ]);
    }

    public static function createForAllUsers(
        string $title,
        string $message,
        string $type = 'info',
        string $priority = 'medium',
        string $actionUrl = null,
        string $actionText = null,
        array $data = [],
        Carbon $expiresAt = null
    ): int {
        $users = User::all();
        $count = 0;

        foreach ($users as $user) {
            static::createForUser(
                $user,
                $title,
                $message,
                $type,
                $priority,
                $actionUrl,
                $actionText,
                $data,
                $expiresAt
            );
            $count++;
        }

        return $count;
    }

    public static function notifyInvoiceOverdue(Invoice $invoice): self
    {
        return static::createForUser(
            $invoice->client->user,
            'Invoice Overdue',
            "Invoice #{$invoice->invoice_number} is overdue. Please make payment as soon as possible.",
            'warning',
            'high',
            route('filament.client.resources.invoices.view', $invoice),
            'View Invoice',
            ['invoice_id' => $invoice->id]
        );
    }

    public static function notifyPaymentReceived(Payment $payment): self
    {
        return static::createForUser(
            $payment->invoice->client->user,
            'Payment Received',
            "Your payment of $" . number_format($payment->amount, 2) . " has been received.",
            'success',
            'medium',
            route('filament.client.resources.payments.view', $payment),
            'View Payment',
            ['payment_id' => $payment->id]
        );
    }

    public static function notifyInvoiceCreated(Invoice $invoice): self
    {
        return static::createForUser(
            $invoice->client->user,
            'New Invoice',
            "A new invoice #{$invoice->invoice_number} has been created for you.",
            'info',
            'medium',
            route('filament.client.resources.invoices.view', $invoice),
            'View Invoice',
            ['invoice_id' => $invoice->id]
        );
    }

    public static function notifySystemMaintenance(
        string $message,
        Carbon $scheduledTime,
        int $durationMinutes = 30
    ): int {
        return static::createForAllUsers(
            'Scheduled Maintenance',
            $message,
            'warning',
            'high',
            null,
            null,
            [
                'scheduled_time' => $scheduledTime->toISOString(),
                'duration_minutes' => $durationMinutes,
            ],
            $scheduledTime->addMinutes($durationMinutes)
        );
    }

    public static function notifySecurityAlert(string $message, array $data = []): int
    {
        return static::createForAllUsers(
            'Security Alert',
            $message,
            'error',
            'high',
            null,
            null,
            $data,
            now()->addDays(7)
        );
    }

    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', now())->delete();
    }

    public static function markAllAsReadForUser(User $user): int
    {
        return static::forUser($user)
            ->unread()
            ->update(['read_at' => now()]);
    }
}
