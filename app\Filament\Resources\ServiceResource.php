<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Service;
use App\Models\Category;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Filament\Resources\Resource;
use App\AdminDashboardSidebarSorting;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\FontWeight;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\ServiceResource\Pages;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\ServiceResource\RelationManagers;
use Filament\Forms\Components\Section;

class ServiceResource extends Resource
{
    protected static ?string $model = Service::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?int $navigationSort = AdminDashboardSidebarSorting::PRODUCTS->value;

    public static function getNavigationLabel(): string
    {
        return __('Services');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->label(__('Service Name') . ':')
                            ->validationAttribute(__('Service Name'))
                            ->placeholder(__('Service Name'))
                            ->maxLength(255),
                        TextInput::make('code')
                            ->required()
                            ->default(strtoupper(Str::random(6)))
                            ->unique('services', 'code', ignoreRecord: true)
                            ->readOnly()
                            ->label(__('Service Code') . ':')
                            ->placeholder(__('Service Code'))
                            ->validationAttribute(__('Service Code')),
                        Select::make('category_id')
                            ->required()
                            ->options(Category::all()->pluck('name', 'id'))
                            ->native(false)
                            ->label(__('Service Category') . ':')
                            ->placeholder(__('Select Category'))
                            ->validationAttribute(__('Service Category')),
                        TextInput::make('unit_price')
                            ->required()
                            ->numeric()
                            ->label(__('Service Price') . ':')
                            ->placeholder(__('0.00'))
                            ->validationAttribute(__('Service Price'))
                            ->prefix('$'),
                        Textarea::make('description')
                            ->label(__('Service Description') . ':')
                            ->placeholder(__('Service Description'))
                            ->validationAttribute(__('Service Description'))
                            ->columnSpanFull(),
                        SpatieMediaLibraryFileUpload::make('service_image')
                            ->label(__('Service Image') . ':')
                            ->collection('service')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif'])
                            ->maxSize(2048)
                            ->columnSpanFull(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('service_image')
                    ->collection('service')
                    ->label(__('Service Image'))
                    ->circular()
                    ->width(50)
                    ->height(50),
                TextColumn::make('name')
                    ->label(__('Service Name'))
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::SemiBold),
                TextColumn::make('code')
                    ->label(__('Service Code'))
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                TextColumn::make('category.name')
                    ->label(__('Category'))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('unit_price')
                    ->label(__('Price'))
                    ->money('USD')
                    ->sortable(),
                TextColumn::make('description')
                    ->label(__('Description'))
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }
                        return $state;
                    }),
                TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->relationship('category', 'name')
                    ->label(__('Category')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServices::route('/'),
            'create' => Pages\CreateService::route('/create'),
            'edit' => Pages\EditService::route('/{record}/edit'),
        ];
    }
}
