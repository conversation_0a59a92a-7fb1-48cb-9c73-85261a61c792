# 🚀 Shared Hosting Setup Guide - 419 Error Fix

## 📋 Overview
This guide provides step-by-step instructions to deploy your Laravel invoice management system on shared hosting and resolve 419 "Page Expired" errors.

## 🔧 Pre-Deployment Checklist

### 1. Environment Configuration
- [ ] Copy `.env.shared-hosting` to `.env`
- [ ] Update database credentials in `.env`
- [ ] Set correct `APP_URL` for your domain
- [ ] Set `APP_ENV=production` and `APP_DEBUG=false`
- [ ] Ensure `SESSION_DRIVER=database`

### 2. Database Setup
- [ ] Create MySQL database on shared hosting
- [ ] Import your database structure
- [ ] Verify sessions table exists
- [ ] Test database connectivity

### 3. File Upload and Permissions
- [ ] Upload all files to shared hosting
- [ ] Set correct file permissions
- [ ] Verify storage directory is writable

## 🛠️ Step-by-Step Deployment

### Step 1: Prepare Environment File

1. **Copy the shared hosting template:**
   ```bash
   cp .env.shared-hosting .env
   ```

2. **Update these critical values in .env:**
   ```env
   # Your actual domain
   APP_URL=https://yourdomain.com
   
   # Production settings
   APP_ENV=production
   APP_DEBUG=false
   
   # Your shared hosting database credentials
   DB_DATABASE=your_database_name
   DB_USERNAME=your_database_user
   DB_PASSWORD=your_database_password
   
   # Session configuration (CRITICAL for 419 fix)
   SESSION_DRIVER=database
   SESSION_DOMAIN=null
   SESSION_SECURE_COOKIE=true
   
   # Mail settings (update with your hosting provider's SMTP)
   MAIL_HOST=mail.yourdomain.com
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your_mail_password
   ```

### Step 2: Database Migration

1. **Upload your database:**
   - Export your local database
   - Import to shared hosting MySQL
   - Verify all tables exist, especially `sessions`

2. **If sessions table is missing:**
   ```bash
   php artisan session:table
   php artisan migrate --force
   ```

### Step 3: File Permissions Setup

Set these permissions on your shared hosting:

```bash
# Application directories
chmod 755 app/
chmod 755 config/
chmod 755 database/
chmod 755 public/
chmod 755 resources/
chmod 755 routes/

# Storage directories (CRITICAL)
chmod 755 storage/
chmod 755 storage/app/
chmod 755 storage/framework/
chmod 755 storage/framework/cache/
chmod 755 storage/framework/sessions/
chmod 755 storage/framework/views/
chmod 755 storage/logs/

# Bootstrap cache (CRITICAL)
chmod 755 bootstrap/
chmod 755 bootstrap/cache/

# Make specific files writable
chmod 644 .env
chmod 644 composer.json
chmod 644 artisan
```

### Step 4: Optimize for Production

Run these commands on shared hosting:

```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Generate application key if needed
php artisan key:generate --force
```

## 🔍 Troubleshooting 419 Errors

### Common Causes and Solutions

#### 1. Session Driver Issues
**Problem:** File sessions don't work reliably on shared hosting
**Solution:** Use database sessions
```env
SESSION_DRIVER=database
```

#### 2. Domain Mismatch
**Problem:** Session cookies tied to wrong domain
**Solution:** Set domain to null for flexibility
```env
SESSION_DOMAIN=null
```

#### 3. HTTPS/HTTP Mismatch
**Problem:** Secure cookies on HTTP or vice versa
**Solution:** Match your hosting environment
```env
# For HTTPS (recommended)
SESSION_SECURE_COOKIE=true
APP_URL=https://yourdomain.com

# For HTTP (not recommended for production)
SESSION_SECURE_COOKIE=false
APP_URL=http://yourdomain.com
```

#### 4. File Permission Issues
**Problem:** Laravel can't write session files
**Solution:** Ensure proper permissions
```bash
chmod 755 storage/framework/sessions/
chmod 755 bootstrap/cache/
```

#### 5. Cache Issues
**Problem:** Old configuration cached
**Solution:** Clear all caches
```bash
php artisan config:clear
php artisan cache:clear
```

## 🧪 Testing Your Setup

### 1. Run the Diagnostic Script
```bash
php fix_419_error.php
```

### 2. Manual Login Test
1. Visit your login page
2. Open browser developer tools
3. Check for CSRF token in page source
4. Attempt login
5. Monitor network requests for 419 errors

### 3. Session Verification
Check if sessions are being created:
```sql
SELECT * FROM sessions ORDER BY last_activity DESC LIMIT 5;
```

## 🚨 Emergency Fixes

### If 419 Errors Persist:

1. **Immediate Fix:**
   ```bash
   php artisan config:clear
   php artisan cache:clear
   php artisan session:table
   php artisan migrate --force
   ```

2. **Check .env file:**
   ```bash
   grep -E "(SESSION_|APP_URL|APP_KEY)" .env
   ```

3. **Verify database connection:**
   ```bash
   php artisan tinker
   >>> DB::connection()->getPdo();
   >>> DB::table('sessions')->count();
   ```

4. **Test CSRF token generation:**
   ```bash
   php artisan tinker
   >>> csrf_token();
   ```

## 📞 Support Checklist

If you still experience issues, provide this information:

- [ ] Shared hosting provider name
- [ ] PHP version
- [ ] MySQL version
- [ ] Error logs from storage/logs/
- [ ] Output of `php fix_419_error.php`
- [ ] Browser developer tools network tab screenshot
- [ ] Current .env configuration (without sensitive data)

## ✅ Success Indicators

Your setup is working correctly when:
- [ ] Login page loads without errors
- [ ] CSRF token visible in page source
- [ ] Login attempts don't return 419 errors
- [ ] Sessions are created in database
- [ ] User can successfully authenticate
- [ ] Dashboard loads after login

## 🔒 Security Recommendations

For production environments:
- [ ] Set `APP_DEBUG=false`
- [ ] Use HTTPS (`SESSION_SECURE_COOKIE=true`)
- [ ] Regular database backups
- [ ] Monitor error logs
- [ ] Keep Laravel updated
- [ ] Use strong database passwords
- [ ] Restrict file permissions appropriately
