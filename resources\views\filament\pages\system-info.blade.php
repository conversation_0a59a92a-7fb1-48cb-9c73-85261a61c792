<div class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- PHP Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">PHP Information</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Version:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['php_version'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Memory Limit:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['memory_limit'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Max Execution Time:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['max_execution_time'] }}s</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Upload Max Filesize:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['upload_max_filesize'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Post Max Size:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['post_max_size'] }}</span>
                </div>
            </div>
        </div>

        <!-- Application Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Application Information</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Laravel Version:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['laravel_version'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Database Driver:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['database_driver'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Server Software:</span>
                    <span class="text-sm text-gray-900 dark:text-white">{{ $systemInfo['server_software'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- PHP Extensions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Loaded PHP Extensions</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            @foreach($systemInfo['extensions'] as $extension)
                <div class="bg-gray-50 dark:bg-gray-700 rounded px-3 py-1">
                    <span class="text-xs text-gray-700 dark:text-gray-300">{{ $extension }}</span>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Critical Extensions Check -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Critical Extensions Status</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @php
                $criticalExtensions = [
                    'pdo' => 'PDO Database Abstraction',
                    'pdo_mysql' => 'MySQL PDO Driver',
                    'pdo_sqlite' => 'SQLite PDO Driver',
                    'openssl' => 'OpenSSL Encryption',
                    'mbstring' => 'Multibyte String',
                    'tokenizer' => 'PHP Tokenizer',
                    'xml' => 'XML Parser',
                    'ctype' => 'Character Type',
                    'json' => 'JSON Support',
                    'bcmath' => 'BC Math',
                    'fileinfo' => 'File Information',
                    'gd' => 'GD Image Library',
                    'curl' => 'cURL Support',
                    'zip' => 'ZIP Archive',
                ];
            @endphp

            @foreach($criticalExtensions as $ext => $description)
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $description }}</p>
                        <p class="text-xs text-gray-600 dark:text-gray-400">{{ $ext }}</p>
                    </div>
                    @if(in_array($ext, $systemInfo['extensions']))
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            ✓ Loaded
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            ✗ Missing
                        </span>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
