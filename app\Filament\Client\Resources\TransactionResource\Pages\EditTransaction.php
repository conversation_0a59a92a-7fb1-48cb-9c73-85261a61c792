<?php

namespace App\Filament\Client\Resources\TransactionResource\Pages;

use App\Filament\Client\Resources\TransactionResource;
use App\Models\Payment;
use App\Models\Invoice;
use App\Services\InvoiceStatusService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Exception;

class EditTransaction extends EditRecord
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn($record) => $record->is_approved == Payment::PENDING),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Ensure we don't accidentally change critical fields
        $data['amount'] = $this->record->amount;
        $data['payment_mode'] = $this->record->payment_mode;
        $data['invoice_id'] = $this->record->invoice_id;
        
        return $data;
    }

    protected function afterSave(): void
    {
        try {
            // Update invoice status based on payment status change
            $this->updateInvoiceStatus();
            
            Notification::make()
                ->success()
                ->title(__('messages.flash.transaction_updated_successfully'))
                ->send();
                
        } catch (Exception $e) {
            Notification::make()
                ->danger()
                ->title(__('messages.flash.error_occurred'))
                ->body($e->getMessage())
                ->send();
        }
    }

    private function updateInvoiceStatus(): void
    {
        $payment = $this->record;
        $invoice = $payment->invoice;
        
        // Calculate total approved payments for this invoice
        $totalApprovedPayments = Payment::where('invoice_id', $invoice->id)
            ->where('is_approved', Payment::APPROVED)
            ->sum('amount');
        
        // Determine new invoice status
        $newStatus = Invoice::UNPAID;
        
        if ($totalApprovedPayments > 0) {
            if ($totalApprovedPayments >= $invoice->final_amount) {
                $newStatus = Invoice::PAID;
            } else {
                $newStatus = Invoice::PARTIALLY;
            }
        }
        
        // Update invoice status using the service
        $statusService = new InvoiceStatusService();
        $statusService->updateInvoiceStatus($invoice, $newStatus);
    }
}
