<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\SystemHealthService;
use Symfony\Component\HttpFoundation\Response;

/**
 * 🔥 SYSTEM ERROR HANDLER - BEAST MODE ERROR PREVENTION 🔥
 * 
 * Global error handling middleware for enhanced system robustness
 */
class SystemErrorHandler
{
    protected SystemHealthService $healthService;

    public function __construct(SystemHealthService $healthService)
    {
        $this->healthService = $healthService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
            
            // Log successful operations for monitoring
            if ($this->isImportantOperation($request)) {
                $this->logSuccessfulOperation($request);
            }
            
            return $response;
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Handle validation errors gracefully
            $this->healthService->logError('validation', $e->getMessage(), [
                'url' => $request->url(),
                'method' => $request->method(),
                'errors' => $e->errors(),
            ]);
            
            throw $e; // Re-throw to maintain normal validation flow
            
        } catch (\Illuminate\Database\QueryException $e) {
            // Handle database errors
            $this->healthService->logError('database', $e->getMessage(), [
                'url' => $request->url(),
                'method' => $request->method(),
                'sql' => $e->getSql(),
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Database operation failed',
                    'message' => 'Please try again later or contact support if the problem persists.',
                ], 500);
            }
            
            return response()->view('errors.database', [], 500);
            
        } catch (\Exception $e) {
            // Handle general errors
            $this->healthService->logError('general', $e->getMessage(), [
                'url' => $request->url(),
                'method' => $request->method(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'An unexpected error occurred',
                    'message' => 'Please try again later or contact support if the problem persists.',
                ], 500);
            }
            
            return response()->view('errors.general', ['exception' => $e], 500);
        }
    }

    /**
     * Check if this is an important operation to monitor
     */
    private function isImportantOperation(Request $request): bool
    {
        $importantRoutes = [
            'invoices',
            'reports',
            'export',
            'pdf',
            'payments',
        ];
        
        foreach ($importantRoutes as $route) {
            if (str_contains($request->path(), $route)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Log successful operations for monitoring
     */
    private function logSuccessfulOperation(Request $request): void
    {
        Log::info('Successful operation', [
            'url' => $request->url(),
            'method' => $request->method(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ]);
    }
}
