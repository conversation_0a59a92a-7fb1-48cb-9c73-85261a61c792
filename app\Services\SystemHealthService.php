<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Models\Setting;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 🔥 SYSTEM HEALTH SERVICE - BEAST MODE MONITORING 🔥
 * 
 * Comprehensive system health monitoring and error prevention service
 */
class SystemHealthService
{
    /**
     * Perform comprehensive system health check
     */
    public function performHealthCheck(): array
    {
        $results = [
            'overall_status' => 'healthy',
            'timestamp' => Carbon::now()->toISOString(),
            'checks' => []
        ];

        try {
            // Database connectivity check
            $results['checks']['database'] = $this->checkDatabaseHealth();
            
            // Data integrity checks
            $results['checks']['data_integrity'] = $this->checkDataIntegrity();
            
            // Export functionality checks
            $results['checks']['export_functionality'] = $this->checkExportFunctionality();
            
            // Settings validation checks
            $results['checks']['settings_validation'] = $this->checkSettingsValidation();
            
            // Performance metrics
            $results['checks']['performance'] = $this->checkPerformanceMetrics();
            
            // Error rate monitoring
            $results['checks']['error_rates'] = $this->checkErrorRates();

            // Determine overall status
            $results['overall_status'] = $this->determineOverallStatus($results['checks']);
            
        } catch (\Exception $e) {
            Log::error('System health check failed: ' . $e->getMessage());
            $results['overall_status'] = 'critical';
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Check database connectivity and basic operations
     */
    private function checkDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            
            // Test basic database operations
            $invoiceCount = Invoice::count();
            $clientCount = Client::count();
            $paymentCount = Payment::count();
            
            $responseTime = (microtime(true) - $start) * 1000;
            
            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'invoice_count' => $invoiceCount,
                'client_count' => $clientCount,
                'payment_count' => $paymentCount,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check data integrity across the system
     */
    private function checkDataIntegrity(): array
    {
        try {
            $issues = [];
            
            // Check for orphaned invoice items
            $orphanedItems = DB::table('invoice_items')
                ->leftJoin('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
                ->whereNull('invoices.id')
                ->count();
            
            if ($orphanedItems > 0) {
                $issues[] = "Found {$orphanedItems} orphaned invoice items";
            }
            
            // Check for invoices without clients
            $invoicesWithoutClients = Invoice::whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('clients')
                    ->whereRaw('clients.id = invoices.client_id');
            })->count();
            
            if ($invoicesWithoutClients > 0) {
                $issues[] = "Found {$invoicesWithoutClients} invoices without valid clients";
            }
            
            // Check for inconsistent payment amounts
            $inconsistentPayments = Payment::whereRaw('amount <= 0')->count();
            
            if ($inconsistentPayments > 0) {
                $issues[] = "Found {$inconsistentPayments} payments with invalid amounts";
            }
            
            return [
                'status' => empty($issues) ? 'healthy' : 'warning',
                'issues' => $issues,
                'checks_performed' => 3,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check export functionality health
     */
    private function checkExportFunctionality(): array
    {
        try {
            $checks = [];
            
            // Check if required export classes exist
            $exportClasses = [
                'App\Exports\FinancialReportExport',
                'App\Exports\ClientPerformanceExport',
                'App\Exports\ProductAnalyticsExport',
                'App\Exports\InvoiceAnalyticsExport',
                'App\Exports\OverdueInvoicesExport',
                'App\Exports\TaxSummaryExport',
            ];
            
            foreach ($exportClasses as $class) {
                $checks[$class] = class_exists($class);
            }
            
            // Check if required view files exist
            $viewFiles = [
                'reports.financial-report-pdf',
                'reports.client-performance-pdf',
                'reports.invoice-analytics-pdf',
                'reports.product-analytics-pdf',
            ];
            
            foreach ($viewFiles as $view) {
                $checks["view_{$view}"] = view()->exists($view);
            }
            
            $allHealthy = !in_array(false, $checks);
            
            return [
                'status' => $allHealthy ? 'healthy' : 'warning',
                'checks' => $checks,
                'total_checks' => count($checks),
                'passed_checks' => count(array_filter($checks)),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check settings validation health
     */
    private function checkSettingsValidation(): array
    {
        try {
            $issues = [];
            
            // Check for required settings
            $requiredSettings = ['app_name', 'company_name', 'current_currency'];
            
            foreach ($requiredSettings as $setting) {
                $value = Setting::where('key', $setting)->first();
                if (!$value || empty($value->value)) {
                    $issues[] = "Missing required setting: {$setting}";
                }
            }
            
            // Check logo settings (should be optional now)
            $logoSettings = Setting::whereIn('key', ['app_logo', 'favicon_icon'])->get();
            $logoValidation = [
                'app_logo_optional' => true,
                'favicon_icon_optional' => true,
            ];
            
            return [
                'status' => empty($issues) ? 'healthy' : 'warning',
                'issues' => $issues,
                'logo_validation' => $logoValidation,
                'required_settings_count' => count($requiredSettings),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check performance metrics
     */
    private function checkPerformanceMetrics(): array
    {
        try {
            $start = microtime(true);
            
            // Test dashboard query performance
            $dashboardStart = microtime(true);
            $invoiceStats = Invoice::selectRaw('status, COUNT(*) as count, SUM(final_amount) as total')
                ->where('status', '!=', Invoice::DRAFT)
                ->groupBy('status')
                ->get();
            $dashboardTime = (microtime(true) - $dashboardStart) * 1000;
            
            // Test reporting query performance
            $reportStart = microtime(true);
            $recentPayments = Payment::where('is_approved', Payment::APPROVED)
                ->where('created_at', '>=', Carbon::now()->subDays(30))
                ->count();
            $reportTime = (microtime(true) - $reportStart) * 1000;
            
            $totalTime = (microtime(true) - $start) * 1000;
            
            return [
                'status' => $totalTime < 1000 ? 'healthy' : 'warning',
                'total_response_time_ms' => round($totalTime, 2),
                'dashboard_query_time_ms' => round($dashboardTime, 2),
                'report_query_time_ms' => round($reportTime, 2),
                'recent_payments_count' => $recentPayments,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check error rates from logs
     */
    private function checkErrorRates(): array
    {
        try {
            // This is a simplified check - in production you'd analyze actual log files
            $cacheKey = 'system_error_count_' . Carbon::now()->format('Y-m-d-H');
            $errorCount = Cache::get($cacheKey, 0);
            
            return [
                'status' => $errorCount < 10 ? 'healthy' : 'warning',
                'error_count_last_hour' => $errorCount,
                'threshold' => 10,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Determine overall system status
     */
    private function determineOverallStatus(array $checks): string
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('unhealthy', $statuses)) {
            return 'critical';
        }
        
        if (in_array('warning', $statuses)) {
            return 'warning';
        }
        
        return 'healthy';
    }

    /**
     * Log error for monitoring
     */
    public function logError(string $component, string $error, array $context = []): void
    {
        $cacheKey = 'system_error_count_' . Carbon::now()->format('Y-m-d-H');
        Cache::increment($cacheKey, 1);
        Cache::put($cacheKey, Cache::get($cacheKey, 1), 3600); // 1 hour TTL
        
        Log::error("System Error [{$component}]: {$error}", $context);
    }

    /**
     * Get system health summary
     */
    public function getHealthSummary(): array
    {
        $health = $this->performHealthCheck();
        
        return [
            'status' => $health['overall_status'],
            'timestamp' => $health['timestamp'],
            'summary' => [
                'database' => $health['checks']['database']['status'] ?? 'unknown',
                'data_integrity' => $health['checks']['data_integrity']['status'] ?? 'unknown',
                'exports' => $health['checks']['export_functionality']['status'] ?? 'unknown',
                'settings' => $health['checks']['settings_validation']['status'] ?? 'unknown',
                'performance' => $health['checks']['performance']['status'] ?? 'unknown',
            ]
        ];
    }
}
