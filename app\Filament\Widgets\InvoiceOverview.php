<?php

namespace App\Filament\Widgets;

use App\Models\Invoice;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;

class InvoiceOverview extends ChartWidget
{
    protected static ?int $sort = 4;
    protected static ?array $options = [
        'responsive' => true,
        'maintainAspectRatio' => false,
        'plugins' => [
            'legend' => [
                'display' => true,
                'position' => 'bottom',
                'labels' => [
                    'usePointStyle' => true,
                    'padding' => 20,
                    'font' => [
                        'size' => 12,
                    ],
                ],
            ],
            'tooltip' => [
                'enabled' => true,
                'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                'titleColor' => '#fff',
                'bodyColor' => '#fff',
                'borderColor' => '#fff',
                'borderWidth' => 1,
            ],
        ],
        'animation' => [
            'animateRotate' => true,
            'animateScale' => true,
            'duration' => 1000,
        ],
        'cutout' => '60%',
    ];
    protected static ?string $maxHeight = '450px'; // Updated for better display

    public function getHeading(): string|Htmlable|null
    {
        return __('messages.admin_dashboard.invoice_overview');
    }


    protected function getData(): array
    {
        try {
            // Get all invoices excluding drafts
            $invoices = Invoice::where('status', '!=', Invoice::DRAFT)->get();

            $data = [];
            $data['total_paid_invoices'] = $invoices->where('status', Invoice::PAID)->count();
            $data['total_unpaid_invoices'] = $invoices->where('status', Invoice::UNPAID)->count();
            $data['total_partially_paid'] = $invoices->where('status', Invoice::PARTIALLY)->count();
            $data['total_overdue'] = $invoices->where('status', Invoice::OVERDUE)->count();
            $data['total_processing'] = $invoices->where('status', Invoice::PROCESSING)->count();

            // Prepare labels and data points
            $labels = [];
            $dataPoints = [];
            $backgroundColors = [];
            $borderColors = [];
            $hoverBackgroundColors = [];
            $hoverBorderColors = [];

            // Only include statuses that have data
            if ($data['total_paid_invoices'] > 0) {
                $labels[] = __('messages.paid_invoices');
                $dataPoints[] = intval($data['total_paid_invoices']);
                $backgroundColors[] = 'rgba(34, 197, 94, 0.8)';  // Green for paid
                $borderColors[] = 'rgba(34, 197, 94, 1)';
                $hoverBackgroundColors[] = 'rgba(34, 197, 94, 0.9)';
                $hoverBorderColors[] = 'rgba(34, 197, 94, 1)';
            }

            if ($data['total_unpaid_invoices'] > 0) {
                $labels[] = __('messages.unpaid_invoices');
                $dataPoints[] = intval($data['total_unpaid_invoices']);
                $backgroundColors[] = 'rgba(239, 68, 68, 0.8)';  // Red for unpaid
                $borderColors[] = 'rgba(239, 68, 68, 1)';
                $hoverBackgroundColors[] = 'rgba(239, 68, 68, 0.9)';
                $hoverBorderColors[] = 'rgba(239, 68, 68, 1)';
            }

            if ($data['total_partially_paid'] > 0) {
                $labels[] = __('messages.partially_paid_invoices');
                $dataPoints[] = intval($data['total_partially_paid']);
                $backgroundColors[] = 'rgba(245, 158, 11, 0.8)';  // Yellow for partially paid
                $borderColors[] = 'rgba(245, 158, 11, 1)';
                $hoverBackgroundColors[] = 'rgba(245, 158, 11, 0.9)';
                $hoverBorderColors[] = 'rgba(245, 158, 11, 1)';
            }

            if ($data['total_overdue'] > 0) {
                $labels[] = __('messages.overdue_invoices');
                $dataPoints[] = intval($data['total_overdue']);
                $backgroundColors[] = 'rgba(220, 38, 127, 0.8)';  // Pink for overdue
                $borderColors[] = 'rgba(220, 38, 127, 1)';
                $hoverBackgroundColors[] = 'rgba(220, 38, 127, 0.9)';
                $hoverBorderColors[] = 'rgba(220, 38, 127, 1)';
            }

            if ($data['total_processing'] > 0) {
                $labels[] = __('messages.processing_invoices');
                $dataPoints[] = intval($data['total_processing']);
                $backgroundColors[] = 'rgba(59, 130, 246, 0.8)';  // Blue for processing
                $borderColors[] = 'rgba(59, 130, 246, 1)';
                $hoverBackgroundColors[] = 'rgba(59, 130, 246, 0.9)';
                $hoverBorderColors[] = 'rgba(59, 130, 246, 1)';
            }

            // If no data, show empty state
            if (empty($dataPoints)) {
                $labels = [__('messages.no_invoices')];
                $dataPoints = [1];
                $backgroundColors = ['rgba(156, 163, 175, 0.8)'];
                $borderColors = ['rgba(156, 163, 175, 1)'];
                $hoverBackgroundColors = ['rgba(156, 163, 175, 0.9)'];
                $hoverBorderColors = ['rgba(156, 163, 175, 1)'];
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => __('messages.admin_dashboard.invoice_overview'),
                        'data' => $dataPoints,
                        'backgroundColor' => $backgroundColors,
                        'borderColor' => $borderColors,
                        'borderWidth' => 2,
                        'hoverBackgroundColor' => $hoverBackgroundColors,
                        'hoverBorderColor' => $hoverBorderColors,
                    ]
                ],
            ];
        } catch (\Exception $e) {
            \Log::error('InvoiceOverview widget error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'labels' => [__('messages.error')],
                'datasets' => [
                    [
                        'label' => __('messages.admin_dashboard.invoice_overview'),
                        'data' => [1],
                        'backgroundColor' => ['rgba(156, 163, 175, 0.8)'],
                        'borderColor' => ['rgba(156, 163, 175, 1)'],
                        'borderWidth' => 2,
                    ]
                ],
            ];
        }
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
