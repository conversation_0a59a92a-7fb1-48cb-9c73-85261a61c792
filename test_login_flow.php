<?php
/**
 * Comprehensive Login Flow Test Script
 * 
 * Tests the complete authentication process to ensure 419 errors are resolved
 * and the login system works properly on shared hosting.
 * 
 * Usage: php test_login_flow.php
 */

echo "=== COMPREHENSIVE LOGIN FLOW TEST ===\n\n";

// Bootstrap Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use App\Models\User;

$testResults = [];
$errors = [];

echo "🧪 TEST 1: Session System Validation\n";
echo str_repeat("-", 50) . "\n";

try {
    // Test session configuration
    $sessionDriver = config('session.driver');
    $sessionLifetime = config('session.lifetime');
    $sessionDomain = config('session.domain');
    
    echo "   Session Driver: {$sessionDriver}\n";
    echo "   Session Lifetime: {$sessionLifetime} minutes\n";
    echo "   Session Domain: " . ($sessionDomain ?: 'null (flexible)') . "\n";
    
    if ($sessionDriver === 'database') {
        // Test database session functionality
        $sessionCount = DB::table('sessions')->count();
        echo "   Current Sessions: {$sessionCount}\n";
        
        // Test session write capability
        $testSessionId = 'test_' . time() . '_' . rand(1000, 9999);
        DB::table('sessions')->insert([
            'id' => $testSessionId,
            'user_id' => null,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'payload' => base64_encode(serialize(['_token' => 'test_token'])),
            'last_activity' => time()
        ]);
        
        $inserted = DB::table('sessions')->where('id', $testSessionId)->exists();
        if ($inserted) {
            echo "   ✅ Session Write Test: PASSED\n";
            DB::table('sessions')->where('id', $testSessionId)->delete();
            $testResults['session_write'] = true;
        } else {
            echo "   ❌ Session Write Test: FAILED\n";
            $errors[] = "Cannot write to sessions table";
            $testResults['session_write'] = false;
        }
    } else {
        echo "   ⚠️  Warning: Not using database sessions (may cause issues on shared hosting)\n";
        $testResults['session_driver'] = false;
    }
    
} catch (Exception $e) {
    echo "   ❌ Session Test Error: " . $e->getMessage() . "\n";
    $errors[] = "Session system error: " . $e->getMessage();
    $testResults['session_system'] = false;
}

echo "\n🧪 TEST 2: CSRF Token Generation\n";
echo str_repeat("-", 50) . "\n";

try {
    // Start a session for CSRF token generation
    if (!session_id()) {
        session_start();
    }
    
    // Generate CSRF token
    $csrfToken = csrf_token();
    echo "   CSRF Token Generated: " . substr($csrfToken, 0, 20) . "...\n";
    
    // Validate token consistency
    $csrfToken2 = csrf_token();
    if ($csrfToken === $csrfToken2) {
        echo "   ✅ CSRF Token Consistency: PASSED\n";
        $testResults['csrf_generation'] = true;
    } else {
        echo "   ❌ CSRF Token Consistency: FAILED\n";
        $errors[] = "CSRF tokens are not consistent";
        $testResults['csrf_generation'] = false;
    }
    
    // Test token length (should be 40 characters)
    if (strlen($csrfToken) === 40) {
        echo "   ✅ CSRF Token Length: VALID (40 chars)\n";
        $testResults['csrf_length'] = true;
    } else {
        echo "   ❌ CSRF Token Length: INVALID (" . strlen($csrfToken) . " chars)\n";
        $errors[] = "CSRF token has incorrect length";
        $testResults['csrf_length'] = false;
    }
    
} catch (Exception $e) {
    echo "   ❌ CSRF Token Error: " . $e->getMessage() . "\n";
    $errors[] = "CSRF token generation error: " . $e->getMessage();
    $testResults['csrf_system'] = false;
}

echo "\n🧪 TEST 3: User Authentication System\n";
echo str_repeat("-", 50) . "\n";

try {
    // Check if users exist
    $userCount = User::count();
    echo "   Total Users: {$userCount}\n";
    
    if ($userCount > 0) {
        // Get first admin user for testing
        $testUser = User::whereHas('roles', function($query) {
            $query->where('name', 'Admin');
        })->first();
        
        if ($testUser) {
            echo "   Test User Found: {$testUser->email}\n";
            echo "   ✅ User System: OPERATIONAL\n";
            $testResults['user_system'] = true;
            
            // Test password hashing
            $testPassword = 'test_password_123';
            $hashedPassword = Hash::make($testPassword);
            $passwordCheck = Hash::check($testPassword, $hashedPassword);
            
            if ($passwordCheck) {
                echo "   ✅ Password Hashing: WORKING\n";
                $testResults['password_hashing'] = true;
            } else {
                echo "   ❌ Password Hashing: FAILED\n";
                $errors[] = "Password hashing system not working";
                $testResults['password_hashing'] = false;
            }
            
        } else {
            echo "   ⚠️  No admin user found for testing\n";
            $testResults['admin_user'] = false;
        }
    } else {
        echo "   ❌ No users found in database\n";
        $errors[] = "No users exist in the system";
        $testResults['user_system'] = false;
    }
    
} catch (Exception $e) {
    echo "   ❌ User System Error: " . $e->getMessage() . "\n";
    $errors[] = "User authentication system error: " . $e->getMessage();
    $testResults['user_system'] = false;
}

echo "\n🧪 TEST 4: Middleware Configuration\n";
echo str_repeat("-", 50) . "\n";

try {
    // Check if CSRF middleware is properly configured
    $middlewareConfig = file_get_contents(base_path('bootstrap/app.php'));
    
    if (strpos($middlewareConfig, 'validateCsrfTokens') !== false) {
        echo "   ✅ CSRF Middleware: CONFIGURED\n";
        $testResults['csrf_middleware'] = true;
    } else {
        echo "   ❌ CSRF Middleware: NOT FOUND\n";
        $errors[] = "CSRF middleware not configured";
        $testResults['csrf_middleware'] = false;
    }
    
    // Check for authentication middleware
    if (strpos($middlewareConfig, 'redirectGuestsTo') !== false) {
        echo "   ✅ Auth Middleware: CONFIGURED\n";
        $testResults['auth_middleware'] = true;
    } else {
        echo "   ❌ Auth Middleware: NOT FOUND\n";
        $errors[] = "Authentication middleware not configured";
        $testResults['auth_middleware'] = false;
    }
    
} catch (Exception $e) {
    echo "   ❌ Middleware Check Error: " . $e->getMessage() . "\n";
    $errors[] = "Middleware configuration error: " . $e->getMessage();
}

echo "\n🧪 TEST 5: Environment Configuration\n";
echo str_repeat("-", 50) . "\n";

// Check critical environment variables
$criticalEnvVars = [
    'APP_KEY' => config('app.key'),
    'APP_URL' => config('app.url'),
    'DB_CONNECTION' => config('database.default'),
    'SESSION_DRIVER' => config('session.driver'),
    'SESSION_DOMAIN' => config('session.domain'),
];

foreach ($criticalEnvVars as $var => $value) {
    if (!empty($value) || $var === 'SESSION_DOMAIN') {
        echo "   ✅ {$var}: " . ($value ?: 'null') . "\n";
    } else {
        echo "   ❌ {$var}: MISSING\n";
        $errors[] = "{$var} is not configured";
    }
}

echo "\n📊 TEST SUMMARY\n";
echo str_repeat("=", 50) . "\n";

$totalTests = count($testResults);
$passedTests = count(array_filter($testResults));
$failedTests = $totalTests - $passedTests;

echo "Total Tests: {$totalTests}\n";
echo "Passed: {$passedTests}\n";
echo "Failed: {$failedTests}\n";

if ($failedTests === 0) {
    echo "\n🎉 ALL TESTS PASSED!\n";
    echo "Your authentication system should work correctly on shared hosting.\n";
} else {
    echo "\n❌ SOME TESTS FAILED\n";
    echo "Issues that need to be addressed:\n";
    foreach ($errors as $i => $error) {
        echo "   " . ($i + 1) . ". {$error}\n";
    }
}

echo "\n🔧 RECOMMENDED ACTIONS:\n";
echo str_repeat("-", 50) . "\n";

if (in_array(false, $testResults)) {
    echo "1. Fix the failed tests above\n";
    echo "2. Run: php artisan config:clear\n";
    echo "3. Run: php artisan config:cache\n";
    echo "4. Test login functionality manually\n";
} else {
    echo "1. Deploy to shared hosting with current configuration\n";
    echo "2. Update .env with production values\n";
    echo "3. Run optimization commands on shared hosting\n";
    echo "4. Test login functionality on live site\n";
}

echo "\n✅ Test completed successfully!\n";
