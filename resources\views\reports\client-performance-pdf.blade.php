<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Client Performance Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 20px;
            color: #666;
            margin-bottom: 10px;
        }
        .date-range {
            font-size: 14px;
            color: #888;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .metric-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            text-align: center;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 12px;
        }
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        .table th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        .table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $company_name ?? 'DCF - Digital Clearing and Forwarding Agency' }}</div>
        <div class="report-title">{{ $report_title ?? 'Client Performance Report' }}</div>
        <div class="date-range">Period: {{ $start_date }} to {{ $end_date }}</div>
    </div>

    @if(isset($summary_stats))
    <div class="section">
        <div class="section-title">Summary Statistics</div>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-label">Total Clients</div>
                <div class="metric-value">{{ $summary_stats['total_clients'] ?? 0 }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Revenue</div>
                <div class="metric-value">${{ number_format($summary_stats['total_revenue'] ?? 0, 2) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Outstanding</div>
                <div class="metric-value">${{ number_format($summary_stats['total_outstanding'] ?? 0, 2) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Avg Payment Ratio</div>
                <div class="metric-value">{{ number_format($summary_stats['avg_payment_ratio'] ?? 0, 2) }}%</div>
            </div>
        </div>
    </div>
    @endif

    @if(isset($clients) && count($clients) > 0)
    <div class="section">
        <div class="section-title">Client Performance Details</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Client Name</th>
                    <th>Email</th>
                    <th>Total Invoices</th>
                    <th>Invoice Amount</th>
                    <th>Payments</th>
                    <th>Outstanding</th>
                    <th>Payment Ratio</th>
                    <th>Avg Invoice</th>
                </tr>
            </thead>
            <tbody>
                @foreach($clients as $client)
                <tr>
                    <td>{{ $client['client_name'] ?? 'Unknown' }}</td>
                    <td>{{ $client['client_email'] ?? 'N/A' }}</td>
                    <td>{{ $client['total_invoices'] ?? 0 }}</td>
                    <td>${{ number_format($client['total_invoice_amount'] ?? 0, 2) }}</td>
                    <td>${{ number_format($client['total_payments'] ?? 0, 2) }}</td>
                    <td>${{ number_format($client['outstanding_amount'] ?? 0, 2) }}</td>
                    <td>{{ number_format($client['payment_ratio'] ?? 0, 2) }}%</td>
                    <td>${{ number_format($client['avg_invoice_value'] ?? 0, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p>Generated on {{ $generated_at }}</p>
        <p>This report is confidential and intended for internal use only.</p>
    </div>
</body>
</html>
