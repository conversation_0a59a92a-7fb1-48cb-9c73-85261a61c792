<?php

namespace App\Filament\Resources\SystemNotificationResource\Pages;

use App\Filament\Resources\SystemNotificationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use App\Models\SystemNotification;
use App\Models\User;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DateTimePicker;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;

class ListSystemNotifications extends ListRecords
{
    protected static string $resource = SystemNotificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            
            Action::make('broadcast_notification')
                ->label('Broadcast to All')
                ->icon('heroicon-o-megaphone')
                ->color('info')
                ->modalHeading('Broadcast Notification to All Users')
                ->modalWidth(MaxWidth::TwoExtraLarge)
                ->form([
                    TextInput::make('title')
                        ->required()
                        ->maxLength(255),
                    
                    Textarea::make('message')
                        ->required()
                        ->rows(4),
                    
                    Select::make('type')
                        ->options([
                            'info' => 'Info',
                            'success' => 'Success',
                            'warning' => 'Warning',
                            'error' => 'Error',
                        ])
                        ->default('info')
                        ->required(),
                    
                    Select::make('priority')
                        ->options([
                            'low' => 'Low',
                            'medium' => 'Medium',
                            'high' => 'High',
                        ])
                        ->default('medium')
                        ->required(),
                    
                    TextInput::make('action_url')
                        ->label('Action URL')
                        ->url(),
                    
                    TextInput::make('action_text')
                        ->label('Action Text'),
                    
                    DateTimePicker::make('expires_at')
                        ->label('Expires At'),
                ])
                ->action(function (array $data): void {
                    $count = SystemNotification::createForAllUsers(
                        $data['title'],
                        $data['message'],
                        $data['type'],
                        $data['priority'],
                        $data['action_url'] ?? null,
                        $data['action_text'] ?? null,
                        [],
                        $data['expires_at'] ?? null
                    );
                    
                    Notification::make()
                        ->title("Broadcast notification sent to {$count} users")
                        ->success()
                        ->send();
                }),
            
            Action::make('cleanup_expired')
                ->label('Cleanup Expired')
                ->icon('heroicon-o-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Cleanup Expired Notifications')
                ->modalDescription('This will delete all expired notifications. This action cannot be undone.')
                ->action(function (): void {
                    $deleted = SystemNotification::cleanupExpired();
                    
                    Notification::make()
                        ->title("Deleted {$deleted} expired notifications")
                        ->success()
                        ->send();
                }),
        ];
    }
}
