<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TaxSummaryExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $taxData;
    protected string $startDate;
    protected string $endDate;

    public function __construct(array $taxData, string $startDate, string $endDate)
    {
        $this->taxData = $taxData;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->taxData as $tax) {
            $data[] = [
                $tax['tax_name'] ?? 'Unknown Tax',
                number_format($tax['tax_rate'] ?? 0, 2) . '%',
                number_format($tax['taxable_amount'] ?? 0, 2),
                number_format($tax['tax_amount'] ?? 0, 2),
                $tax['invoice_count'] ?? 0,
                number_format($tax['percentage_of_total'] ?? 0, 2) . '%',
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Tax Name',
            'Tax Rate',
            'Taxable Amount',
            'Tax Amount',
            'Invoice Count',
            'Percentage of Total',
        ];
    }

    public function title(): string
    {
        return 'Tax Summary';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '28A745'],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
