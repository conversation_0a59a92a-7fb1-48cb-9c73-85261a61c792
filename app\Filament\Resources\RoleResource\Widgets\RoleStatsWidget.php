<?php

namespace App\Filament\Resources\RoleResource\Widgets;

use App\Services\RoleManagementService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RoleStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $roleManagementService = app(RoleManagementService::class);
        $analytics = $roleManagementService->getUserRoleAnalytics();

        return [
            Stat::make('Total Roles', $analytics['total_roles'])
                ->description('System roles')
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('primary'),

            Stat::make('Total Permissions', Permission::count())
                ->description('Available permissions')
                ->descriptionIcon('heroicon-m-key')
                ->color('info'),

            Stat::make('Users with Roles', $analytics['total_users_with_roles'])
                ->description($analytics['users_without_roles'] . ' users without roles')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Most Used Role', $this->getMostUsedRole())
                ->description('Role with most users')
                ->descriptionIcon('heroicon-m-star')
                ->color('warning'),
        ];
    }

    private function getMostUsedRole(): string
    {
        $role = Role::withCount('users')
            ->orderByDesc('users_count')
            ->first();

        return $role ? "{$role->name} ({$role->users_count})" : 'None';
    }
}
