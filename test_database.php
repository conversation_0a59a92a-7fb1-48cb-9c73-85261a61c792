<?php
/**
 * Emergency Database Connection Test Script
 * Run this to verify database connectivity before starting the application
 */

echo "=== EMERGENCY DATABASE CONNECTION TEST ===\n\n";

// Test 1: Check PHP Extensions
echo "1. Testing PHP Extensions:\n";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'fileinfo'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext} - LOADED\n";
    } else {
        echo "   ❌ {$ext} - MISSING\n";
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    echo "\n🚨 CRITICAL: Missing extensions: " . implode(', ', $missing_extensions) . "\n";
    echo "Please enable these extensions in C:\\xampp\\php\\php.ini\n\n";
}

// Test 2: Database Connection
echo "2. Testing Database Connection:\n";

$host = '127.0.0.1';
$port = '3306';
$database = 'invoicemod';
$username = 'root';
$password = '';

try {
    // Test MySQL connection
    $dsn = "mysql:host={$host};port={$port}";
    $pdo = new PDO($dsn, $username, $password);
    echo "   ✅ MySQL Server Connection - SUCCESS\n";
    
    // Test database existence
    $dsn = "mysql:host={$host};port={$port};dbname={$database}";
    $pdo = new PDO($dsn, $username, $password);
    echo "   ✅ Database '{$database}' Connection - SUCCESS\n";
    
    // Test Laravel configuration
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    $connection = DB::connection();
    $pdo = $connection->getPdo();
    echo "   ✅ Laravel Database Connection - SUCCESS\n";
    
    // Test a simple query
    $result = DB::select('SELECT 1 as test');
    echo "   ✅ Database Query Test - SUCCESS\n";
    
} catch (PDOException $e) {
    echo "   ❌ Database Connection - FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "\n🔧 FIX: Database 'invoicemod' doesn't exist.\n";
        echo "Run this SQL command in phpMyAdmin or MySQL:\n";
        echo "CREATE DATABASE invoicemod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n\n";
    }
} catch (Exception $e) {
    echo "   ❌ Laravel Connection - FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n";
}

// Test 3: File Permissions
echo "3. Testing File Permissions:\n";
$directories = ['storage', 'bootstrap/cache'];

foreach ($directories as $dir) {
    if (is_writable($dir)) {
        echo "   ✅ {$dir} - WRITABLE\n";
    } else {
        echo "   ❌ {$dir} - NOT WRITABLE\n";
        echo "   Fix: Run 'icacls {$dir} /grant Everyone:(OI)(CI)F /T'\n";
    }
}

// Test 4: Environment Configuration
echo "4. Testing Environment Configuration:\n";
if (file_exists('.env')) {
    echo "   ✅ .env file - EXISTS\n";
    
    $env_content = file_get_contents('.env');
    if (strpos($env_content, 'APP_KEY=base64:') !== false) {
        echo "   ✅ APP_KEY - SET\n";
    } else {
        echo "   ❌ APP_KEY - MISSING\n";
        echo "   Fix: Run 'php artisan key:generate'\n";
    }
    
    if (strpos($env_content, 'DB_DATABASE=invoicemod') !== false) {
        echo "   ✅ Database Configuration - CORRECT\n";
    } else {
        echo "   ⚠️  Database Configuration - CHECK MANUALLY\n";
    }
} else {
    echo "   ❌ .env file - MISSING\n";
    echo "   Fix: Copy .env.example to .env\n";
}

echo "\n=== TEST COMPLETE ===\n";

// Summary
if (empty($missing_extensions)) {
    echo "🎉 All PHP extensions are loaded!\n";
} else {
    echo "🚨 Please fix missing PHP extensions first.\n";
}

echo "\nNext steps:\n";
echo "1. Fix any issues shown above\n";
echo "2. Run: C:\\xampp\\php\\php.exe artisan migrate\n";
echo "3. Start application: C:\\xampp\\php\\php.exe artisan serve\n";
echo "4. Or access via: http://localhost:88/invoices_mod/public\n";
