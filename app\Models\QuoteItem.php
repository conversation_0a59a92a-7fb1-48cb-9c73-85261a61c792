<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\QuoteItem
 *
 * @property-read \App\Models\Product $product
 * @property-read int|null $quote_item_tax_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|QuoteItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QuoteItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QuoteItem query()
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\QuoteItemTax[] $quoteItemTax
 *
 * @mixin \Eloquent
 */
class QuoteItem extends Model
{
    use HasFactory;

    /**
     * Validation rules
     *
     * @var array
     */
    public static $rules = [
        'product_id' => 'nullable', // Allow null for custom products
        'quantity' => 'required|regex:/^\d*\.?\d{0,2}$/|min:0.01',
        'price' => 'required|regex:/^\d*\.?\d{0,2}$/|min:0',
    ];

    /**
     * Validation rules
     *
     * @var array
     */
    public static $messages = [
        'product_id.required' => 'The product field is required',
    ];

    protected $table = 'quote_items';

    public $fillable = [
        'quote_id',
        'product_id',
        'product_name',
        'quantity',
        'price',
        'total',
    ];

    protected $casts = [
        'quote_id' => 'integer',
        'product_id' => 'integer',
        'product_name' => 'string',
        'quantity' => 'integer',
        'price' => 'double',
        'total' => 'double',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class, 'product_id');
    }

    public function quoteItemTax(): HasMany
    {
        return $this->hasMany(QuoteItemTax::class);
    }
}
