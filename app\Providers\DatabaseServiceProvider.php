<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PDO;
use Exception;

class DatabaseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Use a more graceful approach that doesn't throw exceptions during bootstrap
        $this->configureOptimalDatabaseConnection();
        $this->setupDatabaseEventListeners();
    }

    /**
     * Configure optimal database connection with graceful fallback
     */
    private function configureOptimalDatabaseConnection(): void
    {
        try {
            // Step 1: Check what's actually available
            $availableDrivers = $this->getAvailableDrivers();

            // Step 2: Try MySQL first (preferred)
            if ($this->isMysqlAvailable($availableDrivers)) {
                if ($this->configureMysqlConnection()) {
                    return; // Success with MySQL
                }
            }

            // Step 3: Force MySQL configuration even if test fails (let <PERSON><PERSON> handle errors later)
            $this->forceDefaultMysqlConfiguration();

        } catch (Exception $e) {
            // Log the error but don't throw - let the application continue
            Log::error('Database configuration warning: ' . $e->getMessage());
            $this->forceDefaultMysqlConfiguration();
        }
    }

    /**
     * Get available PDO drivers safely
     */
    private function getAvailableDrivers(): array
    {
        try {
            if (!extension_loaded('pdo')) {
                return [];
            }
            return PDO::getAvailableDrivers();
        } catch (Exception $e) {
            Log::warning('Could not get PDO drivers: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if MySQL is available
     */
    private function isMysqlAvailable(array $drivers): bool
    {
        return extension_loaded('pdo_mysql') && in_array('mysql', $drivers);
    }

    /**
     * Configure MySQL connection with testing
     */
    private function configureMysqlConnection(): bool
    {
        try {
            // Ensure MySQL is set as default
            Config::set('database.default', 'mysql');

            // Test the connection
            $connection = DB::connection('mysql');
            $pdo = $connection->getPdo();

            // Optimize MySQL settings
            $this->optimizeMysqlConnection();

            Log::info('MySQL connection established and optimized');
            return true;

        } catch (Exception $e) {
            Log::warning('MySQL connection test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Force default MySQL configuration (let Laravel handle connection errors)
     */
    private function forceDefaultMysqlConfiguration(): void
    {
        try {
            // Set MySQL as default regardless of connection status
            Config::set('database.default', 'mysql');

            // Ensure MySQL configuration is properly set
            Config::set('database.connections.mysql', [
                'driver' => 'mysql',
                'url' => env('DB_URL'),
                'host' => env('DB_HOST', '127.0.0.1'),
                'port' => env('DB_PORT', '3306'),
                'database' => env('DB_DATABASE', 'invoicemod'),
                'username' => env('DB_USERNAME', 'root'),
                'password' => env('DB_PASSWORD', ''),
                'unix_socket' => env('DB_SOCKET', ''),
                'charset' => env('DB_CHARSET', 'utf8mb4'),
                'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
                'prefix' => '',
                'prefix_indexes' => true,
                'strict' => false,
                'engine' => null,
                'options' => extension_loaded('pdo_mysql') ? [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 60,
                ] : [],
            ]);

            Log::info('Forced MySQL configuration applied');

        } catch (Exception $e) {
            Log::error('Failed to force MySQL configuration: ' . $e->getMessage());
        }
    }

    /**
     * Optimize MySQL connection settings
     */
    private function optimizeMysqlConnection(): void
    {
        try {
            $connection = DB::connection('mysql');
            $pdo = $connection->getPdo();

            // Set MySQL session variables for better performance
            $pdo->exec("SET SESSION sql_mode = 'TRADITIONAL'");
            $pdo->exec("SET SESSION time_zone = '+00:00'");
            $pdo->exec("SET SESSION wait_timeout = 600");
            $pdo->exec("SET SESSION interactive_timeout = 600");

            Log::info('MySQL connection optimized');

        } catch (Exception $e) {
            Log::warning('MySQL optimization failed: ' . $e->getMessage());
        }
    }

    /**
     * Setup database event listeners
     */
    private function setupDatabaseEventListeners(): void
    {
        // Log slow queries
        DB::listen(function ($query) {
            if ($query->time > 1000) { // Log queries taking more than 1 second
                Log::warning('Slow query detected', [
                    'sql' => $query->sql,
                    'bindings' => $query->bindings,
                    'time' => $query->time . 'ms'
                ]);
            }
        });

        // Handle connection errors gracefully
        DB::connection()->getEventDispatcher()->listen(
            'connection.failed',
            function ($event) {
                Log::error('Database connection failed', [
                    'connection' => $event->connection,
                    'exception' => $event->exception->getMessage()
                ]);
            }
        );
    }

    /**
     * Get database status information
     */
    public static function getDatabaseStatus(): array
    {
        $status = [
            'mysql_pdo_available' => extension_loaded('pdo_mysql'),
            'mysql_driver_available' => in_array('mysql', PDO::getAvailableDrivers()),
            'current_connection' => Config::get('database.default'),
            'connections_tested' => []
        ];

        // Test each connection
        $connections = ['mysql', 'sqlite'];
        foreach ($connections as $connection) {
            try {
                DB::connection($connection)->getPdo();
                $status['connections_tested'][$connection] = 'success';
            } catch (Exception $e) {
                $status['connections_tested'][$connection] = 'failed: ' . $e->getMessage();
            }
        }

        return $status;
    }

    /**
     * Force MySQL connection if available
     */
    public static function forceMysqlConnection(): bool
    {
        try {
            if (extension_loaded('pdo_mysql') && in_array('mysql', PDO::getAvailableDrivers())) {
                Config::set('database.default', 'mysql');
                DB::connection('mysql')->getPdo();
                Log::info('Forced MySQL connection successful');
                return true;
            }
        } catch (Exception $e) {
            Log::error('Force MySQL connection failed: ' . $e->getMessage());
        }
        
        return false;
    }

    /**
     * Migrate to SQLite if MySQL fails
     */
    public static function migrateToSqlite(): bool
    {
        try {
            $sqliteDb = database_path('database.sqlite');

            // Create SQLite database if it doesn't exist
            if (!file_exists($sqliteDb)) {
                $dir = dirname($sqliteDb);
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                }
                touch($sqliteDb);
            }

            // Update configuration
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', $sqliteDb);
            Config::set('database.connections.sqlite.foreign_key_constraints', true);

            // Test connection
            $connection = DB::connection('sqlite');
            $pdo = $connection->getPdo();

            // Enable foreign keys for SQLite
            $pdo->exec('PRAGMA foreign_keys = ON');

            Log::info('Successfully migrated to SQLite database');
            return true;

        } catch (Exception $e) {
            Log::error('SQLite migration failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Optimize MySQL connection
     */
    private function optimizeMysqlConnection(): void
    {
        try {
            $connection = DB::connection('mysql');
            $pdo = $connection->getPdo();

            // Set MySQL session variables for better performance
            $pdo->exec("SET SESSION sql_mode = 'TRADITIONAL'");
            $pdo->exec("SET SESSION time_zone = '+00:00'");
            $pdo->exec("SET SESSION wait_timeout = 600");
            $pdo->exec("SET SESSION interactive_timeout = 600");

        } catch (Exception $e) {
            Log::warning('MySQL optimization failed: ' . $e->getMessage());
        }
    }

    /**
     * Configure emergency in-memory database
     */
    private function configureEmergencyDatabase(): void
    {
        try {
            Config::set('database.default', 'sqlite');
            Config::set('database.connections.sqlite.database', ':memory:');
            Config::set('database.connections.sqlite.foreign_key_constraints', false);

            $connection = DB::connection('sqlite');
            $connection->getPdo();

            Log::warning('Emergency in-memory database activated');

        } catch (Exception $e) {
            Log::error('Emergency database configuration failed: ' . $e->getMessage());
            throw $e;
        }
    }
}
