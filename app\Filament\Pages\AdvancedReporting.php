<?php

namespace App\Filament\Pages;

use App\Services\AdvancedReportingService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\View\View;

class AdvancedReporting extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Advanced Reports';
    protected static ?string $title = 'Advanced Reporting Dashboard';
    protected static string $view = 'filament.pages.advanced-reporting';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'Analytics';

    public ?array $data = [];
    public ?array $reportData = null;

    protected ?AdvancedReportingService $reportingService = null;

    public function boot(AdvancedReportingService $reportingService): void
    {
        $this->reportingService = $reportingService;
    }

    protected function getReportingService(): AdvancedReportingService
    {
        if ($this->reportingService === null) {
            $this->reportingService = app(AdvancedReportingService::class);
        }
        return $this->reportingService;
    }

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => Carbon::now()->startOfMonth(),
            'end_date' => Carbon::now()->endOfMonth(),
            'report_type' => 'financial_overview',
        ]);

        $this->generateReport();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DatePicker::make('start_date')
                    ->label('Start Date')
                    ->default(Carbon::now()->startOfMonth())
                    ->required()
                    ->reactive(),
                
                DatePicker::make('end_date')
                    ->label('End Date')
                    ->default(Carbon::now()->endOfMonth())
                    ->required()
                    ->reactive(),
                
                Select::make('report_type')
                    ->label('Report Type')
                    ->options([
                        'financial_overview' => 'Financial Overview',
                        'revenue_analysis' => 'Revenue Analysis',
                        'client_analytics' => 'Client Analytics',
                        'payment_analysis' => 'Payment Analysis',
                        'invoice_metrics' => 'Invoice Metrics',
                    ])
                    ->default('financial_overview')
                    ->required()
                    ->reactive(),
            ])
            ->statePath('data')
            ->live();
    }

    public function generateReport(): void
    {
        $filters = [
            'start_date' => Carbon::parse($this->data['start_date'] ?? Carbon::now()->startOfMonth()),
            'end_date' => Carbon::parse($this->data['end_date'] ?? Carbon::now()->endOfMonth()),
        ];

        $reportType = $this->data['report_type'] ?? 'financial_overview';

        switch ($reportType) {
            case 'financial_overview':
                $this->reportData = $this->getReportingService()->getFinancialOverview($filters);
                break;
            case 'revenue_analysis':
                $this->reportData = [
                    'revenue' => $this->getReportingService()->getRevenueMetrics($filters['start_date'], $filters['end_date'])
                ];
                break;
            case 'client_analytics':
                $this->reportData = [
                    'clients' => $this->getReportingService()->getClientMetrics($filters['start_date'], $filters['end_date'])
                ];
                break;
            case 'payment_analysis':
                $this->reportData = [
                    'payments' => $this->getReportingService()->getPaymentMetrics($filters['start_date'], $filters['end_date'])
                ];
                break;
            case 'invoice_metrics':
                $this->reportData = [
                    'invoices' => $this->getReportingService()->getInvoiceMetrics($filters['start_date'], $filters['end_date'])
                ];
                break;
        }
    }

    public function updatedData(): void
    {
        $this->generateReport();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('export_pdf')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('danger')
                ->action(function () {
                    $this->exportReport('pdf');
                }),
            
            Action::make('export_excel')
                ->label('Export Excel')
                ->icon('heroicon-o-table-cells')
                ->color('success')
                ->action(function () {
                    $this->exportReport('excel');
                }),
            
            Action::make('export_csv')
                ->label('Export CSV')
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->action(function () {
                    $this->exportReport('csv');
                }),
            
            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->generateReport();
                    $this->dispatch('refresh-charts');
                }),
        ];
    }

    public function exportReport(string $format): void
    {
        try {
            $reportType = $this->data['report_type'] ?? 'financial_overview';
            $filePath = $this->getReportingService()->exportReport($reportType, $this->reportData, $format);
            
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => "Report exported successfully as {$format}",
            ]);
            
            // Trigger download
            $this->dispatch('download-file', ['path' => $filePath]);
            
        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Export failed: ' . $e->getMessage(),
            ]);
        }
    }

    public function getRevenueChartData(): array
    {
        if (!$this->reportData || !isset($this->reportData['revenue']['monthly_breakdown'])) {
            return [];
        }

        $data = $this->reportData['revenue']['monthly_breakdown'];
        
        return [
            'labels' => $data->pluck('period')->toArray(),
            'datasets' => [
                [
                    'label' => 'Revenue',
                    'data' => $data->pluck('revenue')->toArray(),
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Invoice Count',
                    'data' => $data->pluck('invoice_count')->toArray(),
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'borderColor' => 'rgb(16, 185, 129)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'yAxisID' => 'y1',
                ]
            ]
        ];
    }

    public function getInvoiceStatusChartData(): array
    {
        if (!$this->reportData || !isset($this->reportData['invoices']['status_distribution'])) {
            return [];
        }

        $statusData = $this->reportData['invoices']['status_distribution'];
        
        return [
            'labels' => array_keys($statusData->toArray()),
            'datasets' => [
                [
                    'data' => array_values($statusData->toArray()),
                    'backgroundColor' => [
                        'rgba(34, 197, 94, 0.8)',   // paid - green
                        'rgba(59, 130, 246, 0.8)',  // sent - blue
                        'rgba(245, 158, 11, 0.8)',  // pending - yellow
                        'rgba(239, 68, 68, 0.8)',   // overdue - red
                        'rgba(107, 114, 128, 0.8)', // draft - gray
                    ],
                    'borderWidth' => 2,
                    'borderColor' => '#ffffff',
                ]
            ]
        ];
    }

    public function getPaymentMethodsChartData(): array
    {
        if (!$this->reportData || !isset($this->reportData['payments']['payment_methods'])) {
            return [];
        }

        $paymentMethods = $this->reportData['payments']['payment_methods'];
        
        return [
            'labels' => $paymentMethods->pluck('payment_method')->toArray(),
            'datasets' => [
                [
                    'data' => $paymentMethods->pluck('total_amount')->toArray(),
                    'backgroundColor' => [
                        'rgba(99, 102, 241, 0.8)',  // stripe - indigo
                        'rgba(245, 158, 11, 0.8)',  // paypal - yellow
                        'rgba(34, 197, 94, 0.8)',   // bank - green
                        'rgba(239, 68, 68, 0.8)',   // cash - red
                        'rgba(168, 85, 247, 0.8)',  // other - purple
                    ],
                    'borderWidth' => 2,
                    'borderColor' => '#ffffff',
                ]
            ]
        ];
    }

    public function getTopClientsData(): array
    {
        if (!$this->reportData || !isset($this->reportData['revenue']['top_clients'])) {
            return [];
        }

        return $this->reportData['revenue']['top_clients']->map(function ($client) {
            return [
                'name' => $client->name,
                'revenue' => number_format($client->total_revenue, 2),
                'percentage' => $this->calculateClientPercentage($client->total_revenue),
            ];
        })->toArray();
    }

    private function calculateClientPercentage(float $clientRevenue): float
    {
        $totalRevenue = $this->reportData['revenue']['total_revenue'] ?? 1;
        return $totalRevenue > 0 ? round(($clientRevenue / $totalRevenue) * 100, 1) : 0;
    }

    public function getKpiMetrics(): array
    {
        if (!$this->reportData) {
            return [];
        }

        $metrics = [];

        // Revenue metrics
        if (isset($this->reportData['revenue'])) {
            $revenue = $this->reportData['revenue'];
            $metrics['total_revenue'] = [
                'value' => number_format($revenue['total_revenue'], 2),
                'growth' => $revenue['growth_rate'],
                'label' => 'Total Revenue',
                'icon' => 'heroicon-o-currency-dollar',
                'color' => $revenue['growth_rate'] >= 0 ? 'success' : 'danger',
            ];
        }

        // Invoice metrics
        if (isset($this->reportData['invoices'])) {
            $invoices = $this->reportData['invoices'];
            $metrics['payment_rate'] = [
                'value' => $invoices['payment_rate'] . '%',
                'growth' => null,
                'label' => 'Payment Rate',
                'icon' => 'heroicon-o-check-circle',
                'color' => $invoices['payment_rate'] >= 80 ? 'success' : ($invoices['payment_rate'] >= 60 ? 'warning' : 'danger'),
            ];
        }

        // Client metrics
        if (isset($this->reportData['clients'])) {
            $clients = $this->reportData['clients'];
            $metrics['active_clients'] = [
                'value' => number_format($clients['active_clients']),
                'growth' => null,
                'label' => 'Active Clients',
                'icon' => 'heroicon-o-users',
                'color' => 'info',
            ];
        }

        // Payment metrics
        if (isset($this->reportData['payments'])) {
            $payments = $this->reportData['payments'];
            $metrics['avg_payment'] = [
                'value' => number_format($payments['average_payment_amount'], 2),
                'growth' => null,
                'label' => 'Avg Payment',
                'icon' => 'heroicon-o-banknotes',
                'color' => 'success',
            ];
        }

        return $metrics;
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
