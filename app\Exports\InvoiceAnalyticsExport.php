<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class InvoiceAnalyticsExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $invoiceData;
    protected string $startDate;
    protected string $endDate;

    public function __construct(array $invoiceData, string $startDate, string $endDate)
    {
        $this->invoiceData = $invoiceData;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->invoiceData as $invoice) {
            $data[] = [
                $invoice['invoice_id'] ?? 'Unknown',
                $invoice['client_name'] ?? 'Unknown Client',
                $invoice['invoice_date'] ?? 'N/A',
                $invoice['due_date'] ?? 'N/A',
                number_format($invoice['total_amount'] ?? 0, 2),
                number_format($invoice['paid_amount'] ?? 0, 2),
                number_format($invoice['outstanding_amount'] ?? 0, 2),
                $invoice['status'] ?? 'Unknown',
                $invoice['days_overdue'] ?? 0,
                $invoice['payment_method'] ?? 'N/A',
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Invoice ID',
            'Client Name',
            'Invoice Date',
            'Due Date',
            'Total Amount',
            'Paid Amount',
            'Outstanding Amount',
            'Status',
            'Days Overdue',
            'Payment Method',
        ];
    }

    public function title(): string
    {
        return 'Invoice Analytics';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
