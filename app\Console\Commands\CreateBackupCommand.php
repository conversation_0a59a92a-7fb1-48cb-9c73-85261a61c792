<?php

namespace App\Console\Commands;

use App\Services\DatabaseBackupService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CreateBackupCommand extends Command
{
    protected $signature = 'backup:create 
                            {--name= : Custom backup name}
                            {--database : Include database}
                            {--config : Include configuration files}
                            {--storage : Include storage files}
                            {--all : Include everything (default)}';

    protected $description = 'Create a system backup';

    protected DatabaseBackupService $backupService;

    public function __construct(DatabaseBackupService $backupService)
    {
        parent::__construct();
        $this->backupService = $backupService;
    }

    public function handle(): int
    {
        $this->info('🚀 Starting backup creation...');
        $this->newLine();

        // Determine what to include
        $includeAll = $this->option('all') || (!$this->option('database') && !$this->option('config') && !$this->option('storage'));
        
        $options = [
            'name' => $this->option('name') ?: 'cli_backup_' . Carbon::now()->format('Y_m_d_H_i_s'),
            'include_database' => $includeAll || $this->option('database'),
            'include_config' => $includeAll || $this->option('config'),
            'include_storage' => $includeAll || $this->option('storage'),
        ];

        // Show what will be included
        $this->table(['Component', 'Included'], [
            ['Database', $options['include_database'] ? '✅ Yes' : '❌ No'],
            ['Configuration', $options['include_config'] ? '✅ Yes' : '❌ No'],
            ['Storage Files', $options['include_storage'] ? '✅ Yes' : '❌ No'],
        ]);

        $this->newLine();

        if (!$this->confirm('Proceed with backup creation?', true)) {
            $this->warn('Backup cancelled.');
            return self::FAILURE;
        }

        // Create progress bar
        $progressBar = $this->output->createProgressBar(4);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('Initializing...');
        $progressBar->start();

        try {
            $progressBar->setMessage('Creating backup...');
            $progressBar->advance();

            $result = $this->backupService->createFullBackup($options);

            if ($result['success']) {
                $progressBar->setMessage('Backup completed successfully!');
                $progressBar->finish();
                $this->newLine(2);

                $this->info('✅ Backup created successfully!');
                $this->newLine();

                // Show backup details
                $this->table(['Detail', 'Value'], [
                    ['Backup Name', $result['backup_name']],
                    ['Created At', $result['timestamp']],
                    ['File Size', $this->formatBytes($result['file_size'] ?? 0)],
                    ['Components', implode(', ', array_keys($result['components']))],
                ]);

                $this->info('📁 Backup saved to: storage/app/backups/');
                $this->newLine();

                return self::SUCCESS;
            } else {
                $progressBar->setMessage('Backup failed!');
                $progressBar->finish();
                $this->newLine(2);

                $this->error('❌ Backup failed: ' . ($result['error'] ?? 'Unknown error'));
                return self::FAILURE;
            }

        } catch (\Exception $e) {
            $progressBar->setMessage('Error occurred!');
            $progressBar->finish();
            $this->newLine(2);

            $this->error('❌ Backup error: ' . $e->getMessage());
            return self::FAILURE;
        }
    }

    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
