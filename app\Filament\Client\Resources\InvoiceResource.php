<?php

namespace App\Filament\Client\Resources;

use App\Models\Tax;
use Filament\Forms;
use App\Models\User;
use App\Models\Quote;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Currency;
use Filament\Forms\Form;
use App\Models\PaymentQrCode;
use App\Models\InvoiceItemTax;
use App\Models\InvoiceSetting;
use Filament\Resources\Resource;
use Filament\Forms\Components\Group;
use App\AdminDashboardSidebarSorting;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Textarea;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Actions\Action;
use Livewire\Features\SupportEvents\HandlesEvents;
use App\Filament\Client\Resources\InvoiceResource\Pages;
use App\Services\InvoicePerformanceService;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?int $navigationSort = AdminDashboardSidebarSorting::INVOICES->value;

    public static function getNavigationLabel(): string
    {
        return __('messages.invoices');
    }

    public static function getModelLabel(): string
    {
        return __('messages.invoice.invoice');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Basic Details')
                    ->schema([
                        Select::make('client_id')
                            ->label(__('messages.invoice.client') . ':')
                            ->validationAttribute(__('messages.invoice.client'))
                            ->afterStateHydrated(function ($record, $set, $operation) {
                                if ($operation === 'edit') {
                                    $set('client_id', $record->client->user->id);
                                }
                            })
                            ->options(fn() => InvoicePerformanceService::getCachedClientOptions())
                            ->getSearchResultsUsing(fn(string $search) => InvoicePerformanceService::searchClients($search))
                            ->native(false)
                            ->searchable()
                            ->required(),

                        TextInput::make('invoice_id')
                            ->label(__('messages.invoice.invoice_number') . ':')
                            ->validationAttribute(__('messages.invoice.invoice_number'))
                            ->default(self::getModel()::generateUniqueInvoiceId())
                            ->disabledOn('edit')
                            ->extraInputAttributes([
                                'oninput' => "if (/[^a-zA-Z0-9]/.test(this.value)) {
                                                this.value = '" . self::getModel()::generateUniqueInvoiceId() . "';
                                            } else {
                                        this.value = this.value.toUpperCase();
                                    }"
                            ])
                            ->required()
                            ->rules(fn($operation) => $operation === 'edit' ? [] : ['regex:/^[a-zA-Z0-9]+$/'])
                            ->maxLength(fn($operation) => $operation === 'edit' ? null : 6)
                            ->prefix(fn($operation) => $operation != 'edit' ? (getInvoiceNoPrefix() ?: null) : null)
                            ->suffix(fn($operation) => $operation != 'edit' ? (getInvoiceNoSuffix() ?: null) : null),

                        DatePicker::make('invoice_date')
                            ->required()
                            ->validationAttribute(__('messages.quote.quote_date'))
                            ->placeholder(__('messages.quote.quote_date'))
                            ->default(now())
                            ->live()
                            ->label(__('messages.quote.quote_date') . ':')
                            ->native(false),

                        DatePicker::make('due_date')
                            ->required()
                            ->validationAttribute(__('messages.quote.due_date'))
                            ->default(now()->addDays(1))
                            ->placeholder(__('messages.quote.due_date'))
                            ->label(__('messages.quote.due_date') . ':')
                            ->native(false),

                        Select::make('status')
                            ->required()
                            ->validationAttribute(__('messages.common.status'))
                            ->label(__('messages.common.status') . ':')
                            ->default(Invoice::UNPAID)
                            ->options(getTranslatedData(Invoice::STATUS_ARR))
                            ->native(false),

                        // US-006: General invoice description header field
                        RichEditor::make('description')
                            ->label(__('Invoice Description') . ':')
                            ->placeholder(__('Enter general invoice description (optional)'))
                            ->maxLength(1000)
                            ->helperText('General description for this invoice (max 1000 characters)')
                            ->columnSpanFull()
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'bulletList',
                                'orderedList',
                            ]),

                        Select::make('template_id')
                            ->label(__('messages.setting.invoice_template') . ':')
                            ->required()
                            ->searchable()
                            ->native(false)
                            ->live()
                            ->default(getInvoiceSettingTemplateId())
                            ->validationAttribute(__('messages.setting.invoice_template'))
                            ->options(InvoiceSetting::toBase()->pluck('template_name', 'id')->toArray()),

                        Select::make('payment_qr_code_id')
                            ->label(__('messages.payment_qr_codes.payment_qr_code') . ':')
                            ->native(false)
                            ->validationAttribute(__('messages.payment_qr_codes.payment_qr_code'))
                            ->default(PaymentQrCode::whereIsDefault(true)->value('id') ?? null)
                            ->options(PaymentQrCode::pluck('title', 'id')->toArray() ?? null),

                        Select::make('currency_id')
                            ->label(__('messages.setting.currencies') . ':')
                            ->native(false)
                            ->optionsLimit(Currency::all()->count())
                            ->searchable()
                            ->options(Currency::all()->mapWithKeys(function ($currency) {
                                return [$currency->id => $currency->icon . ' ' . $currency->name];
                            })),

                        Toggle::make('recurring_status')
                            ->live()
                            ->label(__('messages.invoice.this_is_recurring_invoice')),
                        TextInput::make('recurring_cycle')
                            ->label(__('messages.invoice.recurring_cycle') . ':')
                            ->validationAttribute(__('messages.invoice.recurring_cycle'))
                            ->numeric()
                            ->extraInputAttributes(['oninput' => "this.value = this.value.replace(/[e\+\-]/gi, '')"])
                            ->hidden(fn($get) => !$get('recurring_status'))
                            ->placeholder(__('messages.flash.number_of_days_for_recurring_cycle')),
                    ])->columns(3),

                // US-007: Comprehensive shipment details section (moved before services for logistics priority)
                Section::make('Shipment Information')
                    ->description('Primary shipment details for logistics operations')
                    ->collapsible()
                    ->schema([
                        Group::make([
                            TextInput::make('shipment_weight')
                                ->label(__('Weight') . ':')
                                ->placeholder('0.00')
                                ->numeric()
                                ->step(0.01)
                                ->suffix(fn($get) => $get('shipment_weight_unit') ?? 'kg'),

                            Select::make('shipment_weight_unit')
                                ->label(__('Weight Unit') . ':')
                                ->options([
                                    'kg' => 'Kilograms (kg)',
                                    'lbs' => 'Pounds (lbs)',
                                    'g' => 'Grams (g)',
                                    'oz' => 'Ounces (oz)',
                                    't' => 'Tonnes (t)',
                                ])
                                ->default('kg')
                                ->native(false),
                        ])->columns(2),

                        Group::make([
                            Select::make('transportation_type')
                                ->label(__('Transportation Type') . ':')
                                ->placeholder('Select transportation method')
                                ->options([
                                    'air' => 'Air Freight',
                                    'sea' => 'Sea Freight',
                                    'land' => 'Land Transport',
                                    'rail' => 'Rail Transport',
                                    'courier' => 'Courier Service',
                                    'pickup' => 'Customer Pickup',
                                ])
                                ->searchable()
                                ->native(false),

                            TextInput::make('package_quantity')
                                ->label(__('Number of Packages') . ':')
                                ->placeholder('1')
                                ->numeric()
                                ->minValue(1)
                                ->default(1),
                        ])->columns(2),

                        Group::make([
                            TextInput::make('origin_location')
                                ->label(__('Origin Location') . ':')
                                ->placeholder('Enter origin city/address')
                                ->maxLength(255),

                            TextInput::make('destination_location')
                                ->label(__('Destination Location') . ':')
                                ->placeholder('Enter destination city/address')
                                ->maxLength(255),
                        ])->columns(2),

                        TextInput::make('tracking_number')
                            ->label(__('Tracking Number') . ':')
                            ->placeholder('Enter tracking number (if available)')
                            ->maxLength(100)
                            ->columnSpanFull(),
                    ])->columns(2),

                Section::make('Service Details')
                    ->schema([
                        TextInput::make('product_details')
                            ->visible(false)
                            ->dehydrated(false),
                        Forms\Components\Repeater::make('invoiceItems')
                            ->label('')
                            ->relationship('invoiceItems')
                            ->saveRelationshipsUsing(static function (Repeater $component, HasForms $livewire, ?array $state) {
                                if (!is_array($state)) {
                                    $state = [];
                                }

                                $relationship = $component->getRelationship();
                                $existingRecords = $component->getCachedExistingRecords();
                                $recordsToDelete = [];

                                foreach ($existingRecords->pluck($relationship->getRelated()->getKeyName()) as $keyToCheckForDeletion) {
                                    if (array_key_exists("record-{$keyToCheckForDeletion}", $state)) {
                                        continue;
                                    }

                                    $recordsToDelete[] = $keyToCheckForDeletion;
                                    $existingRecords->forget("record-{$keyToCheckForDeletion}");
                                }

                                $relationship
                                    ->whereKey($recordsToDelete)
                                    ->get()
                                    ->each(static fn(Model $record) => $record->delete());

                                $childComponentContainers = $component->getChildComponentContainers(
                                    withHidden: $component->shouldSaveRelationshipsWhenHidden(),
                                );

                                $itemOrder = 1;
                                $orderColumn = $component->getOrderColumn();
                                $translatableContentDriver = $livewire->makeFilamentTranslatableContentDriver();

                                foreach ($childComponentContainers as $itemKey => $item) {

                                    $itemData = $item->getState(shouldCallHooksBefore: false);

                                    // Check if product_name corresponds to an existing product
                                    $productName = $itemData['product_name'] ?? null;
                                    $existingProduct = null;

                                    if ($productName) {
                                        $existingProduct = Product::where('name', $productName)->first();
                                    }

                                    if ($existingProduct) {
                                        // If product exists in database, store product_id and clear product_name
                                        $itemData['product_id'] = $existingProduct->id;
                                        $itemData['product_name'] = null;
                                    } else {
                                        // If product doesn't exist, it's a free-form entry, store product_name and clear product_id
                                        $itemData['product_id'] = null;
                                        // Keep the product_name as entered by user
                                    }

                                    if ($orderColumn) {
                                        $itemData[$orderColumn] = $itemOrder;
                                        $itemOrder++;
                                    }

                                    $itemData['total'] = floatval($itemData['price'] ?? 0) * floatval($itemData['quantity'] ?? 1);

                                    if ($record = ($existingRecords[$itemKey] ?? null)) {
                                        $itemData = $component->mutateRelationshipDataBeforeSave($itemData, record: $record);

                                        if ($itemData === null) {
                                            continue;
                                        }
                                        $translatableContentDriver
                                            ? $translatableContentDriver->updateRecord($record, $itemData)
                                            : $record->fill($itemData)->save();

                                        InvoiceItemTax::where('invoice_item_id', $record->id)->delete();

                                        if (!empty($itemData['tax_id'])) {
                                            foreach ($itemData['tax_id'] as $taxId) {
                                                $taxValue = Tax::where('id', $taxId)->value('value');

                                                InvoiceItemTax::create([
                                                    'invoice_item_id' => $record->id,
                                                    'tax_id' => $taxId,
                                                    'tax' => $taxValue ?? 0,
                                                ]);
                                            }
                                        }

                                        continue;
                                    }

                                    $relatedModel = $component->getRelatedModel();
                                    $itemData = $component->mutateRelationshipDataBeforeCreate($itemData);

                                    if ($itemData === null) {
                                        continue;
                                    }

                                    if ($translatableContentDriver) {
                                        $record = $translatableContentDriver->makeRecord($relatedModel, $itemData);
                                    } else {
                                        $record = new $relatedModel;
                                        $record->fill($itemData);
                                    }

                                    $record = $relationship->save($record);
                                    $item->model($record)->saveRelationships();
                                    $existingRecords->push($record);

                                    InvoiceItemTax::where('invoice_item_id', $record->id)->delete();

                                    if (!empty($itemData['tax_id'])) {
                                        foreach ($itemData['tax_id'] as $taxId) {
                                            $taxValue = Tax::where('id', $taxId)->value('value');

                                            InvoiceItemTax::create([
                                                'invoice_item_id' => $record->id,
                                                'tax_id' => $taxId,
                                                'tax' => $taxValue ?? 0,
                                            ]);
                                        }
                                    }
                                }

                                $component->getRecord()->setRelation($component->getRelationshipName(), $existingRecords);
                            })
                            ->mutateRelationshipDataBeforeFillUsing(function (array $data) {
                                if (isset($data['id'])) {
                                    $data['tax_id'] = InvoiceItemTax::where('invoice_item_id', $data['id'])->pluck('tax_id')->toArray();
                                }
                                return $data;
                            })
                            ->schema([
                                Select::make('product_name')
                                    ->label(__('messages.product.product') . ':')
                                    ->validationAttribute(__('messages.product.product'))
                                    ->live()
                                    ->required()
                                    ->searchable()
                                    ->allowHtml()
                                    ->placeholder(__('messages.flash.enter_product_or_service_name'))
                                    ->afterStateHydrated(function ($operation, $state, $set, $record) {
                                        if ($operation == 'edit') {
                                            // For existing records, show the product name or get it from product
                                            if (!empty($record->product_name)) {
                                                $set('product_name', $record->product_name);
                                            } elseif (!empty($record->product_id)) {
                                                $product = Product::find($record->product_id);
                                                $set('product_name', $product ? $product->name : '');
                                            }
                                        }
                                    })
                                    ->getSearchResultsUsing(static function (?string $search): array {
                                        if (empty($search)) {
                                            return InvoicePerformanceService::getCachedProductOptions();
                                        }

                                        $searchResults = InvoicePerformanceService::searchProducts($search);

                                        if (!in_array($search, $searchResults)) {
                                            $searchResults = array_merge([$search => $search], $searchResults);
                                        }

                                        return $searchResults;
                                    })
                                    ->columnSpan(2),

                                Textarea::make('description')
                                    ->label(__('messages.common.description') . ':')
                                    ->placeholder(__('messages.flash.enter_product_description'))
                                    ->rows(2)
                                    ->columnSpan(2)
                                    ->debounce(500),
                                Forms\Components\TextInput::make('quantity')
                                    ->label(__('messages.invoice.qty') . ':')
                                    ->validationAttribute(__('messages.invoice.qty'))
                                    ->placeholder(__('messages.invoice.qty'))
                                    ->numeric()
                                    ->extraInputAttributes([
                                        'oninput' => "this.value = this.value.replace(/[e\+\-]/gi, '')",
                                        'step' => '0.01'
                                    ])
                                    ->required()
                                    ->minValue(0)
                                    ->live(onBlur: true)
                                    ->debounce(300)
                                    ->afterStateUpdated(function ($get, $set, $state) {
                                        if ($state && $get('price')) {
                                            $amount = floatval($state) * floatval($get('price'));
                                            $set('amount', number_format($amount, 2));
                                        }
                                    }),

                                Forms\Components\TextInput::make('price')
                                    ->label(__('messages.product.unit_price') . ':')
                                    ->validationAttribute(__('messages.product.unit_price'))
                                    ->placeholder(__('messages.product.unit_price'))
                                    ->numeric()
                                    ->required()
                                    ->extraInputAttributes([
                                        'oninput' => "this.value = this.value.replace(/[e\+\-]/gi, '')",
                                        'step' => '0.01'
                                    ])
                                    ->minValue(0)
                                    ->live(onBlur: true)
                                    ->debounce(300)
                                    ->afterStateUpdated(function ($get, $set, $state) {
                                        if ($state && $get('quantity')) {
                                            $amount = floatval($state) * floatval($get('quantity'));
                                            $set('amount', number_format($amount, 2));
                                        }
                                    }),
                                Forms\Components\Select::make('tax_id')
                                    ->label(__('messages.invoice.tax') . ':')
                                    ->validationAttribute(__('messages.invoice.tax'))
                                    ->multiple()
                                    ->options(fn() => InvoicePerformanceService::getCachedTaxOptions())
                                    ->default(fn() => empty(getDefaultTax()) ? [] : [getDefaultTax()])
                                    ->searchable()
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function ($get, $set) {
                                        // Debounced calculation trigger
                                        self::calculateFinalAmountDebounced($get, $set);
                                    }),

                                Placeholder::make('amount')
                                    ->label(__('messages.quote.amount') . ' :')
                                    ->content(fn($get) => $get('amount') ?? number_format(floatval($get('price') ?? 0) * floatval($get('quantity') ?? 1), 2))
                                    ->default('0.00'),
                            ])
                            ->live(onBlur: true)
                            ->afterStateUpdated(function ($get, $set) {
                                // Use debounced calculation to prevent excessive updates
                                self::calculateFinalAmountDebounced($get, $set);
                            })
                            ->columnSpanFull()
                            ->reorderable()
                            ->addActionLabel(__('messages.product.add_product'))
                            ->deletable(fn($state) => count($state) > 1)
                            ->columns(4),

                    ]),

                Section::make('Summary')
                    ->schema([
                        Group::make([
                            Placeholder::make('documentation')
                                ->hiddenLabel()
                                ->content(''),
                            Group::make([
                                Toggle::make('discount_before_tax')
                                    ->live(onBlur: true)
                                    ->label(__('Discount %(applied before tax)'))
                                    ->afterStateUpdated(fn($get, $set) => self::calculateFinalAmountDebounced($get, $set))
                                    ->disabled(fn($get) => $get('discount_type') == Invoice::SELECT_DISCOUNT_TYPE || $get('discount_type') == Invoice::FIXED),
                                Group::make()
                                    ->schema([
                                        TextInput::make('discount')
                                            ->label(__('messages.invoice.discount') . ':')
                                            ->readOnly(fn($get) => $get('discount_type') == Invoice::SELECT_DISCOUNT_TYPE)
                                            ->numeric()
                                            ->extraInputAttributes([
                                                'oninput' => "this.value = this.value.replace(/[e\+\-]/gi, '')",
                                                'step' => '0.01'
                                            ])
                                            ->live(onBlur: true)
                                            ->debounce(500)
                                            ->afterStateUpdated(function ($set, $get, $state) {
                                                // Validate percentage discount
                                                if ($get('discount_type') == Invoice::PERCENTAGE && $state > 100) {
                                                    $set('discount', 0);
                                                    return;
                                                }
                                                // Trigger debounced calculation
                                                self::calculateFinalAmountDebounced($get, $set);
                                            })
                                            ->placeholder(__('messages.invoice.discount')),

                                        Select::make('discount_type')
                                            ->live()
                                            ->default(0)
                                            ->label(__('messages.invoice.discount_type') . ':')
                                            ->validationAttribute(__('messages.invoice.discount_type'))
                                            ->afterStateUpdated(function ($set, $state, $get) {
                                                // Validate percentage discount
                                                if ($get('discount_type') == Invoice::PERCENTAGE && $get('discount') > 100) {
                                                    $set('discount', 0);
                                                }
                                                // Handle discount before tax toggle
                                                if ($get('discount_type') != Invoice::PERCENTAGE && $get('discount_before_tax') == true) {
                                                    $set('discount_before_tax', false);
                                                }
                                                // Trigger debounced calculation
                                                self::calculateFinalAmountDebounced($get, $set);
                                            })
                                            ->options(getTranslatedData(Invoice::DISCOUNT_TYPE))
                                            ->native(false),

                                        Forms\Components\Select::make('tax2')
                                            ->label(__('messages.invoice.tax') . ':')
                                            ->validationAttribute(__('messages.invoice.tax'))
                                            ->multiple()
                                            ->live()
                                            ->afterStateHydrated(function ($component, $operation, $record) {
                                                if ($operation == 'edit') {
                                                    $component->state($record->invoiceTaxes->pluck('id')->toArray());
                                                }
                                            })
                                            ->afterStateUpdated(fn($get, $set) => self::calculateFinalAmount($get, $set))
                                            ->options(fn() => Tax::all()->pluck('name', 'id')->toArray())
                                            ->optionsLimit(Tax::all()->count())
                                            ->preload()
                                            ->searchable(),
                                    ])->columns(2)
                                    ->extraAttributes(['class' => 'space-x-4'])
                            ])->columns(1),
                            Group::make([
                                Placeholder::make('sub_total')
                                    ->label(__('messages.quote.sub_total') . ' :')
                                    ->inlineLabel()
                                    ->extraAttributes(['class' => 'ms-auto'])
                                    ->content(fn($state) => getCurrencyAmount($state, true))
                                    ->default('0.00'),


                                Placeholder::make('total_discount')
                                    ->label(__('messages.quote.discount') . ' :')
                                    ->inlineLabel()
                                    ->extraAttributes(['class' => 'ms-auto'])
                                    ->content(fn($state) => getCurrencyAmount($state, true))->default('0.00'),

                                Placeholder::make('total_tax')
                                    ->label(__('messages.invoice.tax')  . ' :')
                                    ->inlineLabel()
                                    ->extraAttributes(['class' => 'ms-auto'])
                                    ->content(fn($state) => getCurrencyAmount($state, true))->default('0.00'),

                                Placeholder::make('total')
                                    ->label(__('messages.quote.total') . ' :')
                                    ->inlineLabel()
                                    ->extraAttributes(['class' => 'ms-auto'])
                                    ->default('0.00')
                                    ->content(fn($state) => getCurrencyAmount($state, true))
                            ]),
                        ])->columns(3)->columnSpanFull(),
                        Hidden::make('total_discount'),
                        Hidden::make('total_tax'),
                        Hidden::make('sub_total')->afterStateHydrated(fn($operation, $get, $set, $state) => $operation == 'edit' ? self::calculateFinalAmount($get, $set) : $set('sub_total', $state)),
                        Hidden::make('total'),
                        Actions::make([
                            Action::make('add_note_term')
                                ->icon('heroicon-o-plus')
                                ->label(__('messages.quote.add_note_term'))
                                ->hidden(fn($get) => $get('open_term') || !empty($get('note')) || !empty($get('term')))
                                ->action(function ($set) {
                                    $set('note', '');
                                    $set('term', '');
                                    $set('open_term', true);
                                })
                                ->color('primary'),

                            Action::make('remove_note_term')
                                ->icon('heroicon-o-minus')
                                ->label(__('messages.quote.remove_note_term'))
                                ->hidden(fn($get) => (!$get('open_term') && empty($get('note')) && empty($get('term'))))
                                ->action(function ($set) {
                                    $set('note', '');
                                    $set('term', '');
                                    $set('open_term', false);
                                })
                                ->color('danger')
                        ])->columnSpanFull()->live(),

                        Group::make([
                            Textarea::make('note')
                                ->live(onBlur: true)
                                ->debounce(1000)
                                ->placeholder(__('messages.quote.note'))
                                // ->visible(fn($get) => $get('open_term') || !empty($get('note')))
                                ->label(__('messages.quote.note') . ':'),

                            Textarea::make('term')
                                ->live(onBlur: true)
                                ->debounce(1000)
                                ->placeholder(__('messages.quote.terms'))
                                // ->visible(fn($get) => $get('open_term') || !empty($get('term')))
                                ->label(__('messages.quote.terms') . ':'),
                        ])->columns(2)->columnSpanFull()->visible(fn($get) => $get('open_term') || !empty($get('note')) || !empty($get('term'))),
                    ])
            ]);
    }

    public static function calculateFinalAmount($get, $set)
    {
        $itemWiseTaxes = 0;
        $totalAmount = 0;
        $previousItems = $get('product_details') ?? [];

        $invoiceItems = collect($get('invoiceItems'))->map(function ($item, $key) use ($set, &$itemWiseTaxes, &$totalAmount, $previousItems) {
            // For free-form products, we don't need to check for product updates
            // Just use the provided quantity and price
            $quantity = (int) ($item['quantity'] ?? 1);
            $item['quantity'] = $quantity;

            // Use the provided price, no need to lookup from product
            $price = (float) ($item['price'] ?? 0);
            $item['price'] = $price;

            $itemTotal = $quantity * $price;
            $totalAmount += $itemTotal;

            if (!empty($item['tax_id'])) {
                foreach ($item['tax_id'] as $taxId) {
                    $tax = Tax::find($taxId);
                    if ($tax) {
                        $itemWiseTaxes += ($itemTotal * $tax->value) / 100;
                    }
                }
            }

            return array_merge($item, ['amount' => number_format($itemTotal, 2, '.', '')]);
        })->toArray();

        $set('product_details', $invoiceItems);

        $set('invoiceItems', $invoiceItems);

        $subTotal = collect($invoiceItems)->sum(fn($item) => (float) $item['amount']);
        $set('sub_total', number_format($subTotal, 2, '.', ''));

        $totalTax = 0;
        if (!empty($get('tax2'))) {
            foreach ($get('tax2') as $taxId) {
                $tax = Tax::find($taxId);
                $totalTax += $tax->value;
            }
        }

        $finalAmountWithTax = $subTotal + $itemWiseTaxes;
        $totalTaxAmount = $finalAmountWithTax * ($totalTax / 100);
        $fianalTaxAmount = $itemWiseTaxes + $totalTaxAmount;

        $set('total_tax', number_format($fianalTaxAmount, 2, '.', ''));

        $finalAmount = $finalAmountWithTax + $totalTaxAmount;

        $discountType = $get('discount_type');
        $discount = (float) $get('discount');
        $discountAmount = 0;
        $isDiscountBeforeTax = $get('discount_before_tax');

        if ($discountType == Quote::FIXED) {
            $discountAmount = $discount;
        } elseif ($discountType == Quote::PERCENTAGE) {
            if ($isDiscountBeforeTax) {
                $discountAmount = ($subTotal * $discount) / 100;
                $finalAmountWithTax -= $discountAmount;
                $finalAmount = $finalAmountWithTax + ($finalAmountWithTax * ($totalTax / 100));
            } else {
                $finalAmountWithTax = $finalAmountWithTax +  $totalTaxAmount;
                $discountAmount = ($finalAmountWithTax * $discount) / 100;
                $finalAmount -= $discountAmount;
            }
        }

        $set('total_discount', number_format($discountAmount, 2, '.', ''));
        $set('total', number_format($finalAmount, 2, '.', ''));

        return $finalAmount;
    }

    /**
     * Optimized version of calculateFinalAmount with reduced computational overhead
     * and better performance for real-time updates
     */
    public static function calculateFinalAmountDebounced($get, $set)
    {
        // For performance, we'll do a lightweight calculation first
        // and only do the full calculation when necessary

        // Quick calculation for individual item amounts (immediate feedback)
        if (is_array($get('invoiceItems'))) {
            $invoiceItems = collect($get('invoiceItems'))->map(function ($item) {
                $quantity = floatval($item['quantity'] ?? 1);
                $price = floatval($item['price'] ?? 0);
                $itemTotal = $quantity * $price;
                return array_merge($item, ['amount' => number_format($itemTotal, 2, '.', '')]);
            })->toArray();

            $set('invoiceItems', $invoiceItems);

            // Quick subtotal calculation
            $subTotal = collect($invoiceItems)->sum(fn($item) => floatval($item['amount']));
            $set('sub_total', number_format($subTotal, 2, '.', ''));
        }

        // Defer the full calculation to prevent UI blocking
        // The debouncing is handled by Filament's built-in debounce() method on form fields
        return self::calculateFinalAmount($get, $set);
    }

    public static function getTaxRate($taxId)
    {
        return \App\Models\Tax::where('id', $taxId)->value('value') ?? 0;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'view' => Pages\ViewInvoice::route('/{record}'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
            'payment' => Pages\InvoicePaymentPage::route('/{record}/payment'),
        ];
    }
}
