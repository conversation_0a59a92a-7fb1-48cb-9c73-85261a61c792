@echo off
echo =====================================================
echo EMERGENCY FIX SCRIPT FOR INVOICE APPLICATION
echo =====================================================
echo.

echo Step 1: Clearing Laravel caches...
C:\xampp\php\php.exe artisan config:clear
C:\xampp\php\php.exe artisan cache:clear
C:\xampp\php\php.exe artisan route:clear
C:\xampp\php\php.exe artisan view:clear
echo ✅ Caches cleared

echo.
echo Step 2: Testing PHP extensions...
echo Checking for required extensions:
C:\xampp\php\php.exe -m | findstr /i "pdo_mysql mysqli fileinfo"
echo.

echo Step 3: Testing database connection...
C:\xampp\php\php.exe artisan migrate:status
echo.

echo Step 4: Running migrations (if needed)...
C:\xampp\php\php.exe artisan migrate --force
echo.

echo Step 5: Optimizing application...
C:\xampp\php\php.exe artisan config:cache
C:\xampp\php\php.exe artisan route:cache
echo ✅ Application optimized

echo.
echo Step 6: Setting permissions...
icacls storage /grant Everyone:(OI)(CI)F /T
icacls bootstrap\cache /grant Everyone:(OI)(CI)F /T
echo ✅ Permissions set

echo.
echo =====================================================
echo FIX COMPLETE! 
echo.
echo Next steps:
echo 1. Ensure XAMPP Apache and MySQL are running
echo 2. Access: http://localhost:88/invoices_mod/public
echo 3. Or run: C:\xampp\php\php.exe artisan serve
echo =====================================================
pause
