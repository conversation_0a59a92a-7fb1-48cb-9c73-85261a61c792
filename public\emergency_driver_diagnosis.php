<?php
/**
 * 🚨 EMERGENCY DATABASE DRIVER DIAGNOSIS
 * 
 * This script diagnoses driver availability in web context
 * Access via: http://127.0.0.1:8000/emergency_driver_diagnosis.php
 */

echo "<h1>🚨 EMERGENCY DATABASE DRIVER DIAGNOSIS</h1>";
echo "<hr>";

echo "<h2>📊 PHP Environment Analysis</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>PHP Version</td><td>" . phpversion() . "</td></tr>";
echo "<tr><td>PHP SAPI</td><td>" . php_sapi_name() . "</td></tr>";
echo "<tr><td>Server Software</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Document Root</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Script Filename</td><td>" . ($_SERVER['SCRIPT_FILENAME'] ?? 'Unknown') . "</td></tr>";
echo "</table>";

echo "<h2>🔍 PDO Extension Analysis</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Extension</th><th>Status</th><th>Details</th></tr>";

// Check PDO
$pdo_loaded = extension_loaded('pdo');
echo "<tr><td>PDO</td><td>" . ($pdo_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($pdo_loaded ? 'Core PDO extension available' : 'PDO extension missing') . "</td></tr>";

// Check PDO MySQL
$pdo_mysql_loaded = extension_loaded('pdo_mysql');
echo "<tr><td>PDO MySQL</td><td>" . ($pdo_mysql_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($pdo_mysql_loaded ? 'MySQL PDO driver available' : 'MySQL PDO driver missing') . "</td></tr>";

// Check PDO SQLite
$pdo_sqlite_loaded = extension_loaded('pdo_sqlite');
echo "<tr><td>PDO SQLite</td><td>" . ($pdo_sqlite_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($pdo_sqlite_loaded ? 'SQLite PDO driver available' : 'SQLite PDO driver missing') . "</td></tr>";

// Check MySQLi
$mysqli_loaded = extension_loaded('mysqli');
echo "<tr><td>MySQLi</td><td>" . ($mysqli_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($mysqli_loaded ? 'MySQLi extension available' : 'MySQLi extension missing') . "</td></tr>";

// Check SQLite3
$sqlite3_loaded = extension_loaded('sqlite3');
echo "<tr><td>SQLite3</td><td>" . ($sqlite3_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($sqlite3_loaded ? 'SQLite3 extension available' : 'SQLite3 extension missing') . "</td></tr>";

echo "</table>";

echo "<h2>🔧 PDO Drivers Available</h2>";
if ($pdo_loaded) {
    $drivers = PDO::getAvailableDrivers();
    echo "<ul>";
    foreach ($drivers as $driver) {
        echo "<li><strong>{$driver}</strong></li>";
    }
    echo "</ul>";
    
    if (in_array('mysql', $drivers)) {
        echo "<p>✅ <strong>MySQL PDO driver is available!</strong></p>";
    } else {
        echo "<p>❌ <strong>MySQL PDO driver is NOT available!</strong></p>";
    }
    
    if (in_array('sqlite', $drivers)) {
        echo "<p>✅ <strong>SQLite PDO driver is available!</strong></p>";
    } else {
        echo "<p>❌ <strong>SQLite PDO driver is NOT available!</strong></p>";
    }
} else {
    echo "<p>❌ PDO extension not loaded</p>";
}

echo "<h2>🧪 Connection Testing</h2>";

// Test MySQL connection
echo "<h3>MySQL Connection Test</h3>";
try {
    if ($pdo_mysql_loaded && in_array('mysql', PDO::getAvailableDrivers())) {
        $host = '127.0.0.1';
        $dbname = 'invoicemod';
        $username = 'root';
        $password = '';
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "<p>✅ <strong>MySQL connection: SUCCESS!</strong></p>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "<p>✅ Test query result: " . $result['test'] . "</p>";
        
    } else {
        echo "<p>❌ <strong>MySQL PDO driver not available for testing</strong></p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ <strong>MySQL connection failed:</strong> " . $e->getMessage() . "</p>";
}

// Test SQLite connection
echo "<h3>SQLite Connection Test</h3>";
try {
    if ($pdo_sqlite_loaded && in_array('sqlite', PDO::getAvailableDrivers())) {
        // Test in-memory SQLite
        $pdo = new PDO('sqlite::memory:', null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "<p>✅ <strong>SQLite in-memory connection: SUCCESS!</strong></p>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "<p>✅ Test query result: " . $result['test'] . "</p>";
        
        // Test file-based SQLite
        $dbPath = __DIR__ . '/../database/database.sqlite';
        $dbDir = dirname($dbPath);
        
        if (!is_dir($dbDir)) {
            mkdir($dbDir, 0755, true);
        }
        
        if (!file_exists($dbPath)) {
            touch($dbPath);
        }
        
        $pdo_file = new PDO('sqlite:' . $dbPath, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "<p>✅ <strong>SQLite file connection: SUCCESS!</strong></p>";
        echo "<p>📁 Database file: {$dbPath}</p>";
        
    } else {
        echo "<p>❌ <strong>SQLite PDO driver not available for testing</strong></p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ <strong>SQLite connection failed:</strong> " . $e->getMessage() . "</p>";
}

echo "<h2>📁 PHP Configuration</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Loaded php.ini</td><td>" . (php_ini_loaded_file() ?: 'None') . "</td></tr>";
echo "<tr><td>Additional INI files</td><td>" . (php_ini_scanned_files() ?: 'None') . "</td></tr>";
echo "<tr><td>Extension Directory</td><td>" . ini_get('extension_dir') . "</td></tr>";
echo "</table>";

echo "<h2>🔧 Recommended Actions</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc;'>";
echo "<h3>Based on the diagnosis above:</h3>";

if (!$pdo_loaded) {
    echo "<p>❌ <strong>Critical:</strong> PDO extension is not loaded. Enable it in php.ini</p>";
}

if (!$pdo_mysql_loaded) {
    echo "<p>❌ <strong>Critical:</strong> PDO MySQL driver is not loaded. Enable extension=pdo_mysql in php.ini</p>";
}

if (!$pdo_sqlite_loaded) {
    echo "<p>❌ <strong>Critical:</strong> PDO SQLite driver is not loaded. Enable extension=pdo_sqlite in php.ini</p>";
}

if ($pdo_loaded && $pdo_mysql_loaded && $pdo_sqlite_loaded) {
    echo "<p>✅ <strong>Good:</strong> All required PDO extensions are loaded</p>";
    echo "<p>🔍 <strong>Next:</strong> Check Laravel database configuration</p>";
}

echo "</div>";

echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . " | PHP " . phpversion() . " | " . php_sapi_name() . "</small></p>";
?>
