<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Financial Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 20px;
            color: #666;
            margin-bottom: 10px;
        }
        .date-range {
            font-size: 14px;
            color: #888;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        .table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $company_name ?? 'DCF - Digital Clearing and Forwarding Agency' }}</div>
        <div class="report-title">{{ $report_title ?? 'Financial Report' }}</div>
        <div class="date-range">Period: {{ $start_date }} to {{ $end_date }}</div>
    </div>

    <div class="section">
        <div class="section-title">Financial Summary</div>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-label">Total Invoices</div>
                <div class="metric-value">{{ $summary['total_invoices'] ?? 0 }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Invoice Amount</div>
                <div class="metric-value">${{ number_format($summary['total_invoice_amount'] ?? 0, 2) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Payments Received</div>
                <div class="metric-value">${{ number_format($summary['total_payments_received'] ?? 0, 2) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Outstanding Amount</div>
                <div class="metric-value">${{ number_format($summary['outstanding_amount'] ?? 0, 2) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Payment Success Rate</div>
                <div class="metric-value">{{ number_format($summary['payment_success_rate'] ?? 0, 2) }}%</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Collection Efficiency</div>
                <div class="metric-value">{{ number_format($summary['collection_efficiency'] ?? 0, 2) }}%</div>
            </div>
        </div>
    </div>

    @if(isset($revenue_trends) && count($revenue_trends) > 0)
    <div class="section">
        <div class="section-title">Revenue Trends</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Period</th>
                    <th>Total Revenue</th>
                    <th>Payment Count</th>
                    <th>Average Payment</th>
                </tr>
            </thead>
            <tbody>
                @foreach($revenue_trends as $trend)
                <tr>
                    <td>{{ $trend['period'] }}</td>
                    <td>${{ number_format($trend['total_revenue'], 2) }}</td>
                    <td>{{ $trend['payment_count'] }}</td>
                    <td>${{ number_format($trend['avg_payment_amount'], 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($tax_summary) && count($tax_summary) > 0)
    <div class="section">
        <div class="section-title">Tax Summary</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Tax Name</th>
                    <th>Tax Rate</th>
                    <th>Taxable Amount</th>
                    <th>Tax Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach($tax_summary as $tax)
                <tr>
                    <td>{{ $tax['tax_name'] }}</td>
                    <td>{{ number_format($tax['tax_rate'], 2) }}%</td>
                    <td>${{ number_format($tax['taxable_amount'], 2) }}</td>
                    <td>${{ number_format($tax['tax_amount'], 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p>Generated on {{ $generated_at }}</p>
        <p>This report is confidential and intended for internal use only.</p>
    </div>
</body>
</html>
