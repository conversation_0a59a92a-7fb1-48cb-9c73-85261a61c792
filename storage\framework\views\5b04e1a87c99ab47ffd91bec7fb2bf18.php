<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Invoice | <?php echo e(getAppName()); ?></title>
    <!-- Favicon -->
    <link rel="icon" href="<?php echo e(asset(getSettingValue('favicon_icon'))); ?>" type="image/png">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/css/third-party.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/css/style.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/css/plugins.css')); ?>">
    <script src="<?php echo e(asset('messages.js')); ?>"></script>
    <script>
        let decimalsSeparator = "<?php echo e(getSettingValue('decimal_separator')); ?>"
        let thousandsSeparator = "<?php echo e(getSettingValue('thousand_separator')); ?>"
        let currentDateFormat = "<?php echo e(currentDateFormat()); ?>"
        let momentDateFormat = "<?php echo e(momentJsCurrentDateFormat()); ?>"
        let ajaxCallIsRunning = false
        let phoneNo = ''
        let getUserLanguages = "<?php echo e($userLang); ?>"
        Lang.setLocale(getUserLanguages)
    </script>
</head>

<body>
    <div class="d-flex flex-column flex-root">
        <div class="d-flex flex-row flex-column-fluid">
            <div class="container">
                <div class="d-flex flex-column flex-lg-row">
                    <div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
                        <div class="p-4 p-sm-12">
                            <?php $__sessionArgs = ['success'];
if (session()->has($__sessionArgs[0])) :
if (isset($value)) { $__sessionPrevious[] = $value; }
$value = session()->get($__sessionArgs[0]); ?>
                            <div class="alert alert-success" role="alert">
                                <?php echo e(session('success')); ?>

                            </div>
                            <?php unset($value);
if (isset($__sessionPrevious) && !empty($__sessionPrevious)) { $value = array_pop($__sessionPrevious); }
if (isset($__sessionPrevious) && empty($__sessionPrevious)) { unset($__sessionPrevious); }
endif;
unset($__sessionArgs); ?>
                            <?php echo $__env->make('invoices.show_fields', ['isPublicView' => false], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/invoices/public-invoice/public_view.blade.php ENDPATH**/ ?>