<x-filament-widgets::widget>
    <x-filament::section>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Activity Feed -->
            <div class="lg:col-span-2">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-1.5 animate-pulse"></span>
                            Live
                        </span>
                    </div>
                </div>

                <div class="flow-root">
                    <ul role="list" class="-mb-8">
                        @foreach($this->getActivities() as $index => $activity)
                            <li>
                                <div class="relative pb-8">
                                    @if(!$loop->last)
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600" aria-hidden="true"></span>
                                    @endif
                                    
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800 
                                                {{ $activity['color'] === 'success' ? 'bg-green-500' : 
                                                   ($activity['color'] === 'info' ? 'bg-blue-500' : 
                                                   ($activity['color'] === 'danger' ? 'bg-red-500' : 
                                                   ($activity['color'] === 'warning' ? 'bg-yellow-500' : 'bg-gray-500'))) }}">
                                                <x-heroicon-o-document-text class="h-4 w-4 text-white" />
                                            </span>
                                        </div>
                                        
                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-900 dark:text-white font-medium">
                                                    {{ $activity['title'] }}
                                                </p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $activity['description'] }}
                                                </p>
                                            </div>
                                            
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                                <time datetime="{{ $activity['time']->toISOString() }}">
                                                    {{ $activity['time']->diffForHumans() }}
                                                </time>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>

                <div class="mt-6 text-center">
                    <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                        View all activity
                        <span aria-hidden="true"> &rarr;</span>
                    </a>
                </div>
            </div>

            <!-- Quick Stats Sidebar -->
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Today's Overview</h3>
                    
                    @php $stats = $this->getQuickStats(); @endphp
                    
                    <div class="space-y-4">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <x-heroicon-o-document-text class="h-6 w-6 text-blue-600" />
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Invoices Created</p>
                                    <p class="text-2xl font-bold text-blue-600">{{ $stats['today_invoices'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <x-heroicon-o-banknotes class="h-6 w-6 text-green-600" />
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-900 dark:text-green-100">Payments Received</p>
                                    <p class="text-2xl font-bold text-green-600">{{ $stats['today_payments'] }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <x-heroicon-o-currency-dollar class="h-6 w-6 text-purple-600" />
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Today's Revenue</p>
                                    <p class="text-2xl font-bold text-purple-600">${{ number_format($stats['today_revenue'], 2) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts Section -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Alerts</h3>
                    
                    <div class="space-y-3">
                        @if($stats['overdue_invoices'] > 0)
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-exclamation-triangle class="h-5 w-5 text-red-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                            Overdue Invoices
                                        </h3>
                                        <div class="mt-1 text-sm text-red-700 dark:text-red-300">
                                            {{ $stats['overdue_invoices'] }} invoices are overdue
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($stats['pending_invoices'] > 5)
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-clock class="h-5 w-5 text-yellow-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                            Pending Invoices
                                        </h3>
                                        <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                            {{ $stats['pending_invoices'] }} invoices awaiting payment
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($stats['overdue_invoices'] == 0 && $stats['pending_invoices'] <= 5)
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-check-circle class="h-5 w-5 text-green-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                            All Good!
                                        </h3>
                                        <div class="mt-1 text-sm text-green-700 dark:text-green-300">
                                            No urgent issues to address
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Actions -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                    
                    <div class="space-y-2">
                        <a href="{{ route('filament.admin.resources.invoices.create') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-plus class="h-4 w-4 mr-2" />
                            Create Invoice
                        </a>
                        
                        <a href="{{ route('filament.admin.resources.clients.create') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-user-plus class="h-4 w-4 mr-2" />
                            Add Client
                        </a>
                        
                        <a href="{{ route('filament.admin.pages.advanced-reporting') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-chart-bar class="h-4 w-4 mr-2" />
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
