<?php

/**
 * 🔥 COMPREHENSIVE SYSTEM HEALTH CHECK - FULL BEAST MODE 🔥
 * 
 * This script performs a complete health check of all the fixes implemented
 * for the invoice management system debugging effort.
 */

require_once 'vendor/autoload.php';

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Services\InvoiceStatusService;
use App\Services\ReportingService;
use App\Repositories\DashboardRepository;
use App\Filament\Widgets\IncomeOverview;
use App\Filament\Widgets\DashboardOverview;
use App\Filament\Widgets\InvoiceOverview;
use Carbon\Carbon;

class SystemHealthChecker
{
    private array $results = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        echo "🔥 FULL BEAST MODE SYSTEM HEALTH CHECK 🔥\n";
        echo "==========================================\n\n";
    }

    public function runAllTests(): void
    {
        $this->testIncomeOverviewWidget();
        $this->testDashboardWidgets();
        $this->testInvoiceStatusService();
        $this->testReportingService();
        $this->testDashboardRepository();
        $this->testDataConsistency();
        $this->testErrorHandling();
        
        $this->displayResults();
    }

    private function testIncomeOverviewWidget(): void
    {
        echo "📊 Testing Income Overview Widget...\n";
        
        try {
            $widget = new IncomeOverview();
            
            // Test data retrieval
            $data = $widget->getData();
            $this->assert(
                isset($data['datasets']) && isset($data['labels']),
                "Income Overview widget returns proper data structure"
            );
            
            // Test filter functionality
            $widget->filter = 'today';
            $todayData = $widget->getData();
            $this->assert(
                is_array($todayData),
                "Income Overview widget handles filter changes"
            );
            
            // Test totalFilterDay method
            $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
            $endDate = Carbon::now()->format('Y-m-d');
            $filterData = $widget->totalFilterDay($startDate, $endDate);
            
            $this->assert(
                isset($filterData['days']) && isset($filterData['income']),
                "totalFilterDay method returns correct structure"
            );
            
            $this->pass("Income Overview Widget - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Income Overview Widget - Error: " . $e->getMessage());
        }
    }

    private function testDashboardWidgets(): void
    {
        echo "🎛️ Testing Dashboard Widgets...\n";
        
        try {
            // Test main dashboard widget
            $dashboardWidget = new DashboardOverview();
            $dashboardData = $dashboardWidget->getViewData();
            
            $this->assert(
                isset($dashboardData['totalInvoices']) && 
                isset($dashboardData['paidInvoices']) &&
                isset($dashboardData['unpaidInvoices']),
                "Dashboard Overview widget returns required data"
            );
            
            // Test invoice overview widget
            $invoiceWidget = new InvoiceOverview();
            $invoiceData = $invoiceWidget->getData();
            
            $this->assert(
                isset($invoiceData['labels']) && isset($invoiceData['datasets']),
                "Invoice Overview widget returns chart data"
            );
            
            $this->pass("Dashboard Widgets - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Dashboard Widgets - Error: " . $e->getMessage());
        }
    }

    private function testInvoiceStatusService(): void
    {
        echo "⚡ Testing Invoice Status Service...\n";
        
        try {
            $statusService = new InvoiceStatusService();
            
            // Test status transition validation
            $this->assert(
                $statusService->isValidStatusTransition(Invoice::DRAFT, Invoice::UNPAID),
                "Valid status transition: DRAFT to UNPAID"
            );
            
            $this->assert(
                !$statusService->isValidStatusTransition(Invoice::PAID, Invoice::DRAFT),
                "Invalid status transition: PAID to DRAFT blocked"
            );
            
            // Test overdue detection
            $testInvoice = new Invoice([
                'due_date' => Carbon::yesterday(),
                'status' => Invoice::UNPAID
            ]);
            
            $this->assert(
                $statusService->isOverdue($testInvoice),
                "Overdue detection works correctly"
            );
            
            $this->pass("Invoice Status Service - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Invoice Status Service - Error: " . $e->getMessage());
        }
    }

    private function testReportingService(): void
    {
        echo "📈 Testing Reporting Service...\n";
        
        try {
            $reportingService = new ReportingService();
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now();
            
            // Test financial summary
            $financialSummary = $reportingService->getFinancialSummary($startDate, $endDate);
            $this->assert(
                isset($financialSummary['total_invoices']) &&
                isset($financialSummary['total_payments_received']) &&
                isset($financialSummary['outstanding_amount']),
                "Financial summary returns required metrics"
            );
            
            // Test revenue trends
            $revenueTrends = $reportingService->getRevenueTrends($startDate, $endDate);
            $this->assert(
                is_array($revenueTrends),
                "Revenue trends returns array data"
            );
            
            // Test overdue invoices
            $overdueInvoices = $reportingService->getOverdueInvoices();
            $this->assert(
                $overdueInvoices instanceof \Illuminate\Support\Collection,
                "Overdue invoices returns collection"
            );
            
            // Test dashboard metrics
            $dashboardMetrics = $reportingService->getDashboardMetrics($startDate, $endDate);
            $this->assert(
                isset($dashboardMetrics['financial_summary']) &&
                isset($dashboardMetrics['overdue_summary']),
                "Dashboard metrics compilation works"
            );
            
            $this->pass("Reporting Service - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Reporting Service - Error: " . $e->getMessage());
        }
    }

    private function testDashboardRepository(): void
    {
        echo "🗄️ Testing Dashboard Repository...\n";
        
        try {
            $dashboardRepo = new DashboardRepository();
            
            // Test payment overview data
            $paymentData = $dashboardRepo->getPaymentOverviewData();
            $this->assert(
                isset($paymentData['received_amount']) &&
                isset($paymentData['due_amount']) &&
                isset($paymentData['labels']),
                "Payment overview data structure is correct"
            );
            
            // Test invoice overview data
            $invoiceData = $dashboardRepo->getInvoiceOverviewData();
            $this->assert(
                isset($invoiceData['total_paid_invoices']) &&
                isset($invoiceData['dataPoints']),
                "Invoice overview data structure is correct"
            );
            
            // Test currency data
            $currencyData = $dashboardRepo->getAdminCurrencyData();
            $this->assert(
                isset($currencyData['totalInvoices']) &&
                isset($currencyData['currencyDetails']),
                "Currency data structure is correct"
            );
            
            $this->pass("Dashboard Repository - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Dashboard Repository - Error: " . $e->getMessage());
        }
    }

    private function testDataConsistency(): void
    {
        echo "🔍 Testing Data Consistency...\n";
        
        try {
            // Test that only approved payments are counted
            $approvedPayments = Payment::where('is_approved', Payment::APPROVED)->sum('amount');
            $allPayments = Payment::sum('amount');
            
            $this->assert(
                $approvedPayments <= $allPayments,
                "Approved payments <= total payments (data consistency)"
            );
            
            // Test invoice status consistency
            $paidInvoices = Invoice::where('status', Invoice::PAID)->count();
            $totalInvoices = Invoice::where('status', '!=', Invoice::DRAFT)->count();
            
            $this->assert(
                $paidInvoices <= $totalInvoices,
                "Paid invoices <= total invoices (status consistency)"
            );
            
            $this->pass("Data Consistency - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Data Consistency - Error: " . $e->getMessage());
        }
    }

    private function testErrorHandling(): void
    {
        echo "🛡️ Testing Error Handling...\n";
        
        try {
            // Test widgets with no data
            $widget = new IncomeOverview();
            $widget->filter = 'invalid_filter';
            $data = $widget->getData();
            
            $this->assert(
                isset($data['datasets']) && isset($data['labels']),
                "Widgets handle invalid filters gracefully"
            );
            
            // Test reporting service with invalid dates
            $reportingService = new ReportingService();
            $summary = $reportingService->getFinancialSummary(
                Carbon::now()->addYear(), 
                Carbon::now()->subYear()
            );
            
            $this->assert(
                is_array($summary),
                "Reporting service handles invalid date ranges"
            );
            
            $this->pass("Error Handling - All tests passed");
            
        } catch (\Exception $e) {
            $this->fail("Error Handling - Error: " . $e->getMessage());
        }
    }

    private function assert(bool $condition, string $message): void
    {
        $this->totalTests++;
        
        if ($condition) {
            $this->passedTests++;
            echo "  ✅ {$message}\n";
        } else {
            $this->failedTests++;
            echo "  ❌ {$message}\n";
        }
    }

    private function pass(string $message): void
    {
        echo "  🎉 {$message}\n\n";
    }

    private function fail(string $message): void
    {
        echo "  💥 {$message}\n\n";
    }

    private function displayResults(): void
    {
        echo "==========================================\n";
        echo "🔥 FULL BEAST MODE RESULTS 🔥\n";
        echo "==========================================\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests} ✅\n";
        echo "Failed: {$this->failedTests} ❌\n";
        
        $successRate = $this->totalTests > 0 ? ($this->passedTests / $this->totalTests) * 100 : 0;
        echo "Success Rate: " . number_format($successRate, 1) . "%\n";
        
        if ($this->failedTests === 0) {
            echo "\n🎉 ALL SYSTEMS OPERATIONAL - BEAST MODE SUCCESS! 🎉\n";
        } else {
            echo "\n⚠️ Some issues detected - Review failed tests above\n";
        }
        
        echo "==========================================\n";
    }
}

// Run the health check
if (php_sapi_name() === 'cli') {
    $checker = new SystemHealthChecker();
    $checker->runAllTests();
}
