<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OverdueInvoicesExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $overdueData;

    public function __construct(array $overdueData)
    {
        $this->overdueData = $overdueData;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->overdueData as $invoice) {
            $data[] = [
                $invoice['invoice_id'] ?? 'Unknown',
                $invoice['client_name'] ?? 'Unknown Client',
                $invoice['client_email'] ?? '<EMAIL>',
                $invoice['invoice_date'] ?? 'N/A',
                $invoice['due_date'] ?? 'N/A',
                number_format($invoice['total_amount'] ?? 0, 2),
                number_format($invoice['paid_amount'] ?? 0, 2),
                number_format($invoice['outstanding_amount'] ?? 0, 2),
                $invoice['days_overdue'] ?? 0,
                $invoice['status'] ?? 'Unknown',
                $invoice['overdue_severity'] ?? 'low',
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Invoice ID',
            'Client Name',
            'Client Email',
            'Invoice Date',
            'Due Date',
            'Total Amount',
            'Paid Amount',
            'Outstanding Amount',
            'Days Overdue',
            'Status',
            'Severity',
        ];
    }

    public function title(): string
    {
        return 'Overdue Invoices';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'DC3545'],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
