<?php

namespace App\Filament\Pages;

use App\Services\DatabaseBackupService;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Response;

class DatabaseBackup extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-archive-box';
    protected static ?string $navigationLabel = 'Database Backup';
    protected static ?string $title = 'Database Backup & Export';
    protected static string $view = 'filament.pages.database-backup';
    protected static ?int $navigationSort = 10;
    protected static ?string $navigationGroup = 'System';

    public ?array $data = [];
    protected DatabaseBackupService $backupService;

    public function boot(DatabaseBackupService $backupService): void
    {
        $this->backupService = $backupService;
    }

    public function mount(): void
    {
        $this->form->fill([
            'backup_name' => 'invoice_system_' . now()->format('Y_m_d_H_i'),
            'include_database' => true,
            'include_config' => true,
            'include_storage' => true,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Backup Configuration')
                    ->description('Configure what to include in your backup')
                    ->schema([
                        TextInput::make('backup_name')
                            ->label('Backup Name')
                            ->required()
                            ->placeholder('Enter backup name')
                            ->helperText('A unique name for this backup'),
                        
                        Toggle::make('include_database')
                            ->label('Include Database')
                            ->helperText('Export all database tables and data')
                            ->default(true),
                        
                        Toggle::make('include_config')
                            ->label('Include Configuration Files')
                            ->helperText('Export .env and config files')
                            ->default(true),
                        
                        Toggle::make('include_storage')
                            ->label('Include Storage Files')
                            ->helperText('Export uploaded files and documents')
                            ->default(true),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create_backup')
                ->label('Create Backup')
                ->icon('heroicon-o-archive-box-arrow-down')
                ->color('success')
                ->action('createBackup')
                ->requiresConfirmation()
                ->modalHeading('Create System Backup')
                ->modalDescription('This will create a complete backup of your system. This process may take several minutes.')
                ->modalSubmitActionLabel('Create Backup'),
                
            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action('$refresh'),
        ];
    }

    public function createBackup(): void
    {
        try {
            $result = $this->backupService->createFullBackup($this->data);
            
            if ($result['success']) {
                Notification::make()
                    ->title('Backup Created Successfully')
                    ->body("Backup '{$result['backup_name']}' has been created successfully.")
                    ->success()
                    ->send();
                    
                $this->dispatch('backup-created');
            } else {
                Notification::make()
                    ->title('Backup Failed')
                    ->body($result['error'] ?? 'Unknown error occurred')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Backup Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function downloadBackup(string $filename): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        try {
            $filePath = $this->backupService->downloadBackup($filename);
            
            return Response::download($filePath, $filename, [
                'Content-Type' => 'application/zip',
            ]);
        } catch (\Exception $e) {
            Notification::make()
                ->title('Download Failed')
                ->body($e->getMessage())
                ->danger()
                ->send();
                
            return redirect()->back();
        }
    }

    public function deleteBackup(string $filename): void
    {
        try {
            if ($this->backupService->deleteBackup($filename)) {
                Notification::make()
                    ->title('Backup Deleted')
                    ->body("Backup '{$filename}' has been deleted successfully.")
                    ->success()
                    ->send();
                    
                $this->dispatch('backup-deleted');
            } else {
                Notification::make()
                    ->title('Delete Failed')
                    ->body('Could not delete the backup file.')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Delete Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function getBackups(): array
    {
        return $this->backupService->listBackups();
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }

    public static function canAccess(): bool
    {
        return auth()->user()?->hasRole('admin') ?? false;
    }
}
