<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Client
 *
 * @property int $id
 * @property int $user_id
 * @property int $country_id
 * @property int $state_id
 * @property int $city_id
 * @property string $postal_code
 * @property string|null $website
 * @property string $address
 * @property string|null $note
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Client newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Client newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Client query()
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client wherePostalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereWebsite($value)
 * @property bool|null $is_password_set
 * @property-read \App\Models\City|null $city
 * @property-read \App\Models\Country|null $country
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \App\Models\State|null $state
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereIsPasswordSet($value)
 * @property string|null $vat_no
 * @property string|null $company_name
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Client whereVatNo($value)
 * @mixin Eloquent
 */
class Client extends Model
{
    use HasFactory;

    protected $table = 'clients';

    public $fillable = [
        'user_id',
        'website',
        'postal_code',
        'address',
        'note',
        'country_id',
        'state_id',
        'city_id',
        'is_password_set',
        'vat_no',
        'company_name',
    ];

    protected $casts = [
        'website' => 'string',
        'postal_code' => 'string',
        'address' => 'string',
        'note' => 'string',
        'country_id' => 'integer',
        'state_id' => 'integer',
        'city_id' => 'integer',
        'user_id' => 'integer',
        'is_password_set' => 'boolean',
        'vat_no' => 'string',
        'company_name' => 'string',
    ];

    /**
     * B2B-focused validation rules where company name is required
     * and individual names are optional
     *
     * @var array
     */
    public static $rules = [
        'first_name' => 'nullable|string|max:255',
        'last_name' => 'nullable|string|max:255',
        'email' => 'required|email:filter|unique:users,email',
        'password' => 'required|same:password_confirmation|min:6',
        'postal_code' => 'required|string',
        'address' => 'nullable|string',
        'website' => 'nullable|url',
        'company_name' => 'required|string|max:255',
    ];

    /**
     * Legacy B2C validation rules (kept for backward compatibility)
     *
     * @var array
     */
    public static $b2cRules = [
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'email' => 'required|email:filter|unique:users,email',
        'password' => 'required|same:password_confirmation|min:6',
        'postal_code' => 'required|string',
        'address' => 'nullable|string',
        'website' => 'nullable|url',
        'company_name' => 'nullable|string|max:255',
    ];

    /**
     * Custom validation to ensure at least company name or individual names are provided
     */
    public static function validateClientIdentity($data): array
    {
        $errors = [];

        $hasCompanyName = !empty(trim($data['company_name'] ?? ''));
        $hasFirstName = !empty(trim($data['first_name'] ?? ''));
        $hasLastName = !empty(trim($data['last_name'] ?? ''));

        // For B2B focus, require company name
        if (!$hasCompanyName) {
            $errors['company_name'] = 'Company name is required for B2B clients.';
        }

        // If no company name and no individual names, that's an error
        if (!$hasCompanyName && !$hasFirstName && !$hasLastName) {
            $errors['identity'] = 'Either company name or individual names must be provided.';
        }

        return $errors;
    }

    /**
     * Get the primary display name for this client
     */
    public function getDisplayNameAttribute(): string
    {
        if (!empty($this->company_name)) {
            return $this->company_name;
        }

        return $this->user->individual_name ?: 'Unnamed Client';
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'id');
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id', 'id');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }
}
