<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;

class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Role Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->weight('bold')
                                    ->color('primary'),
                                
                                TextEntry::make('guard_name')
                                    ->badge(),
                            ]),
                        
                        TextEntry::make('description')
                            ->columnSpanFull(),
                        
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('users_count')
                                    ->label('Total Users')
                                    ->badge()
                                    ->color('success'),
                                
                                TextEntry::make('permissions_count')
                                    ->label('Total Permissions')
                                    ->badge()
                                    ->color('info'),
                            ]),
                    ]),

                Section::make('Assigned Permissions')
                    ->schema([
                        RepeatableEntry::make('permissions')
                            ->schema([
                                TextEntry::make('name')
                                    ->weight('bold'),
                                
                                TextEntry::make('description')
                                    ->color('gray'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ]),

                Section::make('Users with this Role')
                    ->schema([
                        RepeatableEntry::make('users')
                            ->schema([
                                TextEntry::make('name')
                                    ->weight('bold'),
                                
                                TextEntry::make('email')
                                    ->color('gray'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
