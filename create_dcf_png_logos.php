<?php
/**
 * DCF PNG Logo Creation Script
 * Creates PNG versions of the DCF logo based on the provided design
 */

// Check if GD extension is available
if (!extension_loaded('gd')) {
    echo "❌ GD extension is not available. Cannot create PNG logos.\n";
    echo "Please install php-gd extension or manually place PNG logo files.\n";
    exit(1);
}

echo "=== DCF PNG LOGO CREATION ===\n\n";

// Create main DCF logo (300x120 pixels)
function createMainLogo() {
    $width = 300;
    $height = 120;
    
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    // Enable alpha blending
    imagealphablending($image, true);
    imagesavealpha($image, true);
    
    // Create transparent background
    $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
    imagefill($image, 0, 0, $transparent);
    
    // Define colors based on DCF design
    $red = imagecolorallocate($image, 227, 62, 62);      // #E53E3E
    $darkRed = imagecolorallocate($image, 197, 48, 48);  // #C53030
    $blue = imagecolorallocate($image, 49, 130, 206);    // #3182CE
    $darkBlue = imagecolorallocate($image, 44, 82, 130); // #2C5282
    $white = imagecolorallocate($image, 255, 255, 255);
    
    // Draw red curved arrow (simplified representation)
    $points = array(
        20, 30,   // Start point
        40, 15,   // Control point 1
        60, 25,   // Control point 2
        80, 35,   // End point
        75, 45,   // Return curve
        55, 40,
        35, 45,
        25, 35
    );
    imagefilledpolygon($image, $points, 8, $red);
    
    // Draw "DCF" text in blue (using built-in font, scaled up)
    $font_size = 5; // Built-in font size
    $text = "DCF";
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    
    // Scale up the text by drawing it multiple times with slight offsets
    for ($x = 100; $x <= 102; $x++) {
        for ($y = 25; $y <= 27; $y++) {
            imagestring($image, $font_size, $x, $y, $text, $blue);
        }
    }
    
    // Draw red banner background
    imagefilledrectangle($image, 100, 55, 280, 85, $red);
    
    // Draw tagline text in white
    $tagline1 = "Digital Clearing and Forwarding";
    $tagline2 = "Agency";
    
    imagestring($image, 2, 105, 60, $tagline1, $white);
    imagestring($image, 2, 105, 72, $tagline2, $white);
    
    return $image;
}

// Create favicon (32x32 pixels)
function createFavicon() {
    $size = 32;
    
    // Create image
    $image = imagecreatetruecolor($size, $size);
    
    // Enable alpha blending
    imagealphablending($image, true);
    imagesavealpha($image, true);
    
    // Create colors
    $blue = imagecolorallocate($image, 49, 130, 206);
    $white = imagecolorallocate($image, 255, 255, 255);
    $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
    
    // Fill with transparent background
    imagefill($image, 0, 0, $transparent);
    
    // Draw blue circle background
    imagefilledellipse($image, 16, 16, 30, 30, $blue);
    
    // Draw "DCF" text
    imagestring($image, 2, 8, 12, "DCF", $white);
    
    return $image;
}

// Create white version for dark backgrounds
function createWhiteLogo() {
    $width = 300;
    $height = 120;
    
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    // Enable alpha blending
    imagealphablending($image, true);
    imagesavealpha($image, true);
    
    // Create transparent background
    $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
    imagefill($image, 0, 0, $transparent);
    
    // Define colors for white version
    $white = imagecolorallocate($image, 255, 255, 255);
    $lightBlue = imagecolorallocate($image, 99, 179, 237); // #63B3ED
    $blue = imagecolorallocate($image, 66, 153, 225);      // #4299E1
    
    // Draw white curved arrow
    $points = array(
        20, 30,   // Start point
        40, 15,   // Control point 1
        60, 25,   // Control point 2
        80, 35,   // End point
        75, 45,   // Return curve
        55, 40,
        35, 45,
        25, 35
    );
    imagefilledpolygon($image, $points, 8, $white);
    
    // Draw "DCF" text in white
    $font_size = 5;
    $text = "DCF";
    
    // Scale up the text
    for ($x = 100; $x <= 102; $x++) {
        for ($y = 25; $y <= 27; $y++) {
            imagestring($image, $font_size, $x, $y, $text, $white);
        }
    }
    
    // Draw light blue banner background
    imagefilledrectangle($image, 100, 55, 280, 85, $lightBlue);
    
    // Draw tagline text in white
    $tagline1 = "Digital Clearing and Forwarding";
    $tagline2 = "Agency";
    
    imagestring($image, 2, 105, 60, $tagline1, $white);
    imagestring($image, 2, 105, 72, $tagline2, $white);
    
    return $image;
}

// Create directory if it doesn't exist
$logoDir = 'public/images';
if (!is_dir($logoDir)) {
    mkdir($logoDir, 0755, true);
}

// Create and save main logo
echo "1. Creating main DCF logo (PNG)...\n";
$mainLogo = createMainLogo();
if (imagepng($mainLogo, $logoDir . '/dcf-logo.png')) {
    echo "   ✅ Main logo saved: dcf-logo.png\n";
} else {
    echo "   ❌ Failed to save main logo\n";
}
imagedestroy($mainLogo);

// Create and save white logo
echo "2. Creating white DCF logo (PNG)...\n";
$whiteLogo = createWhiteLogo();
if (imagepng($whiteLogo, $logoDir . '/dcf-logo-white.png')) {
    echo "   ✅ White logo saved: dcf-logo-white.png\n";
} else {
    echo "   ❌ Failed to save white logo\n";
}
imagedestroy($whiteLogo);

// Create and save favicon
echo "3. Creating DCF favicon (PNG)...\n";
$favicon = createFavicon();
if (imagepng($favicon, $logoDir . '/dcf-favicon.png')) {
    echo "   ✅ Favicon saved: dcf-favicon.png\n";
} else {
    echo "   ❌ Failed to save favicon\n";
}
imagedestroy($favicon);

// Create additional sizes for different use cases
echo "4. Creating additional logo sizes...\n";

// Large logo for high-DPI displays (600x240)
$largeLogo = imagecreatetruecolor(600, 240);
imagealphablending($largeLogo, true);
imagesavealpha($largeLogo, true);
$transparent = imagecolorallocatealpha($largeLogo, 0, 0, 0, 127);
imagefill($largeLogo, 0, 0, $transparent);

// Copy and scale main logo
$mainLogoReload = imagecreatefrompng($logoDir . '/dcf-logo.png');
imagecopyresampled($largeLogo, $mainLogoReload, 0, 0, 0, 0, 600, 240, 300, 120);
if (imagepng($largeLogo, $logoDir . '/dcf-logo-large.png')) {
    echo "   ✅ Large logo saved: dcf-logo-large.png\n";
}
imagedestroy($largeLogo);
imagedestroy($mainLogoReload);

// Small logo for email headers (150x60)
$smallLogo = imagecreatetruecolor(150, 60);
imagealphablending($smallLogo, true);
imagesavealpha($smallLogo, true);
$transparent = imagecolorallocatealpha($smallLogo, 0, 0, 0, 127);
imagefill($smallLogo, 0, 0, $transparent);

// Copy and scale main logo
$mainLogoReload = imagecreatefrompng($logoDir . '/dcf-logo.png');
imagecopyresampled($smallLogo, $mainLogoReload, 0, 0, 0, 0, 150, 60, 300, 120);
if (imagepng($smallLogo, $logoDir . '/dcf-logo-small.png')) {
    echo "   ✅ Small logo saved: dcf-logo-small.png\n";
}
imagedestroy($smallLogo);
imagedestroy($mainLogoReload);

echo "\n=== PNG LOGO CREATION COMPLETE ===\n";
echo "✅ All DCF PNG logo files created successfully!\n\n";

echo "Created files:\n";
echo "• dcf-logo.png (300x120) - Main logo\n";
echo "• dcf-logo-white.png (300x120) - White version\n";
echo "• dcf-favicon.png (32x32) - Favicon\n";
echo "• dcf-logo-large.png (600x240) - High-DPI version\n";
echo "• dcf-logo-small.png (150x60) - Email header version\n";

echo "\nNext steps:\n";
echo "1. Update helper functions to use PNG files\n";
echo "2. Update logo view template\n";
echo "3. Update Filament panel configuration\n";
echo "4. Test PNG logo integration\n";
?>
