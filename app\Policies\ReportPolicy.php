<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ReportPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view financial reports.
     */
    public function viewFinancialReports(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can view invoice analytics.
     */
    public function viewInvoiceAnalytics(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can view client reports.
     */
    public function viewClientReports(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can view product analytics.
     */
    public function viewProductAnalytics(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can export reports.
     */
    public function exportReports(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can view sensitive financial data.
     */
    public function viewSensitiveData(User $user): bool
    {
        return $user->hasRole('super_admin');
    }

    /**
     * Determine whether the user can view all client data.
     */
    public function viewAllClientData(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can view detailed analytics.
     */
    public function viewDetailedAnalytics(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin']);
    }

    /**
     * Determine whether the user can access dashboard metrics.
     */
    public function viewDashboardMetrics(User $user): bool
    {
        return $user->hasRole(['admin', 'super_admin', 'client']);
    }

    /**
     * Determine whether the user can view their own client reports.
     */
    public function viewOwnClientReports(User $user): bool
    {
        return $user->hasRole('client') || $this->viewAllClientData($user);
    }
}
