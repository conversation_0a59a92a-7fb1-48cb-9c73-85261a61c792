<?php
/**
 * 🚨 MYSQL DRIVER ERROR FIX SCRIPT
 * 
 * This script diagnoses and fixes the MySQL driver error:
 * "could not find driver (Connection: mysql, SQL: select * from `sessions`...)"
 * 
 * Usage: php FIX_MYSQL_DRIVER_ERROR.php
 */

echo "🚨 MYSQL DRIVER ERROR DIAGNOSTIC & FIX SCRIPT\n";
echo str_repeat("=", 60) . "\n\n";

echo "🔍 STEP 1: PHP EXTENSIONS DIAGNOSTIC\n";
echo str_repeat("-", 40) . "\n";

// Check critical MySQL extensions
$mysqlExtensions = [
    'pdo' => 'PDO extension (required for database connections)',
    'pdo_mysql' => 'PDO MySQL driver (critical for MySQL connections)',
    'mysqli' => 'MySQLi extension (alternative MySQL driver)',
    'mysqlnd' => 'MySQL Native Driver (recommended)'
];

$missingExtensions = [];
foreach ($mysqlExtensions as $ext => $description) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$ext}: Loaded\n";
    } else {
        echo "   ❌ {$ext}: MISSING - {$description}\n";
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "\n🚨 CRITICAL: Missing MySQL extensions detected!\n";
    echo "Required extensions: " . implode(', ', $missingExtensions) . "\n";
}

echo "\n🔍 STEP 2: PDO DRIVERS CHECK\n";
echo str_repeat("-", 40) . "\n";

if (extension_loaded('pdo')) {
    $availableDrivers = PDO::getAvailableDrivers();
    echo "   Available PDO drivers: " . implode(', ', $availableDrivers) . "\n";
    
    if (in_array('mysql', $availableDrivers)) {
        echo "   ✅ MySQL PDO driver: Available\n";
    } else {
        echo "   ❌ MySQL PDO driver: NOT AVAILABLE\n";
        echo "   💡 This is the root cause of your error!\n";
    }
} else {
    echo "   ❌ PDO extension not loaded\n";
}

echo "\n🔍 STEP 3: XAMPP CONFIGURATION CHECK\n";
echo str_repeat("-", 40) . "\n";

// Check XAMPP PHP configuration
$phpIniPath = php_ini_loaded_file();
echo "   PHP ini file: {$phpIniPath}\n";

if ($phpIniPath && file_exists($phpIniPath)) {
    $phpIniContent = file_get_contents($phpIniPath);
    
    // Check for MySQL extension configurations
    $extensionChecks = [
        'extension=pdo_mysql' => 'PDO MySQL extension',
        'extension=mysqli' => 'MySQLi extension',
        'extension=mysqlnd' => 'MySQL Native Driver'
    ];
    
    foreach ($extensionChecks as $setting => $description) {
        if (strpos($phpIniContent, $setting) !== false) {
            // Check if it's commented out
            $isCommented = strpos($phpIniContent, ';' . $setting) !== false;
            if ($isCommented) {
                echo "   ⚠️  {$setting}: Found but COMMENTED OUT\n";
            } else {
                echo "   ✅ {$setting}: Enabled\n";
            }
        } else {
            echo "   ❌ {$setting}: Not found in php.ini\n";
        }
    }
} else {
    echo "   ❌ Cannot read php.ini file\n";
}

echo "\n🔍 STEP 4: DATABASE CONNECTION TEST\n";
echo str_repeat("-", 40) . "\n";

// Test direct PDO connection
try {
    if (extension_loaded('pdo') && in_array('mysql', PDO::getAvailableDrivers())) {
        $host = '127.0.0.1';
        $dbname = 'invoicemod';
        $username = 'root';
        $password = '';
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        echo "   ✅ Direct PDO MySQL connection: SUCCESS\n";
        
        // Test sessions table access
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM sessions");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   ✅ Sessions table access: SUCCESS ({$result['count']} records)\n";
        
    } else {
        echo "   ❌ Cannot test connection: PDO MySQL driver not available\n";
    }
} catch (PDOException $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'could not find driver') !== false) {
        echo "   💡 This confirms the MySQL driver issue!\n";
    }
}

echo "\n🔧 STEP 5: AUTOMATIC FIXES\n";
echo str_repeat("-", 40) . "\n";

// Check if we can modify php.ini
if ($phpIniPath && is_writable($phpIniPath)) {
    echo "   📝 php.ini is writable, attempting automatic fixes...\n";
    
    $phpIniContent = file_get_contents($phpIniPath);
    $modified = false;
    
    // Uncomment MySQL extensions
    $extensionsToEnable = [
        ';extension=pdo_mysql' => 'extension=pdo_mysql',
        ';extension=mysqli' => 'extension=mysqli',
        ';extension=mysqlnd' => 'extension=mysqlnd'
    ];
    
    foreach ($extensionsToEnable as $commented => $uncommented) {
        if (strpos($phpIniContent, $commented) !== false) {
            $phpIniContent = str_replace($commented, $uncommented, $phpIniContent);
            echo "   ✅ Enabled: {$uncommented}\n";
            $modified = true;
        }
    }
    
    if ($modified) {
        file_put_contents($phpIniPath, $phpIniContent);
        echo "   ✅ php.ini updated successfully\n";
        echo "   ⚠️  RESTART REQUIRED: Please restart XAMPP Apache/PHP\n";
    } else {
        echo "   ℹ️  No changes needed in php.ini\n";
    }
    
} else {
    echo "   ⚠️  Cannot modify php.ini automatically (not writable)\n";
}

echo "\n🛠️ STEP 6: MANUAL FIX INSTRUCTIONS\n";
echo str_repeat("-", 40) . "\n";

echo "If automatic fixes didn't work, follow these manual steps:\n\n";

echo "1. XAMPP Control Panel Method:\n";
echo "   - Open XAMPP Control Panel\n";
echo "   - Click 'Config' next to Apache\n";
echo "   - Select 'PHP (php.ini)'\n";
echo "   - Find and uncomment these lines (remove semicolon):\n";
echo "     ;extension=pdo_mysql  →  extension=pdo_mysql\n";
echo "     ;extension=mysqli     →  extension=mysqli\n";
echo "     ;extension=mysqlnd    →  extension=mysqlnd\n";
echo "   - Save the file\n";
echo "   - Restart Apache in XAMPP\n\n";

echo "2. Manual php.ini Edit:\n";
echo "   - Open: {$phpIniPath}\n";
echo "   - Search for 'extension=pdo_mysql'\n";
echo "   - Remove semicolon (;) from the beginning\n";
echo "   - Do the same for mysqli and mysqlnd\n";
echo "   - Save and restart XAMPP\n\n";

echo "3. Verify Fix:\n";
echo "   - Run this script again\n";
echo "   - Try accessing your Laravel application\n";
echo "   - Check if login works without MySQL driver errors\n\n";

echo "🚀 STEP 7: LARAVEL CONFIGURATION CHECK\n";
echo str_repeat("-", 40) . "\n";

// Check Laravel database configuration
try {
    if (file_exists('.env')) {
        $envContent = file_get_contents('.env');
        
        // Extract database configuration
        preg_match('/DB_CONNECTION=(.*)/', $envContent, $dbConnection);
        preg_match('/DB_HOST=(.*)/', $envContent, $dbHost);
        preg_match('/DB_DATABASE=(.*)/', $envContent, $dbDatabase);
        
        $connection = isset($dbConnection[1]) ? trim($dbConnection[1]) : 'not set';
        $host = isset($dbHost[1]) ? trim($dbHost[1]) : 'not set';
        $database = isset($dbDatabase[1]) ? trim($dbDatabase[1]) : 'not set';
        
        echo "   Laravel DB Configuration:\n";
        echo "   - Connection: {$connection}\n";
        echo "   - Host: {$host}\n";
        echo "   - Database: {$database}\n";
        
        if ($connection === 'mysql') {
            echo "   ✅ Laravel configured for MySQL\n";
        } else {
            echo "   ⚠️  Laravel not configured for MySQL\n";
        }
        
    } else {
        echo "   ❌ .env file not found\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error checking Laravel config: " . $e->getMessage() . "\n";
}

echo "\n📋 SUMMARY & NEXT STEPS\n";
echo str_repeat("-", 40) . "\n";

if (empty($missingExtensions) && extension_loaded('pdo') && in_array('mysql', PDO::getAvailableDrivers())) {
    echo "✅ MySQL drivers are properly configured!\n";
    echo "If you're still getting errors, try:\n";
    echo "1. Clear Laravel caches: php artisan config:clear\n";
    echo "2. Restart your web server\n";
    echo "3. Check database credentials in .env\n";
} else {
    echo "❌ MySQL driver issues detected!\n";
    echo "Required actions:\n";
    echo "1. Enable MySQL extensions in php.ini (see manual instructions above)\n";
    echo "2. Restart XAMPP Apache\n";
    echo "3. Run this script again to verify\n";
    echo "4. Test your Laravel application\n";
}

echo "\n✅ MySQL driver diagnostic completed!\n";
