<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Models\Service;
use App\Models\InvoiceItem;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ReportingService
{
    /**
     * Get financial summary for a date range
     */
    public function getFinancialSummary(Carbon $startDate, Carbon $endDate): array
    {
        try {
            $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
                ->where('status', '!=', Invoice::DRAFT)
                ->get();

            $payments = Payment::where('is_approved', Payment::APPROVED)
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->get();

            $totalInvoiceAmount = $invoices->sum('final_amount');
            $totalPaymentsReceived = $payments->sum('amount');
            $invoiceCount = $invoices->count();

            $paidInvoicesCount = $invoices->where('status', Invoice::PAID)->count();
            $unpaidInvoicesCount = $invoices->where('status', Invoice::UNPAID)->count();
            $partiallyPaidCount = $invoices->where('status', Invoice::PARTIALLY)->count();
            $overdueCount = $invoices->where('status', Invoice::OVERDUE)->count();
            $processingCount = $invoices->where('status', Invoice::PROCESSING)->count();

            return [
                'total_invoices' => $invoiceCount,
                'total_invoice_amount' => (float) $totalInvoiceAmount,
                'total_payments_received' => (float) $totalPaymentsReceived,
                'outstanding_amount' => (float) max(0, $totalInvoiceAmount - $totalPaymentsReceived),
                'paid_invoices' => $paidInvoicesCount,
                'unpaid_invoices' => $unpaidInvoicesCount,
                'partially_paid_invoices' => $partiallyPaidCount,
                'overdue_invoices' => $overdueCount,
                'processing_invoices' => $processingCount,
                'average_invoice_value' => $invoiceCount > 0 ? (float) ($totalInvoiceAmount / $invoiceCount) : 0,
                'payment_success_rate' => $invoiceCount > 0 ? (float) (($paidInvoicesCount / $invoiceCount) * 100) : 0,
                'collection_efficiency' => $totalInvoiceAmount > 0 ? (float) (($totalPaymentsReceived / $totalInvoiceAmount) * 100) : 0,
            ];
        } catch (\Exception $e) {
            \Log::error('ReportingService getFinancialSummary error: ' . $e->getMessage());

            return [
                'total_invoices' => 0,
                'total_invoice_amount' => 0.0,
                'total_payments_received' => 0.0,
                'outstanding_amount' => 0.0,
                'paid_invoices' => 0,
                'unpaid_invoices' => 0,
                'partially_paid_invoices' => 0,
                'overdue_invoices' => 0,
                'processing_invoices' => 0,
                'average_invoice_value' => 0.0,
                'payment_success_rate' => 0.0,
                'collection_efficiency' => 0.0,
            ];
        }
    }

    /**
     * Get revenue trends over time
     */
    public function getRevenueTrends(Carbon $startDate, Carbon $endDate, string $interval = 'daily'): array
    {
        try {
            $dateFormat = match($interval) {
                'daily' => '%Y-%m-%d',
                'weekly' => '%Y-%u',
                'monthly' => '%Y-%m',
                'yearly' => '%Y',
                default => '%Y-%m-%d'
            };

            $payments = Payment::where('is_approved', Payment::APPROVED)
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->select(
                    DB::raw("DATE_FORMAT(payment_date, '{$dateFormat}') as period"),
                    DB::raw('SUM(amount) as total_revenue'),
                    DB::raw('COUNT(*) as payment_count'),
                    DB::raw('AVG(amount) as avg_payment_amount')
                )
                ->groupBy('period')
                ->orderBy('period')
                ->get();

            return $payments->map(function ($payment) {
                return [
                    'period' => $payment->period,
                    'total_revenue' => (float) $payment->total_revenue,
                    'payment_count' => (int) $payment->payment_count,
                    'avg_payment_amount' => (float) $payment->avg_payment_amount,
                ];
            })->toArray();
        } catch (\Exception $e) {
            \Log::error('ReportingService getRevenueTrends error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get client performance metrics
     */
    public function getClientPerformance(Carbon $startDate, Carbon $endDate): Collection
    {
        return Client::with(['user', 'invoices' => function($query) use ($startDate, $endDate) {
            $query->whereBetween('invoice_date', [$startDate, $endDate])
                  ->where('status', '!=', Invoice::DRAFT);
        }])
        ->get()
        ->map(function($client) {
            $invoices = $client->invoices;
            $totalInvoiceAmount = $invoices->sum('final_amount');
            
            $payments = Payment::whereIn('invoice_id', $invoices->pluck('id'))
                ->where('is_approved', Payment::APPROVED)
                ->sum('amount');

            return [
                'client_id' => $client->id,
                'client_name' => $client->user->first_name . ' ' . $client->user->last_name,
                'client_email' => $client->user->email,
                'total_invoices' => $invoices->count(),
                'total_invoice_amount' => $totalInvoiceAmount,
                'total_payments' => $payments,
                'outstanding_amount' => $totalInvoiceAmount - $payments,
                'payment_ratio' => $totalInvoiceAmount > 0 ? ($payments / $totalInvoiceAmount) * 100 : 0,
                'avg_invoice_value' => $invoices->count() > 0 ? $totalInvoiceAmount / $invoices->count() : 0,
                'last_invoice_date' => $invoices->max('invoice_date'),
                'last_payment_date' => Payment::whereIn('invoice_id', $invoices->pluck('id'))
                    ->where('is_approved', Payment::APPROVED)
                    ->max('payment_date'),
            ];
        })
        ->sortByDesc('total_invoice_amount');
    }

    /**
     * Get product/service performance analysis
     */
    public function getProductPerformance(Carbon $startDate, Carbon $endDate): Collection
    {
        $invoiceItems = InvoiceItem::whereHas('invoice', function($query) use ($startDate, $endDate) {
            $query->whereBetween('invoice_date', [$startDate, $endDate])
                  ->where('status', '!=', Invoice::DRAFT);
        })
        ->with('invoice', 'product')
        ->get();

        // Group by product (both database products and free-form entries)
        $productStats = $invoiceItems->groupBy(function($item) {
            return $item->product_id ? 'product_' . $item->product_id : 'freeform_' . md5($item->product_name);
        })
        ->map(function($items, $key) {
            $firstItem = $items->first();
            $isProduct = $firstItem->product_id !== null;
            
            return [
                'type' => $isProduct ? 'product' : 'freeform',
                'product_id' => $firstItem->product_id,
                'product_name' => $isProduct ? $firstItem->product->name : $firstItem->product_name,
                'product_code' => $isProduct ? $firstItem->product->code : null,
                'total_quantity' => $items->sum('quantity'),
                'total_amount' => $items->sum('amount'),
                'invoice_count' => $items->unique('invoice_id')->count(),
                'avg_price' => $items->avg('price'),
                'avg_quantity' => $items->avg('quantity'),
            ];
        })
        ->sortByDesc('total_amount');

        return $productStats;
    }

    /**
     * Get overdue invoices report
     */
    public function getOverdueInvoices(): Collection
    {
        try {
            return Invoice::where('status', '!=', Invoice::PAID)
                ->where('status', '!=', Invoice::DRAFT)
                ->where('due_date', '<', Carbon::now())
                ->with(['client.user', 'payments'])
                ->get()
                ->map(function($invoice) {
                    try {
                        $totalPaid = $invoice->payments->where('is_approved', Payment::APPROVED)->sum('amount');
                        $daysOverdue = Carbon::now()->diffInDays(Carbon::parse($invoice->due_date));
                        $outstandingAmount = max(0, $invoice->final_amount - $totalPaid);

                        return [
                            'invoice_id' => $invoice->invoice_id,
                            'client_name' => ($invoice->client && $invoice->client->user)
                                ? $invoice->client->user->first_name . ' ' . $invoice->client->user->last_name
                                : 'Unknown Client',
                            'client_email' => ($invoice->client && $invoice->client->user)
                                ? $invoice->client->user->email
                                : '<EMAIL>',
                            'invoice_date' => $invoice->invoice_date,
                            'due_date' => $invoice->due_date,
                            'total_amount' => (float) $invoice->final_amount,
                            'paid_amount' => (float) $totalPaid,
                            'outstanding_amount' => (float) $outstandingAmount,
                            'days_overdue' => (int) $daysOverdue,
                            'status' => $invoice->status,
                            'status_label' => $invoice->status_label ?? 'Unknown',
                            'overdue_severity' => $this->getOverdueSeverity($daysOverdue),
                        ];
                    } catch (\Exception $e) {
                        \Log::error('Error processing overdue invoice: ' . $e->getMessage());
                        return null;
                    }
                })
                ->filter() // Remove null entries
                ->sortByDesc('days_overdue');
        } catch (\Exception $e) {
            \Log::error('ReportingService getOverdueInvoices error: ' . $e->getMessage());
            return collect([]);
        }
    }

    /**
     * Get overdue severity level
     */
    private function getOverdueSeverity(int $daysOverdue): string
    {
        if ($daysOverdue <= 7) return 'low';
        if ($daysOverdue <= 30) return 'medium';
        if ($daysOverdue <= 60) return 'high';
        return 'critical';
    }

    /**
     * Get tax summary report
     */
    public function getTaxSummary(Carbon $startDate, Carbon $endDate): array
    {
        $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', Invoice::DRAFT)
            ->with('taxes')
            ->get();

        $taxSummary = [];
        $totalTaxAmount = 0;

        foreach ($invoices as $invoice) {
            foreach ($invoice->taxes as $tax) {
                $taxAmount = ($invoice->amount * $tax->value) / 100;
                
                if (!isset($taxSummary[$tax->name])) {
                    $taxSummary[$tax->name] = [
                        'tax_name' => $tax->name,
                        'tax_rate' => $tax->value,
                        'total_amount' => 0,
                        'invoice_count' => 0,
                    ];
                }
                
                $taxSummary[$tax->name]['total_amount'] += $taxAmount;
                $taxSummary[$tax->name]['invoice_count']++;
                $totalTaxAmount += $taxAmount;
            }
        }

        return [
            'tax_breakdown' => array_values($taxSummary),
            'total_tax_amount' => $totalTaxAmount,
            'total_invoices_with_tax' => $invoices->filter(function($invoice) {
                return $invoice->taxes->count() > 0;
            })->count(),
        ];
    }

    /**
     * Get payment method analysis
     */
    public function getPaymentMethodAnalysis(Carbon $startDate, Carbon $endDate): Collection
    {
        return Payment::where('is_approved', Payment::APPROVED)
            ->whereBetween('payment_date', [$startDate, $endDate])
            ->select('payment_mode', DB::raw('COUNT(*) as transaction_count'), DB::raw('SUM(amount) as total_amount'))
            ->groupBy('payment_mode')
            ->get()
            ->map(function($payment) {
                return [
                    'payment_method' => Payment::PAYMENT_MODE_NEW[$payment->payment_mode] ?? 'Unknown',
                    'transaction_count' => $payment->transaction_count,
                    'total_amount' => $payment->total_amount,
                    'average_amount' => $payment->total_amount / $payment->transaction_count,
                ];
            })
            ->sortByDesc('total_amount');
    }

    /**
     * Get invoice status distribution
     */
    public function getInvoiceStatusDistribution(Carbon $startDate, Carbon $endDate): array
    {
        $invoices = Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', Invoice::DRAFT)
            ->select('status', DB::raw('COUNT(*) as count'), DB::raw('SUM(final_amount) as total_amount'))
            ->groupBy('status')
            ->get();

        return $invoices->map(function($invoice) {
            return [
                'status' => $this->getStatusLabel($invoice->status),
                'count' => $invoice->count,
                'total_amount' => $invoice->total_amount,
                'percentage' => 0, // Will be calculated in the calling method
            ];
        })->toArray();
    }

    /**
     * Get status label for display
     */
    private function getStatusLabel(int $status): string
    {
        return match($status) {
            Invoice::PAID => 'Paid',
            Invoice::UNPAID => 'Unpaid',
            Invoice::PARTIALLY => 'Partially Paid',
            Invoice::PROCESSING => 'Processing',
            Invoice::DRAFT => 'Draft',
            default => 'Unknown'
        };
    }

    /**
     * Get comprehensive dashboard metrics
     */
    public function getDashboardMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $financial = $this->getFinancialSummary($startDate, $endDate);
        $clientPerformance = $this->getClientPerformance($startDate, $endDate);
        $productPerformance = $this->getProductPerformance($startDate, $endDate);
        $overdue = $this->getOverdueInvoices();

        return [
            'financial_summary' => $financial,
            'top_clients' => $clientPerformance->take(5),
            'top_products' => $productPerformance->take(5),
            'overdue_summary' => [
                'count' => $overdue->count(),
                'total_amount' => $overdue->sum('outstanding_amount'),
                'avg_days_overdue' => $overdue->avg('days_overdue'),
            ],
            'recent_trends' => $this->getRevenueTrends($startDate, $endDate, 'daily'),
        ];
    }

    /**
     * Export data to CSV format
     */
    public function exportToCSV(string $reportType, Carbon $startDate, Carbon $endDate): string
    {
        $data = match($reportType) {
            'financial' => $this->getFinancialSummary($startDate, $endDate),
            'clients' => $this->getClientPerformance($startDate, $endDate)->toArray(),
            'products' => $this->getProductPerformance($startDate, $endDate)->toArray(),
            'overdue' => $this->getOverdueInvoices()->toArray(),
            default => []
        };

        if (empty($data)) {
            return '';
        }

        $output = fopen('php://temp', 'r+');

        // Add headers
        if (is_array($data) && !empty($data)) {
            $firstRow = is_array($data[0]) ? $data[0] : $data;
            fputcsv($output, array_keys($firstRow));

            // Add data rows
            if (isset($data[0])) {
                foreach ($data as $row) {
                    fputcsv($output, $row);
                }
            } else {
                fputcsv($output, $data);
            }
        }

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }
}
