<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if services table exists, if not create it
        if (!Schema::hasTable('services')) {
            Schema::create('services', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('code');
                $table->unsignedBigInteger('category_id');
                $table->foreign('category_id')->references('id')
                    ->on('categories')
                    ->onUpdate('cascade')
                    ->onDelete('cascade');
                $table->float('unit_price');
                $table->string('description')->nullable();
                $table->timestamps();
            });
        }

        // Ensure quote_items table references services properly
        if (Schema::hasTable('quote_items')) {
            Schema::table('quote_items', function (Blueprint $table) {
                // Check if we need to update the foreign key
                if (Schema::hasColumn('quote_items', 'product_id')) {
                    // Drop existing foreign key if it exists
                    try {
                        $table->dropForeign(['product_id']);
                    } catch (\Exception $e) {
                        // Foreign key might not exist, continue
                    }

                    // Add foreign key to services table
                    $table->foreign('product_id')->references('id')->on('services')->onDelete('cascade');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is for ensuring table exists, so we don't drop it in down()
        // The original table creation migration should handle the down() method
    }
};
