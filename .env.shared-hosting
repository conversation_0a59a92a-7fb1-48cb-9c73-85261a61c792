# =============================================================================
# SHARED HOSTING ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and update the values for your shared hosting environment
# This configuration is optimized to prevent 419 "Page Expired" errors

APP_NAME="Invoice Management System"
APP_ENV=production
APP_KEY=base64:Xmf+fskyzxe1qaDZZNWAYkocjcXFlV8KAHh7LSzOoZE=
APP_DEBUG=false
APP_TIMEZONE=UTC
# UPDATE THIS: Replace with your actual shared hosting domain
APP_URL=https://yourdomain.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_LEVEL=error

# =============================================================================
# DATABASE CONFIGURATION - UPDATE THESE VALUES
# =============================================================================
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
# UPDATE THESE: Replace with your shared hosting database credentials
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

# =============================================================================
# SESSION CONFIGURATION - CRITICAL FOR 419 ERROR FIX
# =============================================================================
# Use database sessions for shared hosting reliability
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
# Use null for domain to work with any domain
SESSION_DOMAIN=null
# Root path for cookies
SESSION_PATH=/
# Set to true for HTTPS (recommended for production)
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_DRIVER=database
CACHE_STORE=database
CACHE_PREFIX=

# =============================================================================
# QUEUE AND BROADCAST CONFIGURATION
# =============================================================================
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

# =============================================================================
# EXTERNAL SERVICES (Update as needed)
# =============================================================================
MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# =============================================================================
# MAIL CONFIGURATION
# =============================================================================
MAIL_MAILER=smtp
MAIL_SCHEME=null
# UPDATE THESE: Replace with your shared hosting mail settings
MAIL_HOST=mail.yourdomain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# =============================================================================
# AWS CONFIGURATION (Optional)
# =============================================================================
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_APP_NAME="${APP_NAME}"

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
TWILIO_SID=
TWILIO_TOKEN=
TWILIO_WHATSAPP_NUMBER=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

STRIPE_KEY=
STRIPE_SECRET=
STRIPE_WEBHOOK_SECRET_KEY=

PAYPAL_CLIENT_ID=
PAYPAL_SECRET=
PAYPAL_MODE="live"

PAYSTACK_PUBLIC_KEY=
PAYSTACK_SECRET_KEY=
PAYSTACK_PAYMENT_URL=
PAYSTACK_MERCHANT_EMAIL=

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

RAZORPAY_KEY=
RAZORPAY_SECRET=

ENABLE_UPGRADE_ROUTE=false
