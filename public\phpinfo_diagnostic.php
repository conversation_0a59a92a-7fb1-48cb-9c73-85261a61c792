<?php
/**
 * PHP Diagnostic Script for MySQL Driver Issues
 * Access via: http://127.0.0.1:8000/phpinfo_diagnostic.php
 */

echo "<h1>🔍 PHP MySQL Driver Diagnostic</h1>";
echo "<hr>";

echo "<h2>📊 PHP Extensions Status</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Extension</th><th>Status</th><th>Details</th></tr>";

// Check PDO
$pdo_loaded = extension_loaded('pdo');
echo "<tr><td>PDO</td><td>" . ($pdo_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($pdo_loaded ? 'Core PDO extension available' : 'PDO extension missing') . "</td></tr>";

// Check PDO MySQL
$pdo_mysql_loaded = extension_loaded('pdo_mysql');
echo "<tr><td>PDO MySQL</td><td>" . ($pdo_mysql_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($pdo_mysql_loaded ? 'MySQL PDO driver available' : 'MySQL PDO driver missing') . "</td></tr>";

// Check MySQL
$mysql_loaded = extension_loaded('mysql');
echo "<tr><td>MySQL (Legacy)</td><td>" . ($mysql_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($mysql_loaded ? 'Legacy MySQL extension (deprecated)' : 'Legacy MySQL extension not loaded') . "</td></tr>";

// Check MySQLi
$mysqli_loaded = extension_loaded('mysqli');
echo "<tr><td>MySQLi</td><td>" . ($mysqli_loaded ? '✅ LOADED' : '❌ NOT LOADED') . "</td><td>" . ($mysqli_loaded ? 'MySQLi extension available' : 'MySQLi extension missing') . "</td></tr>";

echo "</table>";

echo "<h2>🔧 PDO Drivers Available</h2>";
if ($pdo_loaded) {
    $drivers = PDO::getAvailableDrivers();
    echo "<ul>";
    foreach ($drivers as $driver) {
        echo "<li><strong>{$driver}</strong></li>";
    }
    echo "</ul>";
    
    if (in_array('mysql', $drivers)) {
        echo "<p>✅ <strong>MySQL PDO driver is available!</strong></p>";
    } else {
        echo "<p>❌ <strong>MySQL PDO driver is NOT available!</strong></p>";
    }
} else {
    echo "<p>❌ PDO extension not loaded</p>";
}

echo "<h2>🌐 PHP Configuration</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>PHP Version</td><td>" . phpversion() . "</td></tr>";
echo "<tr><td>PHP SAPI</td><td>" . php_sapi_name() . "</td></tr>";
echo "<tr><td>Server Software</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Document Root</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Script Filename</td><td>" . ($_SERVER['SCRIPT_FILENAME'] ?? 'Unknown') . "</td></tr>";
echo "</table>";

echo "<h2>🔍 Extension Loading Test</h2>";
try {
    if (class_exists('PDO')) {
        echo "<p>✅ PDO class exists</p>";
        
        if (in_array('mysql', PDO::getAvailableDrivers())) {
            echo "<p>✅ MySQL driver available in PDO</p>";
            
            // Test connection
            try {
                $host = '127.0.0.1';
                $dbname = 'invoicemod';
                $username = 'root';
                $password = '';
                
                $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]);
                
                echo "<p>✅ <strong>Direct PDO MySQL connection: SUCCESS!</strong></p>";
                
                // Test a simple query
                $stmt = $pdo->query("SELECT 1 as test");
                $result = $stmt->fetch();
                echo "<p>✅ Test query result: " . $result['test'] . "</p>";
                
            } catch (PDOException $e) {
                echo "<p>❌ <strong>Direct PDO MySQL connection failed:</strong> " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>❌ MySQL driver NOT available in PDO</p>";
        }
    } else {
        echo "<p>❌ PDO class does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Extension test failed: " . $e->getMessage() . "</p>";
}

echo "<h2>📁 PHP INI Information</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Loaded php.ini</td><td>" . (php_ini_loaded_file() ?: 'None') . "</td></tr>";
echo "<tr><td>Additional INI files</td><td>" . (php_ini_scanned_files() ?: 'None') . "</td></tr>";
echo "<tr><td>Extension Directory</td><td>" . ini_get('extension_dir') . "</td></tr>";
echo "</table>";

echo "<h2>🔧 Recommended Solutions</h2>";
if (!$pdo_mysql_loaded) {
    echo "<div style='background: #ffebee; padding: 10px; border-left: 4px solid #f44336;'>";
    echo "<h3>❌ PDO MySQL Extension Not Loaded</h3>";
    echo "<p><strong>Solutions:</strong></p>";
    echo "<ol>";
    echo "<li>Enable <code>extension=pdo_mysql</code> in php.ini</li>";
    echo "<li>Restart Apache/Web Server</li>";
    echo "<li>Check if php_pdo_mysql.dll exists in extension directory</li>";
    echo "<li>Verify XAMPP MySQL service is running</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; padding: 10px; border-left: 4px solid #4caf50;'>";
    echo "<h3>✅ PDO MySQL Extension Loaded Successfully</h3>";
    echo "<p>The MySQL driver issue might be related to Laravel configuration or caching.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Generated: " . date('Y-m-d H:i:s') . "</small></p>";
?>
