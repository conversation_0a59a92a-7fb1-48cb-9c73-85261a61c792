<?php $__env->startComponent('mail::layout'); ?>
    
    <?php $__env->slot('header'); ?>
        <?php $__env->startComponent('mail::header', ['url' => config('app.url')]); ?>
            <img src="<?php echo e(asset(getLogoUrl())); ?>" class="logo" alt="<?php echo e(getAppName()); ?>">
        <?php echo $__env->renderComponent(); ?>
    <?php $__env->endSlot(); ?>

    
    <div>
        <h2><?php echo e(__('messages.mail_content.welcome') . ' ' . __('messages.mail_content.to')); ?> <?php echo e($clientName); ?>, <b></b>
        </h2><br>
        <p> <?php echo e(__('messages.mail_content.your_account_has_been_successfully_created_on') . ' ' . getAppName()); ?></p>
        <p><?php echo e(__('messages.mail_content.your_email_address_is')); ?> <strong><?php echo e($userName); ?></strong></p>
        <p><?php echo e(__('messages.mail_content.in')); ?> <?php echo e(getAppName()); ?>,
            <?php echo e(__('messages.mail_content.you_can_manage_all_of_your_invoices')); ?>.</p>
        <p><?php echo e(__('messages.mail_content.thank_for_joining_and_have_a_great_day')); ?>!</p><br>
        <div style="display: flex;justify-content: center">
            <a href="<?php echo e(route('client.password.reset', $client_id)); ?>"
                style="padding: 7px 15px;text-decoration: none;font-size: 14px;background-color:  #0000FF;font-weight: 500;border: none;border-radius: 8px;color: white">
                <?php echo e(__('messages.mail_content.join_now')); ?>

            </a>
        </div>
    </div>

    
    <?php $__env->slot('footer'); ?>
        <?php $__env->startComponent('mail::footer'); ?>
            <h6>© <?php echo e(date('Y')); ?> <?php echo e(getAppName()); ?>.</h6>
        <?php echo $__env->renderComponent(); ?>
    <?php $__env->endSlot(); ?>
<?php echo $__env->renderComponent(); ?>
<?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/emails/create_new_client_mail.blade.php ENDPATH**/ ?>