<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Add missing name column if it doesn't exist
            if (!Schema::hasColumn('clients', 'name')) {
                $table->string('name')->after('id');
            }
            
            // Add other potentially missing columns
            if (!Schema::hasColumn('clients', 'email')) {
                $table->string('email')->nullable()->after('name');
            }
            
            if (!Schema::hasColumn('clients', 'phone')) {
                $table->string('phone')->nullable()->after('email');
            }
            
            if (!Schema::hasColumn('clients', 'address')) {
                $table->text('address')->nullable()->after('phone');
            }
            
            if (!Schema::hasColumn('clients', 'city')) {
                $table->string('city')->nullable()->after('address');
            }
            
            if (!Schema::hasColumn('clients', 'state')) {
                $table->string('state')->nullable()->after('city');
            }
            
            if (!Schema::hasColumn('clients', 'zip')) {
                $table->string('zip')->nullable()->after('state');
            }
            
            if (!Schema::hasColumn('clients', 'country')) {
                $table->string('country')->nullable()->after('zip');
            }
            
            // Add indexes for better performance
            $table->index('name');
            $table->index('email');
        });
    }

    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->dropColumn(['name', 'email', 'phone', 'address', 'city', 'state', 'zip', 'country']);
            $table->dropIndex(['name']);
            $table->dropIndex(['email']);
        });
    }
};
