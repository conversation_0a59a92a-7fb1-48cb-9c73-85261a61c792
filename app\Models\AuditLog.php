<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'model_type',
        'model_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'description',
        'severity',
        'tags',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'tags' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function model()
    {
        return $this->morphTo();
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function scopeForModel($query, string $modelType, int $modelId = null)
    {
        $query = $query->where('model_type', $modelType);
        
        if ($modelId) {
            $query->where('model_id', $modelId);
        }
        
        return $query;
    }

    public function scopeForAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    public function getFormattedChangesAttribute(): array
    {
        $changes = [];
        
        if ($this->old_values && $this->new_values) {
            foreach ($this->new_values as $field => $newValue) {
                $oldValue = $this->old_values[$field] ?? null;
                
                if ($oldValue !== $newValue) {
                    $changes[] = [
                        'field' => $field,
                        'old' => $oldValue,
                        'new' => $newValue,
                    ];
                }
            }
        }
        
        return $changes;
    }

    public function getSeverityColorAttribute(): string
    {
        return match ($this->severity) {
            'critical' => 'danger',
            'high' => 'warning',
            'medium' => 'info',
            'low' => 'success',
            default => 'gray',
        };
    }

    public function getSeverityIconAttribute(): string
    {
        return match ($this->severity) {
            'critical' => 'heroicon-o-exclamation-triangle',
            'high' => 'heroicon-o-exclamation-circle',
            'medium' => 'heroicon-o-information-circle',
            'low' => 'heroicon-o-check-circle',
            default => 'heroicon-o-document-text',
        };
    }

    public static function logActivity(
        string $action,
        Model $model = null,
        array $oldValues = [],
        array $newValues = [],
        string $description = null,
        string $severity = 'medium',
        array $tags = []
    ): self {
        $user = auth()->user();
        $request = request();

        return static::create([
            'user_id' => $user?->id,
            'action' => $action,
            'model_type' => $model ? get_class($model) : null,
            'model_id' => $model?->id,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'description' => $description,
            'severity' => $severity,
            'tags' => $tags,
        ]);
    }

    public static function logLogin(User $user, bool $successful = true): self
    {
        return static::logActivity(
            $successful ? 'login_success' : 'login_failed',
            $user,
            [],
            [],
            $successful ? 'User logged in successfully' : 'Failed login attempt',
            $successful ? 'low' : 'high',
            ['authentication', 'security']
        );
    }

    public static function logLogout(User $user): self
    {
        return static::logActivity(
            'logout',
            $user,
            [],
            [],
            'User logged out',
            'low',
            ['authentication']
        );
    }

    public static function logModelCreated(Model $model, string $description = null): self
    {
        return static::logActivity(
            'created',
            $model,
            [],
            $model->toArray(),
            $description ?? class_basename($model) . ' created',
            'medium',
            ['crud', 'create']
        );
    }

    public static function logModelUpdated(Model $model, array $oldValues, string $description = null): self
    {
        return static::logActivity(
            'updated',
            $model,
            $oldValues,
            $model->toArray(),
            $description ?? class_basename($model) . ' updated',
            'medium',
            ['crud', 'update']
        );
    }

    public static function logModelDeleted(Model $model, string $description = null): self
    {
        return static::logActivity(
            'deleted',
            $model,
            $model->toArray(),
            [],
            $description ?? class_basename($model) . ' deleted',
            'high',
            ['crud', 'delete']
        );
    }

    public static function logSecurityEvent(string $event, string $description, string $severity = 'high'): self
    {
        return static::logActivity(
            'security_event',
            null,
            [],
            ['event' => $event],
            $description,
            $severity,
            ['security', 'alert']
        );
    }

    public static function logSystemEvent(string $event, string $description, array $data = []): self
    {
        return static::logActivity(
            'system_event',
            null,
            [],
            array_merge(['event' => $event], $data),
            $description,
            'medium',
            ['system']
        );
    }
}
