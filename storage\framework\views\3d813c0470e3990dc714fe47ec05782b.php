<?php $__env->startComponent('mail::layout'); ?>
    
    <?php $__env->slot('header'); ?>
        <?php $__env->startComponent('mail::header', ['url' => config('app.url')]); ?>
            <img src="<?php echo e(asset(getLogoUrl())); ?>" class="logo" alt="<?php echo e(getAppName()); ?>">
        <?php echo $__env->renderComponent(); ?>
    <?php $__env->endSlot(); ?>

    
    <div>
        <h2><?php echo e(__('messages.dear')); ?> <?php echo e($clientName); ?>, <b></b></h2><br>
        <p><?php echo e(__('messages.i_hope_you_are_well')); ?>.</p>
        <p><?php echo e(__('messages.please_see_attached_the_invoice')); ?> <?php echo e(' #' . $invoiceNumber); ?>.
            <?php echo e(__('messages.the_invoice_is_due_by')); ?>

            <?php echo e($dueDate); ?>.</p>
        <p><?php echo e(__('messages.please_do_not_hesitate_to_get_in_touch')); ?>.</p>
        <p><?php echo e(__('messages.also_you_can_see_the_attachment_invoice_PDF')); ?>.</p><br>
        <div style="display: flex;justify-content: center">
            <a href="<?php echo e(route('filament.admin.resources.invoices.view', ['record' => $id])); ?>"
                style="padding: 7px 15px;text-decoration: none;font-size: 14px;background-color: #df4645;font-weight: 500;border: none;border-radius: 8px;color: white;margin-right: 5px;">
                <?php echo e(__('messages.view_invoice')); ?>

            </a>
            <a href="<?php echo e(route('invoice-show-url', ['invoiceId' => $invoiceId])); ?>"
                style="padding: 7px 15px;text-decoration: none;font-size: 14px;background-color: #df4645;font-weight: 500;border: none;border-radius: 8px;color: white; margin-left: 5px;">
                <?php echo e(__('messages.public_view')); ?>

            </a>
        </div>
    </div>

    
    <?php $__env->slot('footer'); ?>
        <?php $__env->startComponent('mail::footer'); ?>
            <h6>© <?php echo e(date('Y')); ?> <?php echo e(getAppName()); ?>.</h6>
        <?php echo $__env->renderComponent(); ?>
    <?php $__env->endSlot(); ?>
<?php echo $__env->renderComponent(); ?>
<?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/emails/create_invoice_client_mail.blade.php ENDPATH**/ ?>