<?php

namespace App\Filament\Client\Resources;

use App\AdminDashboardSidebarSorting;
use App\Filament\Client\Resources\TransactionResource\Pages\ListTransactions;
use App\Filament\Client\Resources\TransactionResource\Pages\EditTransaction;
use App\Filament\Resources\ClientResource;
use App\Models\Payment;
use App\Models\Role;
use Filament\Forms\Form;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Tables\Actions;
use Filament\Tables\Actions\ActionGroup;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TransactionResource extends Resource
{
    protected static ?string $model = Payment::class;

    protected static ?string $navigationIcon = 'heroicon-s-numbered-list';

    protected static ?int $navigationSort = AdminDashboardSidebarSorting::TRANSACTIONS->value;

    public static function getNavigationLabel(): string
    {
        return __('messages.transactions');
    }

    public static function getModelLabel(): string
    {
        return __('messages.transactions');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)->schema([
                    TextInput::make('invoice.invoice_id')
                        ->label(__('messages.invoice.invoice_id'))
                        ->disabled()
                        ->dehydrated(false),
                    Select::make('is_approved')
                        ->label(__('messages.common.status'))
                        ->options(Payment::PAYMENT_STATUS_NEW)
                        ->required()
                        ->native(false),
                ]),
                Grid::make(2)->schema([
                    TextInput::make('amount')
                        ->label(__('messages.quote.amount'))
                        ->required()
                        ->numeric()
                        ->minValue(0.01)
                        ->disabled(),
                    DatePicker::make('payment_date')
                        ->label(__('messages.payment.payment_date'))
                        ->default(Carbon::today())
                        ->required(),
                ]),
                Grid::make(2)->schema([
                    Select::make('payment_mode')
                        ->label(__('messages.payment.payment_mode'))
                        ->options(Payment::PAYMENT_MODE_NEW)
                        ->required()
                        ->native(false)
                        ->disabled(),
                    TextInput::make('transaction_id')
                        ->label(__('messages.payment.transaction_id'))
                        ->disabled()
                        ->dehydrated(false),
                ]),
                Textarea::make('notes')
                    ->label(__('messages.quote.note'))
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function ($query) {
                if (getLogInUser()->hasRole(Role::CLIENT)) {
                    $query->whereHas('invoice.client', function ($q) {
                        $q->where('user_id', Auth::id());
                    });
                }
            })
            ->defaultSort('id', 'desc')
            ->columns([
                TextColumn::make('transaction_id')
                    ->label(__('messages.payment.transaction_id'))
                    ->default(__('messages.common.n/a'))
                    ->hidden(getLogInUser()->hasrole(Role::CLIENT))
                    ->searchable(),
                TextColumn::make('invoice.invoice_id')
                    ->label(__('messages.invoice.invoice_id'))
                    ->badge('info')
                    ->url(fn($record) => (InvoiceResource::getUrl('view', ['record' => $record->invoice->id])))
                    ->searchable(),
                TextColumn::make('invoice.client.user.full_name')
                    ->label(__('messages.invoice.client'))
                    ->color('primary')
                    ->html()
                    ->formatStateUsing(fn($record) => "<a href='" . ClientResource::getUrl('view', ['record' => $record->invoice->client->id]) . "' class='text-indigo-600 hover:underline'>" . e($record->invoice->client->user->full_name) . "</a>")
                    ->weight(FontWeight::SemiBold)
                    ->searchable(['first_name', 'last_name'])
                    ->description(fn($record) => $record->invoice->client->user->email ?? __('messages.common.n/a'))
                    ->hidden(getLogInUser()->hasrole(Role::CLIENT)),
                TextColumn::make('payment_date')
                    ->label(__('messages.payment.payment_date'))
                    ->badge('primary')
                    ->date('Y-m-d')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('amount')
                    ->label(__('messages.quote.amount'))
                    ->formatStateUsing(function ($record) {
                        return getInvoiceCurrencyAmount($record->amount, $record->invoice->currency_id, true);
                    })
                    ->sortable()
                    ->searchable(),
                TextColumn::make('is_approved')
                    ->label(__('messages.setting.payment_approved'))
                    ->view('filament.clusters.transactions.columns.is_approved')
                    ->hidden(getLogInUser()->hasrole(Role::CLIENT)),
                TextColumn::make('payment_mode')
                    ->label(__('messages.payment.payment_mode'))
                    ->formatStateUsing(function ($record) {
                        return Payment::PAYMENT_MODE[$record->payment_mode];
                    })
                    ->badge('info')
                    ->description(fn($record) => view('filament.clusters.transactions.columns.note_modal', ['record' => $record])),
                TextColumn::make('user_id')
                    ->label(__('messages.common.status'))
                    ->formatStateUsing(function ($record) {
                        return Payment::PAYMENT_MODE[$record->payment_mode];
                    })
                    ->view('filament.clusters.transactions.columns.status')
                    ->badge('info'),
                TextColumn::make('payment_attachment')
                    ->label(__('messages.common.attachment'))
                    ->html()
                    ->default(__('messages.common.n/a'))
                    ->view('filament.clusters.transactions.columns.attachment')
                // ->getStateUsing(
                //     function ($record) {
                //         $attachment = $record->getMedia(Payment::PAYMENT_ATTACHMENT)->first()?->getUrl();
                //         // dump($attachment);
                //         // if (getLogInUser()->hasrole(Role::CLIENT)) {
                //         //     if ($record->invoice->client->user_id !== getLogInUserId()) {
                //         //         Notification::make()
                //         //             ->danger()
                //         //             ->title(__('messages.flash.seems_you_are_not_allowed_to_access_this_record'))
                //         //             ->send();
                //         //         $this->halt();
                //         //         return;
                //         //     }
                //         // }

                //         if ($attachment) {
                //             return '<a href="' . $attachment . '" style="margin-left: -17px;color: #6571ff" class="hoverLink  " target="_blank" download>Download</a>';
                //         } else {
                //             return '';
                //         }
                //     }
                // )

            ])
            ->filters([
                SelectFilter::make('payment_mode')
                    ->label(__('messages.payment.payment_mode'))
                    ->options([Payment::PAYMENT_MODE_NEW])
                    ->native(false),
                SelectFilter::make('is_approved')
                    ->label(__('messages.common.status'))
                    ->options([Payment::PAYMENT_STATUS_NEW])
                    ->native(false),
            ])
            ->actions([
                ActionGroup::make([
                    Actions\EditAction::make()
                        ->label(__('messages.common.edit'))
                        ->icon('heroicon-o-pencil-square')
                        ->visible(fn($record) => true), // Always visible for all statuses
                    Actions\Action::make('approve')
                        ->label(__('messages.common.approve'))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->visible(fn($record) => $record->is_approved == Payment::PENDING)
                        ->requiresConfirmation()
                        ->action(function ($record, $livewire) {
                            $livewire->changeStatus(Payment::APPROVED, $record);
                        }),
                    Actions\Action::make('reject')
                        ->label(__('messages.common.reject'))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->visible(fn($record) => $record->is_approved == Payment::PENDING)
                        ->requiresConfirmation()
                        ->action(function ($record, $livewire) {
                            $livewire->changeStatus(Payment::REJECTED, $record);
                        }),
                    Actions\Action::make('mark_pending')
                        ->label(__('messages.common.mark_pending'))
                        ->icon('heroicon-o-clock')
                        ->color('warning')
                        ->visible(fn($record) => $record->is_approved != Payment::PENDING)
                        ->requiresConfirmation()
                        ->action(function ($record, $livewire) {
                            $livewire->changeStatus(Payment::PENDING, $record);
                        }),
                ])
                    ->label(__('messages.common.actions'))
                    ->icon('heroicon-o-ellipsis-vertical')
                    ->size('sm')
                    ->color('gray')
                    ->button()
            ])
            ->actionsColumnLabel(__('messages.common.actions'))
            ->paginated([10, 25, 50]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListTransactions::route('/'),
            'edit' => EditTransaction::route('/{record}/edit'),
        ];
    }
}
