<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SystemNotificationResource\Pages;
use App\Models\SystemNotification;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;
use Carbon\Carbon;

class SystemNotificationResource extends Resource
{
    protected static ?string $model = SystemNotification::class;
    protected static ?string $navigationIcon = 'heroicon-o-bell';
    protected static ?string $navigationLabel = 'Notifications';
    protected static ?string $navigationGroup = 'System';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                
                Forms\Components\Textarea::make('message')
                    ->required()
                    ->rows(4),
                
                Forms\Components\Select::make('type')
                    ->options([
                        'info' => 'Info',
                        'success' => 'Success',
                        'warning' => 'Warning',
                        'error' => 'Error',
                    ])
                    ->default('info')
                    ->required(),
                
                Forms\Components\Select::make('priority')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                    ])
                    ->default('medium')
                    ->required(),
                
                Forms\Components\TextInput::make('action_url')
                    ->label('Action URL')
                    ->url(),
                
                Forms\Components\TextInput::make('action_text')
                    ->label('Action Text'),
                
                Forms\Components\DateTimePicker::make('expires_at')
                    ->label('Expires At'),
                
                Forms\Components\KeyValue::make('data')
                    ->label('Additional Data'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('title')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 30 ? $state : null;
                    }),
                
                TextColumn::make('message')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                
                BadgeColumn::make('type')
                    ->colors([
                        'info' => 'info',
                        'success' => 'success',
                        'warning' => 'warning',
                        'error' => 'danger',
                    ]),
                
                BadgeColumn::make('priority')
                    ->colors([
                        'low' => 'success',
                        'medium' => 'warning',
                        'high' => 'danger',
                    ]),
                
                IconColumn::make('read_at')
                    ->label('Read')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->since(),
                
                TextColumn::make('expires_at')
                    ->label('Expires')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'info' => 'Info',
                        'success' => 'Success',
                        'warning' => 'Warning',
                        'error' => 'Error',
                    ]),
                
                SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                    ]),
                
                SelectFilter::make('read_status')
                    ->label('Read Status')
                    ->options([
                        'read' => 'Read',
                        'unread' => 'Unread',
                    ])
                    ->query(function ($query, $data) {
                        if ($data['value'] === 'read') {
                            return $query->whereNotNull('read_at');
                        } elseif ($data['value'] === 'unread') {
                            return $query->whereNull('read_at');
                        }
                        return $query;
                    }),
                
                SelectFilter::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                Action::make('mark_as_read')
                    ->label('Mark as Read')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn ($record) => !$record->isRead())
                    ->action(function ($record): void {
                        $record->markAsRead();
                        Notification::make()
                            ->title('Notification marked as read')
                            ->success()
                            ->send();
                    }),
                
                Action::make('send_to_all')
                    ->label('Send to All Users')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->requiresConfirmation()
                    ->modalHeading('Send Notification to All Users')
                    ->modalDescription('This will create a copy of this notification for all users.')
                    ->action(function ($record): void {
                        $count = SystemNotification::createForAllUsers(
                            $record->title,
                            $record->message,
                            $record->type,
                            $record->priority,
                            $record->action_url,
                            $record->action_text,
                            $record->data ?? [],
                            $record->expires_at
                        );
                        
                        Notification::make()
                            ->title("Notification sent to {$count} users")
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('mark_as_read')
                        ->label('Mark as Read')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records): void {
                            foreach ($records as $record) {
                                $record->markAsRead();
                            }
                            
                            Notification::make()
                                ->title('Selected notifications marked as read')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSystemNotifications::route('/'),
            'create' => Pages\CreateSystemNotification::route('/create'),
            'view' => Pages\ViewSystemNotification::route('/{record}'),
            'edit' => Pages\EditSystemNotification::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereNull('read_at')
            ->where('created_at', '>=', Carbon::today())
            ->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        $highPriorityCount = static::getModel()::whereNull('read_at')
            ->where('priority', 'high')
            ->where('created_at', '>=', Carbon::today())
            ->count();
            
        return $highPriorityCount > 0 ? 'danger' : 'primary';
    }
}
