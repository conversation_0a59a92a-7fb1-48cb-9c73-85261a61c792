<?php
/**
 * Authentication System Monitor
 * 
 * Real-time monitoring tool for authentication system health,
 * session management, and 419 error detection.
 * 
 * Usage: php monitor_auth_system.php
 */

echo "=== AUTHENTICATION SYSTEM MONITOR ===\n\n";

// Bootstrap Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

function displayHeader($title) {
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "  " . strtoupper($title) . "\n";
    echo str_repeat("=", 60) . "\n";
}

function displaySection($title) {
    echo "\n" . str_repeat("-", 40) . "\n";
    echo $title . "\n";
    echo str_repeat("-", 40) . "\n";
}

displayHeader("Real-time Authentication Monitor");

echo "🕐 Monitor Started: " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
echo "🔄 Refresh Rate: Every 5 seconds (Ctrl+C to stop)\n";

// Monitor loop
$iteration = 0;
while (true) {
    $iteration++;
    
    // Clear screen (works on most terminals)
    if ($iteration > 1) {
        echo "\033[2J\033[H"; // Clear screen and move cursor to top
    }
    
    displayHeader("Authentication System Status - Iteration #{$iteration}");
    echo "🕐 Current Time: " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
    
    try {
        // 1. Session Statistics
        displaySection("📊 Session Statistics");
        
        $totalSessions = DB::table('sessions')->count();
        $activeSessions = DB::table('sessions')
            ->where('last_activity', '>', time() - (config('session.lifetime') * 60))
            ->count();
        $expiredSessions = $totalSessions - $activeSessions;
        
        echo sprintf("   Total Sessions: %d\n", $totalSessions);
        echo sprintf("   Active Sessions: %d\n", $activeSessions);
        echo sprintf("   Expired Sessions: %d\n", $expiredSessions);
        
        // Recent session activity
        $recentSessions = DB::table('sessions')
            ->select('user_id', 'ip_address', 'last_activity')
            ->orderBy('last_activity', 'desc')
            ->limit(5)
            ->get();
        
        echo "\n   Recent Session Activity:\n";
        foreach ($recentSessions as $session) {
            $lastActivity = Carbon::createFromTimestamp($session->last_activity);
            $userId = $session->user_id ?: 'Guest';
            echo sprintf("   - User: %s | IP: %s | Last: %s\n", 
                $userId, 
                $session->ip_address, 
                $lastActivity->diffForHumans()
            );
        }
        
        // 2. Authentication Attempts
        displaySection("🔐 Authentication Activity");
        
        // Check for recent login attempts (from logs if available)
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logContent = file_get_contents($logFile);
            $lines = explode("\n", $logContent);
            $recentLines = array_slice($lines, -50); // Last 50 lines
            
            $loginAttempts = 0;
            $failedAttempts = 0;
            $csrfErrors = 0;
            
            foreach ($recentLines as $line) {
                if (strpos($line, 'login') !== false) {
                    $loginAttempts++;
                }
                if (strpos($line, 'failed') !== false || strpos($line, 'Failed') !== false) {
                    $failedAttempts++;
                }
                if (strpos($line, '419') !== false || strpos($line, 'CSRF') !== false) {
                    $csrfErrors++;
                }
            }
            
            echo sprintf("   Recent Login Attempts: %d\n", $loginAttempts);
            echo sprintf("   Failed Attempts: %d\n", $failedAttempts);
            echo sprintf("   CSRF Errors (419): %d\n", $csrfErrors);
            
            if ($csrfErrors > 0) {
                echo "   ⚠️  WARNING: CSRF errors detected!\n";
            }
        } else {
            echo "   Log file not found - unable to track authentication attempts\n";
        }
        
        // 3. System Health
        displaySection("🏥 System Health");
        
        // Database connectivity
        try {
            $dbTime = DB::selectOne('SELECT NOW() as current_time');
            echo "   ✅ Database: Connected\n";
            echo sprintf("   📅 DB Time: %s\n", $dbTime->current_time);
        } catch (Exception $e) {
            echo "   ❌ Database: Connection Failed\n";
            echo sprintf("   Error: %s\n", $e->getMessage());
        }
        
        // Session table health
        try {
            $sessionTableSize = DB::select("
                SELECT 
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'sessions'
            ");
            
            if (!empty($sessionTableSize)) {
                echo sprintf("   📊 Sessions Table Size: %.2f MB\n", $sessionTableSize[0]->size_mb);
            }
        } catch (Exception $e) {
            echo "   ⚠️  Could not determine sessions table size\n";
        }
        
        // Configuration status
        $sessionDriver = config('session.driver');
        $sessionDomain = config('session.domain');
        $appEnv = config('app.env');
        
        echo sprintf("   ⚙️  Session Driver: %s\n", $sessionDriver);
        echo sprintf("   🌐 Session Domain: %s\n", $sessionDomain ?: 'null (flexible)');
        echo sprintf("   🌍 Environment: %s\n", $appEnv);
        
        // 4. Performance Metrics
        displaySection("⚡ Performance Metrics");
        
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
        
        echo sprintf("   💾 Memory Usage: %.2f MB\n", $memoryUsage);
        echo sprintf("   📈 Peak Memory: %.2f MB\n", $peakMemory);
        
        // Session cleanup recommendation
        if ($expiredSessions > 100) {
            echo "\n   🧹 RECOMMENDATION: Clean up expired sessions\n";
            echo "   Run: php artisan session:gc\n";
        }
        
        // 5. Quick Health Check
        displaySection("🎯 Quick Health Check");
        
        $healthScore = 0;
        $maxScore = 5;
        
        // Check 1: Database connection
        if (DB::connection()->getPdo()) {
            echo "   ✅ Database Connection\n";
            $healthScore++;
        } else {
            echo "   ❌ Database Connection\n";
        }
        
        // Check 2: Sessions table exists
        if (DB::getSchemaBuilder()->hasTable('sessions')) {
            echo "   ✅ Sessions Table\n";
            $healthScore++;
        } else {
            echo "   ❌ Sessions Table\n";
        }
        
        // Check 3: Active sessions exist
        if ($activeSessions > 0) {
            echo "   ✅ Active Sessions\n";
            $healthScore++;
        } else {
            echo "   ⚠️  No Active Sessions\n";
        }
        
        // Check 4: Configuration is production-ready
        if ($sessionDriver === 'database') {
            echo "   ✅ Session Configuration\n";
            $healthScore++;
        } else {
            echo "   ⚠️  Session Configuration (not database)\n";
        }
        
        // Check 5: No recent CSRF errors
        if (!isset($csrfErrors) || $csrfErrors === 0) {
            echo "   ✅ No CSRF Errors\n";
            $healthScore++;
        } else {
            echo "   ❌ CSRF Errors Detected\n";
        }
        
        $healthPercentage = ($healthScore / $maxScore) * 100;
        echo sprintf("\n   🏥 Overall Health: %d/%d (%.0f%%)\n", $healthScore, $maxScore, $healthPercentage);
        
        if ($healthPercentage >= 80) {
            echo "   🎉 System Status: HEALTHY\n";
        } elseif ($healthPercentage >= 60) {
            echo "   ⚠️  System Status: WARNING\n";
        } else {
            echo "   🚨 System Status: CRITICAL\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Monitor Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "Press Ctrl+C to stop monitoring...\n";
    
    // Wait 5 seconds before next iteration
    sleep(5);
}
