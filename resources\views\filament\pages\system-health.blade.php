<x-filament-panels::page>
    <div class="space-y-6">
        @if($healthData)
            <!-- Overall Status Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="p-3 rounded-full {{ $this->getOverallStatusColor() === 'success' ? 'bg-green-100 text-green-600' : ($this->getOverallStatusColor() === 'warning' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600') }}">
                            <x-heroicon-o-heart class="w-8 h-8" />
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">System Health</h2>
                            <p class="text-lg {{ $this->getOverallStatusColor() === 'success' ? 'text-green-600' : ($this->getOverallStatusColor() === 'warning' ? 'text-yellow-600' : 'text-red-600') }}">
                                {{ ucfirst($healthData['overall_status']) }}
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Last checked</p>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ now()->format('M d, Y H:i:s') }}</p>
                    </div>
                </div>
            </div>

            <!-- Health Checks Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($healthData['checks'] as $checkName => $check)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                                {{ str_replace('_', ' ', $checkName) }}
                            </h3>
                            <div class="p-2 rounded-full {{ $this->getCheckStatusColor($check['status']) === 'success' ? 'bg-green-100 text-green-600' : ($this->getCheckStatusColor($check['status']) === 'warning' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600') }}">
                                <x-heroicon-o-check-circle class="w-5 h-5" />
                            </div>
                        </div>
                        
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ $check['message'] }}</p>
                        
                        @if(isset($check['details']) && !empty($check['details']))
                            <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                                <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Details:</p>
                                @foreach($check['details'] as $key => $value)
                                    <div class="text-xs text-gray-600 dark:text-gray-400">
                                        <span class="font-medium">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                        @if(is_array($value))
                                            {{ implode(', ', $value) }}
                                        @else
                                            {{ $value }}
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Errors and Warnings -->
            @if(!empty($healthData['errors']) || !empty($healthData['warnings']))
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    @if(!empty($healthData['errors']))
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-4">
                                <x-heroicon-o-x-circle class="w-5 h-5 inline mr-2" />
                                Critical Errors
                            </h3>
                            <ul class="space-y-2">
                                @foreach($healthData['errors'] as $error)
                                    <li class="text-sm text-red-700 dark:text-red-300">• {{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if(!empty($healthData['warnings']))
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-4">
                                <x-heroicon-o-exclamation-triangle class="w-5 h-5 inline mr-2" />
                                Warnings
                            </h3>
                            <ul class="space-y-2">
                                @foreach($healthData['warnings'] as $warning)
                                    <li class="text-sm text-yellow-700 dark:text-yellow-300">• {{ $warning }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Quick System Stats -->
            @if($systemInfo)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick System Stats</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <p class="text-2xl font-bold text-blue-600">{{ $systemInfo['php_version'] }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">PHP Version</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-green-600">{{ $systemInfo['laravel_version'] }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Laravel Version</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-purple-600">{{ $systemInfo['database_driver'] }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Database Driver</p>
                        </div>
                        <div class="text-center">
                            <p class="text-2xl font-bold text-orange-600">{{ $systemInfo['memory_limit'] }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Memory Limit</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Recommendations -->
            @if(!empty($healthData['recommendations']))
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">
                        <x-heroicon-o-light-bulb class="w-5 h-5 inline mr-2" />
                        Recommendations
                    </h3>
                    <ul class="space-y-2">
                        @foreach($healthData['recommendations'] as $recommendation)
                            <li class="text-sm text-blue-700 dark:text-blue-300">• {{ $recommendation }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
        @else
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
                <x-heroicon-o-exclamation-triangle class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-600 dark:text-gray-400">Unable to load system health data</p>
            </div>
        @endif
    </div>
</x-filament-panels::page>
