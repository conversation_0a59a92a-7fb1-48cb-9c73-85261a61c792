<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Client;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class AdvancedReportingService
{
    /**
     * Get comprehensive financial overview
     */
    public function getFinancialOverview(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? Carbon::now()->startOfMonth();
        $endDate = $filters['end_date'] ?? Carbon::now()->endOfMonth();
        
        return [
            'revenue' => $this->getRevenueMetrics($startDate, $endDate),
            'invoices' => $this->getInvoiceMetrics($startDate, $endDate),
            'clients' => $this->getClientMetrics($startDate, $endDate),
            'payments' => $this->getPaymentMetrics($startDate, $endDate),
            'trends' => $this->getTrendAnalysis($startDate, $endDate),
        ];
    }

    /**
     * Get revenue metrics and analytics
     */
    public function getRevenueMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalRevenue = Invoice::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'paid')
            ->sum('total');

        $previousPeriod = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        $previousRevenue = Invoice::whereBetween('created_at', [$previousPeriod, $startDate])
            ->where('status', 'paid')
            ->sum('total');

        $growthRate = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        // Monthly revenue breakdown
        $monthlyRevenue = Invoice::selectRaw('
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                SUM(total) as revenue,
                COUNT(*) as invoice_count
            ')
            ->whereBetween('created_at', [$startDate->copy()->subMonths(11), $endDate])
            ->where('status', 'paid')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                return [
                    'period' => Carbon::create($item->year, $item->month)->format('M Y'),
                    'revenue' => (float) $item->revenue,
                    'invoice_count' => $item->invoice_count,
                ];
            });

        // Revenue by client
        $revenueByClient = Invoice::select('clients.name', DB::raw('SUM(invoices.total) as total_revenue'))
            ->join('clients', 'invoices.client_id', '=', 'clients.id')
            ->whereBetween('invoices.created_at', [$startDate, $endDate])
            ->where('invoices.status', 'paid')
            ->groupBy('clients.id', 'clients.name')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get();

        return [
            'total_revenue' => (float) $totalRevenue,
            'previous_revenue' => (float) $previousRevenue,
            'growth_rate' => round($growthRate, 2),
            'monthly_breakdown' => $monthlyRevenue,
            'top_clients' => $revenueByClient,
            'average_invoice_value' => $this->getAverageInvoiceValue($startDate, $endDate),
        ];
    }

    /**
     * Get invoice metrics and analytics
     */
    public function getInvoiceMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $invoices = Invoice::whereBetween('created_at', [$startDate, $endDate]);
        
        $totalInvoices = $invoices->count();
        $paidInvoices = $invoices->where('status', 'paid')->count();
        $pendingInvoices = $invoices->where('status', 'sent')->count();
        $overdueInvoices = $invoices->where('status', 'overdue')->count();

        // Status distribution
        $statusDistribution = Invoice::selectRaw('status, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Average payment time
        $averagePaymentTime = Payment::join('invoices', 'payments.invoice_id', '=', 'invoices.id')
            ->whereBetween('payments.created_at', [$startDate, $endDate])
            ->selectRaw('AVG(DATEDIFF(payments.created_at, invoices.created_at)) as avg_days')
            ->value('avg_days');

        // Invoice aging analysis
        $agingAnalysis = $this->getInvoiceAgingAnalysis();

        return [
            'total_invoices' => $totalInvoices,
            'paid_invoices' => $paidInvoices,
            'pending_invoices' => $pendingInvoices,
            'overdue_invoices' => $overdueInvoices,
            'payment_rate' => $totalInvoices > 0 ? round(($paidInvoices / $totalInvoices) * 100, 2) : 0,
            'status_distribution' => $statusDistribution,
            'average_payment_time' => round($averagePaymentTime ?? 0, 1),
            'aging_analysis' => $agingAnalysis,
        ];
    }

    /**
     * Get client metrics and analytics
     */
    public function getClientMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalClients = Client::count();
        $activeClients = Client::whereHas('invoices', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();

        $newClients = Client::whereBetween('created_at', [$startDate, $endDate])->count();

        // Client lifetime value
        $clientLifetimeValue = Client::select('clients.*')
            ->selectRaw('SUM(invoices.total) as lifetime_value')
            ->selectRaw('COUNT(invoices.id) as total_invoices')
            ->leftJoin('invoices', 'clients.id', '=', 'invoices.client_id')
            ->where('invoices.status', 'paid')
            ->groupBy('clients.id')
            ->orderByDesc('lifetime_value')
            ->limit(10)
            ->get();

        // Client acquisition trend
        $acquisitionTrend = Client::selectRaw('
                DATE_FORMAT(created_at, "%Y-%m") as month,
                COUNT(*) as new_clients
            ')
            ->whereBetween('created_at', [$startDate->copy()->subMonths(11), $endDate])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return [
            'total_clients' => $totalClients,
            'active_clients' => $activeClients,
            'new_clients' => $newClients,
            'client_retention_rate' => $this->getClientRetentionRate($startDate, $endDate),
            'lifetime_value_ranking' => $clientLifetimeValue,
            'acquisition_trend' => $acquisitionTrend,
        ];
    }

    /**
     * Get payment metrics and analytics
     */
    public function getPaymentMetrics(Carbon $startDate, Carbon $endDate): array
    {
        $totalPayments = Payment::whereBetween('created_at', [$startDate, $endDate])->sum('amount');
        $paymentCount = Payment::whereBetween('created_at', [$startDate, $endDate])->count();

        // Payment methods distribution
        $paymentMethods = Payment::selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('payment_method')
            ->get();

        // Payment trends
        $paymentTrends = Payment::selectRaw('
                DATE(created_at) as date,
                SUM(amount) as daily_amount,
                COUNT(*) as daily_count
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_payments' => (float) $totalPayments,
            'payment_count' => $paymentCount,
            'average_payment_amount' => $paymentCount > 0 ? round($totalPayments / $paymentCount, 2) : 0,
            'payment_methods' => $paymentMethods,
            'daily_trends' => $paymentTrends,
        ];
    }

    /**
     * Get trend analysis
     */
    public function getTrendAnalysis(Carbon $startDate, Carbon $endDate): array
    {
        // Revenue trend
        $revenueTrend = $this->getRevenueTrend($startDate, $endDate);
        
        // Invoice volume trend
        $invoiceTrend = $this->getInvoiceTrend($startDate, $endDate);
        
        // Client growth trend
        $clientTrend = $this->getClientGrowthTrend($startDate, $endDate);

        return [
            'revenue_trend' => $revenueTrend,
            'invoice_trend' => $invoiceTrend,
            'client_trend' => $clientTrend,
            'seasonal_patterns' => $this->getSeasonalPatterns(),
        ];
    }

    /**
     * Get invoice aging analysis
     */
    private function getInvoiceAgingAnalysis(): array
    {
        $today = Carbon::now();
        
        return [
            'current' => Invoice::where('status', 'sent')
                ->where('due_date', '>=', $today)
                ->count(),
            '1_30_days' => Invoice::where('status', 'sent')
                ->whereBetween('due_date', [$today->copy()->subDays(30), $today->copy()->subDay()])
                ->count(),
            '31_60_days' => Invoice::where('status', 'sent')
                ->whereBetween('due_date', [$today->copy()->subDays(60), $today->copy()->subDays(31)])
                ->count(),
            '61_90_days' => Invoice::where('status', 'sent')
                ->whereBetween('due_date', [$today->copy()->subDays(90), $today->copy()->subDays(61)])
                ->count(),
            'over_90_days' => Invoice::where('status', 'sent')
                ->where('due_date', '<', $today->copy()->subDays(90))
                ->count(),
        ];
    }

    /**
     * Get average invoice value
     */
    private function getAverageInvoiceValue(Carbon $startDate, Carbon $endDate): float
    {
        return (float) Invoice::whereBetween('created_at', [$startDate, $endDate])
            ->avg('total') ?? 0;
    }

    /**
     * Get client retention rate
     */
    private function getClientRetentionRate(Carbon $startDate, Carbon $endDate): float
    {
        $previousPeriodStart = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        
        $previousClients = Client::whereHas('invoices', function ($query) use ($previousPeriodStart, $startDate) {
            $query->whereBetween('created_at', [$previousPeriodStart, $startDate]);
        })->pluck('id');

        $retainedClients = Client::whereIn('id', $previousClients)
            ->whereHas('invoices', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })->count();

        return $previousClients->count() > 0 ? 
            round(($retainedClients / $previousClients->count()) * 100, 2) : 0;
    }

    /**
     * Get revenue trend data
     */
    private function getRevenueTrend(Carbon $startDate, Carbon $endDate): Collection
    {
        return Invoice::selectRaw('
                DATE(created_at) as date,
                SUM(total) as revenue
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'paid')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get invoice trend data
     */
    private function getInvoiceTrend(Carbon $startDate, Carbon $endDate): Collection
    {
        return Invoice::selectRaw('
                DATE(created_at) as date,
                COUNT(*) as count
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get client growth trend
     */
    private function getClientGrowthTrend(Carbon $startDate, Carbon $endDate): Collection
    {
        return Client::selectRaw('
                DATE(created_at) as date,
                COUNT(*) as new_clients
            ')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get seasonal patterns
     */
    private function getSeasonalPatterns(): array
    {
        $monthlyData = Invoice::selectRaw('
                MONTH(created_at) as month,
                AVG(total) as avg_revenue,
                COUNT(*) as invoice_count
            ')
            ->where('status', 'paid')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return $monthlyData->map(function ($item) {
            return [
                'month' => Carbon::create(null, $item->month)->format('F'),
                'avg_revenue' => round($item->avg_revenue, 2),
                'invoice_count' => $item->invoice_count,
            ];
        })->toArray();
    }

    /**
     * Export report data to various formats
     */
    public function exportReport(string $type, array $data, string $format = 'pdf'): string
    {
        switch ($format) {
            case 'pdf':
                return $this->exportToPdf($type, $data);
            case 'excel':
                return $this->exportToExcel($type, $data);
            case 'csv':
                return $this->exportToCsv($type, $data);
            default:
                throw new \InvalidArgumentException("Unsupported export format: {$format}");
        }
    }

    /**
     * Export to PDF
     */
    private function exportToPdf(string $type, array $data): string
    {
        // Implementation for PDF export
        // This would use a PDF library like DomPDF or similar
        return "reports/{$type}_" . date('Y-m-d_H-i-s') . ".pdf";
    }

    /**
     * Export to Excel
     */
    private function exportToExcel(string $type, array $data): string
    {
        // Implementation for Excel export
        // This would use a library like PhpSpreadsheet
        return "reports/{$type}_" . date('Y-m-d_H-i-s') . ".xlsx";
    }

    /**
     * Export to CSV
     */
    private function exportToCsv(string $type, array $data): string
    {
        // Implementation for CSV export
        return "reports/{$type}_" . date('Y-m-d_H-i-s') . ".csv";
    }
}
