<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice Analytics Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 20px;
            color: #666;
            margin-bottom: 10px;
        }
        .date-range {
            font-size: 14px;
            color: #888;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            text-align: center;
        }
        .status-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 12px;
        }
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        .table th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        .table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .overdue-high {
            background-color: #ffebee !important;
            color: #c62828;
        }
        .overdue-medium {
            background-color: #fff3e0 !important;
            color: #ef6c00;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $company_name ?? 'DCF - Digital Clearing and Forwarding Agency' }}</div>
        <div class="report-title">{{ $report_title ?? 'Invoice Analytics Report' }}</div>
        <div class="date-range">Period: {{ $start_date }} to {{ $end_date }}</div>
    </div>

    @if(isset($status_distribution) && count($status_distribution) > 0)
    <div class="section">
        <div class="section-title">Invoice Status Distribution</div>
        <div class="status-grid">
            @foreach($status_distribution as $status)
            <div class="status-card">
                <div class="status-label">{{ ucfirst(str_replace('_', ' ', $status['status'])) }}</div>
                <div class="status-value">{{ $status['count'] }} ({{ number_format($status['percentage'], 1) }}%)</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                    ${{ number_format($status['total_amount'], 2) }}
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    @if(isset($overdue_analysis) && count($overdue_analysis) > 0)
    <div class="section">
        <div class="section-title">Overdue Invoices Analysis</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Invoice ID</th>
                    <th>Client</th>
                    <th>Due Date</th>
                    <th>Amount</th>
                    <th>Outstanding</th>
                    <th>Days Overdue</th>
                    <th>Severity</th>
                </tr>
            </thead>
            <tbody>
                @foreach($overdue_analysis as $invoice)
                <tr class="{{ $invoice['days_overdue'] > 60 ? 'overdue-high' : ($invoice['days_overdue'] > 30 ? 'overdue-medium' : '') }}">
                    <td>{{ $invoice['invoice_id'] }}</td>
                    <td>{{ $invoice['client_name'] }}</td>
                    <td>{{ $invoice['due_date'] }}</td>
                    <td>${{ number_format($invoice['total_amount'], 2) }}</td>
                    <td>${{ number_format($invoice['outstanding_amount'], 2) }}</td>
                    <td>{{ $invoice['days_overdue'] }}</td>
                    <td>{{ ucfirst($invoice['overdue_severity'] ?? 'low') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($payment_trends) && count($payment_trends) > 0)
    <div class="section">
        <div class="section-title">Payment Trends</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Period</th>
                    <th>Total Revenue</th>
                    <th>Payment Count</th>
                    <th>Average Payment</th>
                </tr>
            </thead>
            <tbody>
                @foreach($payment_trends as $trend)
                <tr>
                    <td>{{ $trend['period'] }}</td>
                    <td>${{ number_format($trend['total_revenue'], 2) }}</td>
                    <td>{{ $trend['payment_count'] }}</td>
                    <td>${{ number_format($trend['avg_payment_amount'], 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p>Generated on {{ $generated_at }}</p>
        <p>This report is confidential and intended for internal use only.</p>
    </div>
</body>
</html>
