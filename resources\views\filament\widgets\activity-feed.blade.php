<x-filament-widgets::widget>
    <x-filament::section>
        <!-- Modern Activity Feed Container -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Enhanced Activity Feed -->
            <div class="lg:col-span-2">
                <!-- Modern Header with Gradient -->
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-3">
                        <div class="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                            <x-heroicon-o-clock class="h-6 w-6 text-white" />
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white">Recent Activity</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Latest system updates and events</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 dark:from-green-900/30 dark:to-emerald-900/30 dark:text-green-300 shadow-sm">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse shadow-sm"></span>
                            Live Updates
                        </span>
                        <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800">
                            <x-heroicon-o-arrow-path class="h-4 w-4" />
                        </button>
                    </div>
                </div>

                <!-- Modern Activity Timeline -->
                <div class="space-y-4">
                    @foreach($this->getActivities() as $index => $activity)
                        <div class="group relative">
                            <!-- Modern Activity Card -->
                            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 hover:shadow-md hover:border-gray-200 dark:hover:border-gray-600 transition-all duration-300 hover:-translate-y-0.5">
                                <div class="flex items-start space-x-4">
                                    <!-- Enhanced Icon with Gradient Background -->
                                    <div class="flex-shrink-0">
                                        <div class="relative">
                                            <div class="h-12 w-12 rounded-2xl flex items-center justify-center shadow-lg
                                                {{ $activity['color'] === 'success' ? 'bg-gradient-to-br from-green-400 to-green-600' :
                                                   ($activity['color'] === 'info' ? 'bg-gradient-to-br from-blue-400 to-blue-600' :
                                                   ($activity['color'] === 'danger' ? 'bg-gradient-to-br from-red-400 to-red-600' :
                                                   ($activity['color'] === 'warning' ? 'bg-gradient-to-br from-yellow-400 to-yellow-600' : 'bg-gradient-to-br from-gray-400 to-gray-600'))) }}">
                                                <x-dynamic-component :component="$activity['icon']" class="h-6 w-6 text-white" />
                                            </div>
                                            <!-- Activity Type Badge -->
                                            <div class="absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-white dark:border-gray-800
                                                {{ $activity['type'] === 'invoice' ? 'bg-blue-500' :
                                                   ($activity['type'] === 'payment' ? 'bg-green-500' :
                                                   ($activity['type'] === 'client' ? 'bg-purple-500' : 'bg-gray-500')) }}">
                                            </div>
                                        </div>
                                    </div>
                                        
                                    <!-- Enhanced Content Area -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <!-- Activity Title with Modern Typography -->
                                                <h4 class="text-base font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                                                    <a href="{{ $activity['url'] ?? '#' }}" class="hover:underline decoration-2 underline-offset-2">
                                                        {{ $activity['title'] }}
                                                    </a>
                                                </h4>

                                                <!-- Activity Description with Better Spacing -->
                                                <p class="mt-2 text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                                                    {{ $activity['description'] }}
                                                </p>

                                                <!-- Enhanced Timestamp with Icon -->
                                                <div class="mt-3 flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                                                    <x-heroicon-o-clock class="h-3 w-3" />
                                                    <time datetime="{{ $activity['time']->toISOString() }}" class="font-medium">
                                                        {{ $activity['time']->diffForHumans() }}
                                                    </time>
                                                    <!-- Activity Type Label -->
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium
                                                        {{ $activity['type'] === 'invoice' ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300' :
                                                           ($activity['type'] === 'payment' ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300' :
                                                           ($activity['type'] === 'client' ? 'bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300' : 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300')) }}">
                                                        {{ ucfirst($activity['type']) }}
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Action Button -->
                                            <div class="flex-shrink-0 ml-4">
                                                <a href="{{ $activity['url'] ?? '#' }}" class="inline-flex items-center p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-all duration-200">
                                                    <x-heroicon-o-arrow-top-right-on-square class="h-4 w-4" />
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Modern View All Button -->
                <div class="mt-8 text-center">
                    <a href="#" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-sm font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <x-heroicon-o-eye class="h-4 w-4 mr-2" />
                        View All Activity
                        <x-heroicon-o-arrow-right class="h-4 w-4 ml-2" />
                    </a>
                </div>
            </div>

            <!-- Enhanced Quick Stats Sidebar -->
            <div class="space-y-8">
                <div>
                    <!-- Modern Sidebar Header -->
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                            <x-heroicon-o-chart-bar class="h-5 w-5 text-white" />
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">Today's Overview</h3>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Real-time metrics</p>
                        </div>
                    </div>

                    @php $stats = $this->getQuickStats(); @endphp

                    <div class="space-y-4">
                        <!-- Modern Stat Card 1 -->
                        <div class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-800/20 rounded-2xl p-5 border border-blue-100 dark:border-blue-800/30 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-sm">
                                        <x-heroicon-o-document-text class="h-5 w-5 text-white" />
                                    </div>
                                    <div>
                                        <p class="text-xs font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">Invoices</p>
                                        <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">{{ $stats['today_invoices'] }}</p>
                                    </div>
                                </div>
                                <div class="text-xs text-blue-600 dark:text-blue-400 font-medium">Today</div>
                            </div>
                        </div>

                        <!-- Modern Stat Card 2 -->
                        <div class="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 rounded-2xl p-5 border border-green-100 dark:border-green-800/30 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-sm">
                                        <x-heroicon-o-banknotes class="h-5 w-5 text-white" />
                                    </div>
                                    <div>
                                        <p class="text-xs font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">Payments</p>
                                        <p class="text-2xl font-bold text-green-900 dark:text-green-100">{{ $stats['today_payments'] }}</p>
                                    </div>
                                </div>
                                <div class="text-xs text-green-600 dark:text-green-400 font-medium">Received</div>
                            </div>
                        </div>

                        <!-- Modern Stat Card 3 -->
                        <div class="bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-900/20 dark:to-violet-800/20 rounded-2xl p-5 border border-purple-100 dark:border-purple-800/30 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="p-2 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl shadow-sm">
                                        <x-heroicon-o-currency-dollar class="h-5 w-5 text-white" />
                                    </div>
                                    <div>
                                        <p class="text-xs font-semibold text-purple-700 dark:text-purple-300 uppercase tracking-wide">Revenue</p>
                                        <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">${{ number_format($stats['today_revenue'], 2) }}</p>
                                    </div>
                                </div>
                                <div class="text-xs text-purple-600 dark:text-purple-400 font-medium">Today</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alerts Section -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Alerts</h3>
                    
                    <div class="space-y-3">
                        @if($stats['overdue_invoices'] > 0)
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-exclamation-triangle class="h-5 w-5 text-red-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                            Overdue Invoices
                                        </h3>
                                        <div class="mt-1 text-sm text-red-700 dark:text-red-300">
                                            {{ $stats['overdue_invoices'] }} invoices are overdue
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($stats['pending_invoices'] > 5)
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-clock class="h-5 w-5 text-yellow-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                            Pending Invoices
                                        </h3>
                                        <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                                            {{ $stats['pending_invoices'] }} invoices awaiting payment
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($stats['overdue_invoices'] == 0 && $stats['pending_invoices'] <= 5)
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <x-heroicon-o-check-circle class="h-5 w-5 text-green-400" />
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                            All Good!
                                        </h3>
                                        <div class="mt-1 text-sm text-green-700 dark:text-green-300">
                                            No urgent issues to address
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Actions -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                    
                    <div class="space-y-2">
                        <a href="{{ route('filament.admin.resources.invoices.create') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-plus class="h-4 w-4 mr-2" />
                            Create Invoice
                        </a>
                        
                        <a href="{{ route('filament.admin.resources.clients.create') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-user-plus class="h-4 w-4 mr-2" />
                            Add Client
                        </a>
                        
                        <a href="{{ route('filament.admin.pages.advanced-reporting') }}" 
                           class="w-full flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <x-heroicon-o-chart-bar class="h-4 w-4 mr-2" />
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
