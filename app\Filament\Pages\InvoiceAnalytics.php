<?php

namespace App\Filament\Pages;

use App\Services\ReportingService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class InvoiceAnalytics extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static string $view = 'filament.pages.invoice-analytics';
    protected static ?string $navigationGroup = 'Reports';
    protected static ?int $navigationSort = 2;

    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $analyticsType = 'status_distribution';

    protected ReportingService $reportingService;

    public function boot(ReportingService $reportingService): void
    {
        $this->reportingService = $reportingService;
    }

    public function mount(): void
    {
        $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
    }

    public static function getNavigationLabel(): string
    {
        return __('Invoice Analytics');
    }

    public function getTitle(): string | Htmlable
    {
        return __('Invoice Analytics & Tracking');
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(3)->schema([
                    DatePicker::make('startDate')
                        ->label('Start Date')
                        ->required()
                        ->default(Carbon::now()->startOfMonth())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'before_or_equal:endDate'])
                        ->validationAttribute('Start Date'),
                    DatePicker::make('endDate')
                        ->label('End Date')
                        ->required()
                        ->default(Carbon::now())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'after_or_equal:startDate'])
                        ->validationAttribute('End Date'),
                    Select::make('analyticsType')
                        ->label('Analytics Type')
                        ->options([
                            'status_distribution' => 'Status Distribution',
                            'overdue_analysis' => 'Overdue Analysis',
                            'payment_trends' => 'Payment Trends',
                            'invoice_aging' => 'Invoice Aging',
                        ])
                        ->default('status_distribution')
                        ->required()
                        ->rules(['required', 'in:status_distribution,overdue_analysis,payment_trends,invoice_aging'])
                        ->validationAttribute('Analytics Type'),
                ]),
            ])
            ->statePath('data');
    }

    protected function getValidationRules(): array
    {
        return [
            'data.startDate' => ['required', 'date', 'before_or_equal:data.endDate'],
            'data.endDate' => ['required', 'date', 'after_or_equal:data.startDate'],
            'data.analyticsType' => ['required', 'in:status_distribution,overdue_analysis,payment_trends,invoice_aging'],
        ];
    }

    protected function getValidationAttributes(): array
    {
        return [
            'data.startDate' => 'Start Date',
            'data.endDate' => 'End Date',
            'data.analyticsType' => 'Analytics Type',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateAnalytics')
                ->label('Generate Analytics')
                ->icon('heroicon-o-chart-pie')
                ->color('primary')
                ->action('generateAnalytics'),
            Action::make('exportReport')
                ->label('Export Report')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action('exportReport'),
        ];
    }

    public function generateAnalytics(): void
    {
        try {
            $this->validate();

            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);
            $analyticsType = $this->data['analyticsType'];

            $this->dispatch('analytics-generated', [
                'analyticsType' => $analyticsType,
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ]);
        } catch (\Exception $e) {
            \Log::error('Invoice Analytics generation error: ' . $e->getMessage());
            $this->addError('general', 'Failed to generate analytics: ' . $e->getMessage());
        }
    }

    public function exportReport()
    {
        try {
            $this->validate();

            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            return response()->streamDownload(function () use ($startDate, $endDate) {
                echo $this->generateAnalyticsReport($startDate, $endDate);
            }, 'invoice-analytics-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            \Log::error('Invoice Analytics export error: ' . $e->getMessage());
            $this->addError('general', 'Failed to export report: ' . $e->getMessage());
            return null;
        }
    }

    public function getStatusDistribution(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $distribution = $this->reportingService->getInvoiceStatusDistribution($startDate, $endDate);
        
        // Calculate percentages
        $totalCount = array_sum(array_column($distribution, 'count'));
        
        return array_map(function($item) use ($totalCount) {
            $item['percentage'] = $totalCount > 0 ? ($item['count'] / $totalCount) * 100 : 0;
            return $item;
        }, $distribution);
    }

    public function getOverdueAnalysis(): array
    {
        $overdueInvoices = $this->reportingService->getOverdueInvoices();
        
        // Group by overdue ranges
        $ranges = [
            '1-30' => ['min' => 1, 'max' => 30, 'count' => 0, 'amount' => 0],
            '31-60' => ['min' => 31, 'max' => 60, 'count' => 0, 'amount' => 0],
            '61-90' => ['min' => 61, 'max' => 90, 'count' => 0, 'amount' => 0],
            '90+' => ['min' => 91, 'max' => PHP_INT_MAX, 'count' => 0, 'amount' => 0],
        ];

        foreach ($overdueInvoices as $invoice) {
            $daysOverdue = $invoice['days_overdue'];
            
            foreach ($ranges as $key => &$range) {
                if ($daysOverdue >= $range['min'] && $daysOverdue <= $range['max']) {
                    $range['count']++;
                    $range['amount'] += $invoice['outstanding_amount'];
                    break;
                }
            }
        }

        return [
            'total_overdue' => $overdueInvoices->count(),
            'total_overdue_amount' => $overdueInvoices->sum('outstanding_amount'),
            'ranges' => $ranges,
            'invoices' => $overdueInvoices->take(10)->toArray(), // Top 10 most overdue
        ];
    }

    public function getPaymentTrends(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        return $this->reportingService->getRevenueTrends($startDate, $endDate, 'daily');
    }

    public function getInvoiceAging(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        // Get all unpaid invoices in the date range
        $invoices = \App\Models\Invoice::whereBetween('invoice_date', [$startDate, $endDate])
            ->where('status', '!=', \App\Models\Invoice::PAID)
            ->where('status', '!=', \App\Models\Invoice::DRAFT)
            ->with(['client.user', 'payments'])
            ->get();

        $agingRanges = [
            'current' => ['min' => 0, 'max' => 0, 'count' => 0, 'amount' => 0],
            '1-30' => ['min' => 1, 'max' => 30, 'count' => 0, 'amount' => 0],
            '31-60' => ['min' => 31, 'max' => 60, 'count' => 0, 'amount' => 0],
            '61-90' => ['min' => 61, 'max' => 90, 'count' => 0, 'amount' => 0],
            '90+' => ['min' => 91, 'max' => PHP_INT_MAX, 'count' => 0, 'amount' => 0],
        ];

        foreach ($invoices as $invoice) {
            $totalPaid = $invoice->payments->where('is_approved', \App\Models\Payment::APPROVED)->sum('amount');
            $outstandingAmount = $invoice->final_amount - $totalPaid;
            
            if ($outstandingAmount <= 0) continue;

            $daysFromDue = Carbon::now()->diffInDays(Carbon::parse($invoice->due_date), false);
            
            if ($daysFromDue <= 0) {
                $agingRanges['current']['count']++;
                $agingRanges['current']['amount'] += $outstandingAmount;
            } else {
                foreach (['1-30', '31-60', '61-90', '90+'] as $key) {
                    $range = $agingRanges[$key];
                    if ($daysFromDue >= $range['min'] && $daysFromDue <= $range['max']) {
                        $agingRanges[$key]['count']++;
                        $agingRanges[$key]['amount'] += $outstandingAmount;
                        break;
                    }
                }
            }
        }

        return $agingRanges;
    }

    private function generateAnalyticsReport(Carbon $startDate, Carbon $endDate): string
    {
        $data = [
            'status_distribution' => $this->getStatusDistribution(),
            'overdue_analysis' => $this->getOverdueAnalysis(),
            'payment_trends' => $this->getPaymentTrends(),
            'invoice_aging' => $this->getInvoiceAging(),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
        ];

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.invoice-analytics-pdf', $data);
        
        return $pdf->output();
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
