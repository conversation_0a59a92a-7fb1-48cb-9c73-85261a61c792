<?php

namespace App\Filament\Client\Resources\QuoteResource\Pages;

use App\Filament\Client\Resources\QuoteResource;
use App\Http\Controllers\QuoteController;
use Filament\Actions;
use Filament\Actions\ActionGroup;
use Filament\Resources\Pages\ListRecords;

class ListQuotes extends ListRecords
{
    protected static string $resource = QuoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Actions\Action::make('excel')
                    ->label(__('messages.quote.excel_export'))
                    ->icon('heroicon-o-document-plus')
                    ->url(function () {
                        if (getLogInUser()->hasRole('admin')) {
                            return route('admin.quotesExcel');
                        } elseif (getLogInUser()->hasRole('client')) {
                            return route('client.quotesExcel');
                        }
                    }, shouldOpenInNewTab: true),
                Actions\Action::make('pdf')
                    ->label(__('messages.pdf_export'))
                    ->icon('heroicon-o-document-text')
                    ->url(function () {
                        if (getLogInUser()->hasRole('admin')) {
                            return route('admin.quotes.pdf');
                        } elseif (getLogInUser()->hasRole('client')) {
                            return route('client.export.quotes.pdf');
                        }
                    }, shouldOpenInNewTab: true),
            ])->button()->label(__('messages.common.export'))->icon('heroicon-s-chevron-down')->color('success'),
            Actions\CreateAction::make(),
        ];
    }
}
