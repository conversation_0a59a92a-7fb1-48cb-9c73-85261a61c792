<?php

namespace Tests\Feature;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Client;
use App\Models\User;
use App\Repositories\InvoiceRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FreeFormInvoiceTest extends TestCase
{
    use RefreshDatabase;

    protected InvoiceRepository $invoiceRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->invoiceRepository = new InvoiceRepository();
    }

    /** @test */
    public function it_can_create_invoice_with_custom_product_names()
    {
        // Create test user and client
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);

        $invoiceData = [
            'client_id' => $client->id,
            'invoice_id' => 'TEST001',
            'invoice_date' => now()->format('Y-m-d'),
            'due_date' => now()->addDays(30)->format('Y-m-d'),
            'invoiceItems' => [
                [
                    'product_name' => 'Custom Web Development Service',
                    'description' => 'Full-stack web application development with React and Laravel',
                    'quantity' => 1,
                    'price' => 2500.00,
                ],
                [
                    'product_name' => 'SEO Optimization',
                    'description' => 'Complete SEO audit and optimization for better search rankings',
                    'quantity' => 1,
                    'price' => 800.00,
                ]
            ]
        ];

        $invoice = $this->invoiceRepository->store($invoiceData);

        $this->assertInstanceOf(Invoice::class, $invoice);
        $this->assertEquals('TEST001', $invoice->invoice_id);
        $this->assertEquals(2, $invoice->invoiceItems->count());

        // Check first item
        $firstItem = $invoice->invoiceItems->first();
        $this->assertEquals('Custom Web Development Service', $firstItem->product_name);
        $this->assertEquals('Full-stack web application development with React and Laravel', $firstItem->description);
        $this->assertEquals(1, $firstItem->quantity);
        $this->assertEquals(2500.00, $firstItem->price);
        $this->assertNull($firstItem->product_id); // Should be null for custom products

        // Check second item
        $secondItem = $invoice->invoiceItems->skip(1)->first();
        $this->assertEquals('SEO Optimization', $secondItem->product_name);
        $this->assertEquals('Complete SEO audit and optimization for better search rankings', $secondItem->description);
        $this->assertEquals(1, $secondItem->quantity);
        $this->assertEquals(800.00, $secondItem->price);
        $this->assertNull($secondItem->product_id);
    }

    /** @test */
    public function it_validates_invoice_items_correctly()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);

        // Test with missing product_name (should fail)
        $invalidData = [
            'client_id' => $client->id,
            'invoice_id' => 'TEST002',
            'invoice_date' => now()->format('Y-m-d'),
            'due_date' => now()->addDays(30)->format('Y-m-d'),
            'invoiceItems' => [
                [
                    'description' => 'Service without name',
                    'quantity' => 1,
                    'price' => 100.00,
                ]
            ]
        ];

        $this->expectException(\Exception::class);
        $this->invoiceRepository->store($invalidData);
    }

    /** @test */
    public function it_calculates_totals_correctly_for_custom_products()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);

        $invoiceData = [
            'client_id' => $client->id,
            'invoice_id' => 'TEST003',
            'invoice_date' => now()->format('Y-m-d'),
            'due_date' => now()->addDays(30)->format('Y-m-d'),
            'invoiceItems' => [
                [
                    'product_name' => 'Consulting Hours',
                    'description' => 'Technical consulting at hourly rate',
                    'quantity' => 10,
                    'price' => 150.00,
                ],
                [
                    'product_name' => 'Project Management',
                    'description' => 'Full project management service',
                    'quantity' => 1,
                    'price' => 500.00,
                ]
            ]
        ];

        $invoice = $this->invoiceRepository->store($invoiceData);

        // First item: 10 * 150 = 1500
        // Second item: 1 * 500 = 500
        // Total: 2000
        $expectedTotal = 2000.00;

        $this->assertEquals($expectedTotal, $invoice->sub_total);
        $this->assertEquals($expectedTotal, $invoice->final_amount); // Assuming no taxes/discounts
    }

    /** @test */
    public function it_handles_mixed_product_types()
    {
        // This test would verify that the system can handle both
        // existing products (with product_id) and custom products (with product_name)
        // in the same invoice, but since we've moved to free-form only,
        // this is more of a legacy compatibility test
        
        $this->assertTrue(true); // Placeholder for now
    }
}
