<?php

namespace App\Livewire;

use Livewire\Component;
use Filament\Tables\Table;
use App\Models\InvoiceItem;
use App\Models\Tax;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;

class InvoiceItemTable extends Component implements HasTable, HasForms
{
    use InteractsWithForms;
    use InteractsWithTable;

    public $record;
    public $currency_id;

    public function mount($record)
    {
        $this->record = $record->id;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(InvoiceItem::where('invoice_id', $this->record))
            ->columns([
                TextColumn::make('product_name')
                    ->formatStateUsing(function ($record) {
                        // Show custom product name if available, otherwise show product name from database
                        if (!empty($record->product_name)) {
                            return $record->product_name;
                        } elseif (isset($record->product->name)) {
                            return $record->product->name;
                        }
                        return 'N/A';
                    })
                    ->description(function ($record) {
                        // Show description if available
                        return !empty($record->description) ? $record->description : null;
                    })
                    ->label(__('messages.product.product')),
                TextColumn::make('quantity')
                    ->label(__('messages.invoice.qty')),
                TextColumn::make('price')
                    ->formatStateUsing(fn($state) => getCurrencyAmount($state, true))
                    ->label(__('messages.invoice.price')),
                TextColumn::make('invoiceItemTax')
                    ->label(__('messages.quote.tax') . ' (in %)')
                    ->formatStateUsing(function ($record) {
                        $tax = [];
                        $getTaxes = Tax::whereIn('id', $record->invoiceItemTax->pluck('tax_id'))->get();
                        foreach ($getTaxes as $taxes) {
                            $tax[] = $taxes->value;
                        }
                        return !empty($tax) ? implode(', ', $tax) : '';
                    })
                    ->html(),
                TextColumn::make('total')
                    ->label(__('messages.invoice.amount'))
                    ->formatStateUsing(fn($record) => isset($record->total) ? getCurrencyAmount($record->total, true) : 'N/A')

            ])
            ->paginated(false)
            ->defaultSort('created_at', 'desc');
    }

    public function render()
    {
        return view('livewire.invoice-item-table');
    }
}
