<?php

namespace App\Filament\Widgets;

use App\Models\Invoice;
use App\Models\Payment;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\DB;

class IncomeOverview extends ChartWidget
{
    protected static bool $isLazy = true;
    protected int|string|array $columnSpan = 'full';
    protected static ?string $maxHeight = '500px'; // Increased from 400px for better chart display
    protected static ?int $sort = 2;

    // Add minimum height for consistent display
    protected static ?string $minHeight = '450px';
    public ?string $filter = 'this_month';

    protected static ?array $options = [
        'responsive' => true,
        'maintainAspectRatio' => false,
        'aspectRatio' => 2.5, // Better aspect ratio for wider charts
        'plugins' => [
            'legend' => [
                'display' => true,
                'position' => 'top',
                'labels' => [
                    'usePointStyle' => true,
                    'padding' => 20,
                    'font' => [
                        'size' => 12,
                    ],
                ],
            ],
            'tooltip' => [
                'enabled' => true,
                'backgroundColor' => 'rgba(0, 0, 0, 0.8)',
                'titleColor' => '#fff',
                'bodyColor' => '#fff',
                'borderColor' => '#fff',
                'borderWidth' => 1,
            ],
        ],
        'scales' => [
            'x' => [
                'display' => true,
                'grid' => [
                    'display' => false,
                ],
                'ticks' => [
                    'font' => [
                        'size' => 11,
                    ],
                ],
            ],
            'y' => [
                'display' => true,
                'grid' => [
                    'color' => 'rgba(0, 0, 0, 0.1)',
                ],
                'ticks' => [
                    'font' => [
                        'size' => 11,
                    ],
                ],
            ],
        ],
        'elements' => [
            'line' => [
                'tension' => 0.4,
            ],
            'point' => [
                'radius' => 4,
                'hoverRadius' => 6,
            ],
        ],
    ];
    public function getHeading(): string|Htmlable|null
    {
        return __('messages.admin_dashboard.income_overview');
    }
    public function totalFilterDay($startDate, $endDate): array
    {
        try {
            // Fix typo in variable name and add error handling
            $payments = Payment::whereIsApproved(Payment::APPROVED)
                ->whereBetween('payment_date', [$startDate, $endDate])
                ->select(DB::raw('DATE(payment_date) as date'), DB::raw('SUM(amount) as total_income'))
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            $transactionMap = [];
            foreach ($payments as $payment) {
                $transactionMap[$payment->date] = (float) $payment->total_income;
            }

            $period = CarbonPeriod::create($startDate, $endDate);

            $dateArr = [];
            $income = [];
            foreach ($period as $date) {
                $dateKey = $date->format('Y-m-d');
                $dateArr[] = $date->format('d-m-y');
                $income[] = $transactionMap[$dateKey] ?? 0;
            }

            $data['days'] = $dateArr;
            $data['income'] = [
                'label' => trans('messages.form.income') . ' (' . getCurrencySymbol() . ')',
                'data' => $income,
                'borderColor' => 'rgba(54, 162, 235, 1)',
                'backgroundColor' => 'rgba(54, 162, 235, 0.1)',
                'borderWidth' => 3,
                'fill' => true,
                'pointBackgroundColor' => 'rgba(54, 162, 235, 1)',
                'pointBorderColor' => '#fff',
                'pointBorderWidth' => 2,
                'pointHoverBackgroundColor' => '#fff',
                'pointHoverBorderColor' => 'rgba(54, 162, 235, 1)',
            ];

            return $data;
        } catch (\Exception $e) {
            // Log error and return empty data structure
            \Log::error('IncomeOverview chart data error: ' . $e->getMessage());

            return [
                'days' => [],
                'income' => [
                    'label' => trans('messages.form.income') . ' (' . getCurrencySymbol() . ')',
                    'data' => [],
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'backgroundColor' => 'rgba(54, 162, 235, 0.1)',
                    'borderWidth' => 3,
                    'fill' => true,
                    'pointBackgroundColor' => 'rgba(54, 162, 235, 1)',
                    'pointBorderColor' => '#fff',
                    'pointBorderWidth' => 2,
                    'pointHoverBackgroundColor' => '#fff',
                    'pointHoverBorderColor' => 'rgba(54, 162, 235, 1)',
                ],
            ];
        }
    }

    protected function getData(): array
    {
        try {
            $activeFilter = $this->filter ?? 'this_month';
            $start_date = null;
            $end_date = null;

            switch ($activeFilter) {
                case 'today':
                    $start_date = Carbon::today()->format('Y-m-d');
                    $end_date = Carbon::today()->format('Y-m-d');
                    break;
                case 'yesterday':
                    $start_date = Carbon::yesterday()->format('Y-m-d');
                    $end_date = Carbon::yesterday()->format('Y-m-d');
                    break;
                case 'last_7_days':
                    $start_date = Carbon::now()->subDays(6)->format('Y-m-d');
                    $end_date = Carbon::today()->format('Y-m-d');
                    break;
                case 'last_30_days':
                    $start_date = Carbon::now()->subDays(29)->format('Y-m-d');
                    $end_date = Carbon::today()->format('Y-m-d');
                    break;
                case 'this_month':
                    $start_date = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $end_date = Carbon::today()->format('Y-m-d');
                    break;
                case 'last_month':
                    $start_date = Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');
                    $end_date = Carbon::now()->subMonth()->endOfMonth()->format('Y-m-d');
                    break;
                default:
                    $start_date = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $end_date = Carbon::today()->format('Y-m-d');
                    break;
            }

            if ($start_date && $end_date) {
                $report = $this->totalFilterDay($start_date, $end_date);
            } else {
                $report = [
                    'days' => [],
                    'income' => [
                        'label' => trans('messages.form.income') . ' (' . getCurrencySymbol() . ')',
                        'data' => [],
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'backgroundColor' => 'rgba(54, 162, 235, 0.1)',
                        'borderWidth' => 3,
                        'fill' => true,
                    ],
                ];
            }

            return [
                'datasets' => [$report['income']],
                'labels' => $report['days'],
            ];
        } catch (\Exception $e) {
            \Log::error('IncomeOverview getData error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'datasets' => [[
                    'label' => trans('messages.form.income') . ' (' . getCurrencySymbol() . ')',
                    'data' => [],
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'backgroundColor' => 'rgba(54, 162, 235, 0.1)',
                    'borderWidth' => 3,
                    'fill' => true,
                ]],
                'labels' => [],
            ];
        }
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getFilters(): ?array
    {
        return [
            'today' => __('messages.datepicker.today'),
            'yesterday' => __('messages.datepicker.yesterday'),
            'last_7_days' => __('messages.datepicker.last_7_days'),
            'last_30_days' => __('messages.datepicker.last_30_days'),
            'this_month' => __('messages.datepicker.this_month'),
            'last_month' => __('messages.datepicker.last_month'),
        ];
    }
}
