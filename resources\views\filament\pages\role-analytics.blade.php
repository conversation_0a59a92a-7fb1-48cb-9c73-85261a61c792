<div class="space-y-6">
    <!-- Role Distribution Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Role Distribution</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <canvas id="roleDistributionChart" width="400" height="300"></canvas>
            </div>
            <div class="space-y-4">
                @foreach($analytics['role_distribution'] as $role)
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">{{ $role['name'] }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $role['permission_count'] }} permissions</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">{{ $role['user_count'] }} users</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $role['percentage'] }}%</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Role Hierarchy -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Role Hierarchy</h3>
        <div class="space-y-4">
            @foreach($hierarchy as $roleName => $roleInfo)
                <div class="flex items-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg {{ isset($roleInfo['role']) ? 'bg-green-50 dark:bg-green-900/20' : 'bg-gray-50 dark:bg-gray-700' }}">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-10 h-10 rounded-full flex items-center justify-center {{ $roleInfo['color'] === 'red' ? 'bg-red-100 text-red-600' : ($roleInfo['color'] === 'blue' ? 'bg-blue-100 text-blue-600' : ($roleInfo['color'] === 'green' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600')) }}">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ $roleName }}</h4>
                            <div class="flex space-x-2">
                                @if(isset($roleInfo['role']))
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $roleInfo['user_count'] ?? 0 }} users
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        {{ $roleInfo['permission_count'] ?? 0 }} permissions
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Not Created
                                    </span>
                                @endif
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $roleInfo['description'] }}</p>
                        <div class="mt-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400">Level {{ $roleInfo['level'] }}</span>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Permission Usage Statistics -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Permission Usage Statistics</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Permission</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Roles Using</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Usage %</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach(array_slice($analytics['permission_usage'], 0, 10) as $permission)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                {{ $permission['name'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ $permission['role_count'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $permission['usage_percentage'] }}%"></div>
                                    </div>
                                    {{ $permission['usage_percentage'] }}%
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($permission['usage_percentage'] > 75)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Widely Used
                                    </span>
                                @elseif($permission['usage_percentage'] > 25)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Moderately Used
                                    </span>
                                @else
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Rarely Used
                                    </span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center">
            <div class="text-3xl font-bold text-blue-600 mb-2">{{ $analytics['total_roles'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Roles</div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{{ $analytics['total_users_with_roles'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Users with Roles</div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center">
            <div class="text-3xl font-bold text-red-600 mb-2">{{ $analytics['users_without_roles'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Users without Roles</div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center">
            <div class="text-3xl font-bold text-purple-600 mb-2">{{ count($analytics['permission_usage']) }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Permissions</div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Role Distribution Chart
    const ctx = document.getElementById('roleDistributionChart');
    if (ctx) {
        const roleData = @json($analytics['role_distribution']);
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: roleData.map(role => role.name),
                datasets: [{
                    data: roleData.map(role => role.user_count),
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',   // red
                        'rgba(245, 158, 11, 0.8)',  // orange
                        'rgba(59, 130, 246, 0.8)',  // blue
                        'rgba(34, 197, 94, 0.8)',   // green
                        'rgba(168, 85, 247, 0.8)',  // purple
                        'rgba(107, 114, 128, 0.8)', // gray
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff',
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                    }
                }
            }
        });
    }
});
</script>
