<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Exception;
use PDO;

class SystemStabilityService
{
    /**
     * Perform comprehensive system health check
     */
    public function performHealthCheck(): array
    {
        $results = [
            'overall_status' => 'healthy',
            'checks' => [],
            'errors' => [],
            'warnings' => [],
            'recommendations' => []
        ];

        // Database connectivity check
        $results['checks']['database'] = $this->checkDatabaseHealth();
        
        // Extension availability check
        $results['checks']['extensions'] = $this->checkPHPExtensions();
        
        // File system permissions check
        $results['checks']['filesystem'] = $this->checkFileSystemPermissions();
        
        // Configuration validation check
        $results['checks']['configuration'] = $this->checkConfiguration();
        
        // Memory and performance check
        $results['checks']['performance'] = $this->checkPerformance();
        
        // Security check
        $results['checks']['security'] = $this->checkSecurity();

        // Determine overall status
        foreach ($results['checks'] as $check) {
            if ($check['status'] === 'error') {
                $results['overall_status'] = 'critical';
                $results['errors'][] = $check['message'];
            } elseif ($check['status'] === 'warning' && $results['overall_status'] !== 'critical') {
                $results['overall_status'] = 'warning';
                $results['warnings'][] = $check['message'];
            }
        }

        return $results;
    }

    /**
     * Check database health and connectivity
     */
    private function checkDatabaseHealth(): array
    {
        try {
            // Test current connection
            $connection = DB::connection();
            $pdo = $connection->getPdo();
            
            // Test basic query
            $result = DB::select('SELECT 1 as test');
            
            // Check critical tables
            $criticalTables = ['users', 'invoices', 'clients', 'settings'];
            $missingTables = [];
            
            foreach ($criticalTables as $table) {
                if (!Schema::hasTable($table)) {
                    $missingTables[] = $table;
                }
            }
            
            if (!empty($missingTables)) {
                return [
                    'status' => 'error',
                    'message' => 'Missing critical tables: ' . implode(', ', $missingTables),
                    'details' => ['missing_tables' => $missingTables]
                ];
            }
            
            // Check for the specific 'total' column issue
            if (Schema::hasTable('invoices') && !Schema::hasColumn('invoices', 'total')) {
                return [
                    'status' => 'error',
                    'message' => 'Invoice table missing total column',
                    'details' => ['missing_column' => 'total in invoices table']
                ];
            }
            
            return [
                'status' => 'healthy',
                'message' => 'Database connection and schema are healthy',
                'details' => [
                    'driver' => $pdo->getAttribute(PDO::ATTR_DRIVER_NAME),
                    'connection' => Config::get('database.default')
                ]
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'details' => ['exception' => $e->getMessage()]
            ];
        }
    }

    /**
     * Check PHP extensions
     */
    private function checkPHPExtensions(): array
    {
        $requiredExtensions = [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'pdo_sqlite' => extension_loaded('pdo_sqlite'),
            'openssl' => extension_loaded('openssl'),
            'mbstring' => extension_loaded('mbstring'),
            'tokenizer' => extension_loaded('tokenizer'),
            'xml' => extension_loaded('xml'),
            'ctype' => extension_loaded('ctype'),
            'json' => extension_loaded('json'),
            'bcmath' => extension_loaded('bcmath'),
            'fileinfo' => extension_loaded('fileinfo'),
        ];

        $missing = array_keys(array_filter($requiredExtensions, fn($loaded) => !$loaded));
        
        if (!empty($missing)) {
            return [
                'status' => 'error',
                'message' => 'Missing required PHP extensions: ' . implode(', ', $missing),
                'details' => ['missing_extensions' => $missing]
            ];
        }

        // Check PDO drivers
        if (extension_loaded('pdo')) {
            $drivers = PDO::getAvailableDrivers();
            if (!in_array('mysql', $drivers) && !in_array('sqlite', $drivers)) {
                return [
                    'status' => 'error',
                    'message' => 'No suitable PDO database drivers available',
                    'details' => ['available_drivers' => $drivers]
                ];
            }
        }

        return [
            'status' => 'healthy',
            'message' => 'All required PHP extensions are loaded',
            'details' => ['loaded_extensions' => array_keys($requiredExtensions)]
        ];
    }

    /**
     * Check file system permissions
     */
    private function checkFileSystemPermissions(): array
    {
        $paths = [
            'storage' => storage_path(),
            'bootstrap/cache' => base_path('bootstrap/cache'),
            'public' => public_path(),
        ];

        $issues = [];

        foreach ($paths as $name => $path) {
            if (!is_dir($path)) {
                $issues[] = "Directory {$name} does not exist: {$path}";
            } elseif (!is_writable($path)) {
                $issues[] = "Directory {$name} is not writable: {$path}";
            }
        }

        if (!empty($issues)) {
            return [
                'status' => 'error',
                'message' => 'File system permission issues detected',
                'details' => ['issues' => $issues]
            ];
        }

        return [
            'status' => 'healthy',
            'message' => 'File system permissions are correct',
            'details' => ['checked_paths' => array_keys($paths)]
        ];
    }

    /**
     * Check configuration
     */
    private function checkConfiguration(): array
    {
        $issues = [];

        // Check APP_KEY
        if (empty(config('app.key'))) {
            $issues[] = 'APP_KEY is not set';
        }

        // Check database configuration
        $dbConfig = config('database.connections.' . config('database.default'));
        if (empty($dbConfig)) {
            $issues[] = 'Database configuration is missing';
        }

        // Check session configuration
        if (config('session.driver') === 'database' && !Schema::hasTable('sessions')) {
            $issues[] = 'Session driver is set to database but sessions table does not exist';
        }

        if (!empty($issues)) {
            return [
                'status' => 'warning',
                'message' => 'Configuration issues detected',
                'details' => ['issues' => $issues]
            ];
        }

        return [
            'status' => 'healthy',
            'message' => 'Configuration is valid',
            'details' => ['app_env' => config('app.env')]
        ];
    }

    /**
     * Check performance metrics
     */
    private function checkPerformance(): array
    {
        $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB
        $peakMemory = memory_get_peak_usage(true) / 1024 / 1024; // MB

        // Test database query performance
        $start = microtime(true);
        try {
            DB::select('SELECT 1');
            $queryTime = (microtime(true) - $start) * 1000; // ms
        } catch (Exception $e) {
            $queryTime = null;
        }

        $warnings = [];
        if ($memoryUsage > 128) {
            $warnings[] = 'High memory usage: ' . round($memoryUsage, 2) . 'MB';
        }
        if ($queryTime && $queryTime > 100) {
            $warnings[] = 'Slow database queries: ' . round($queryTime, 2) . 'ms';
        }

        return [
            'status' => empty($warnings) ? 'healthy' : 'warning',
            'message' => empty($warnings) ? 'Performance is optimal' : 'Performance issues detected',
            'details' => [
                'memory_usage_mb' => round($memoryUsage, 2),
                'peak_memory_mb' => round($peakMemory, 2),
                'query_time_ms' => $queryTime ? round($queryTime, 2) : null,
                'warnings' => $warnings
            ]
        ];
    }

    /**
     * Check security configuration
     */
    private function checkSecurity(): array
    {
        $issues = [];

        // Check if debug mode is enabled in production
        if (config('app.env') === 'production' && config('app.debug')) {
            $issues[] = 'Debug mode is enabled in production';
        }

        // Check HTTPS configuration
        if (config('app.env') === 'production' && !config('app.url', '')->startsWith('https://')) {
            $issues[] = 'HTTPS is not configured for production';
        }

        // Check session security
        if (!config('session.secure') && config('app.env') === 'production') {
            $issues[] = 'Session cookies are not secure in production';
        }

        return [
            'status' => empty($issues) ? 'healthy' : 'warning',
            'message' => empty($issues) ? 'Security configuration is good' : 'Security issues detected',
            'details' => ['issues' => $issues]
        ];
    }

    /**
     * Auto-fix common issues
     */
    public function autoFixIssues(): array
    {
        $fixes = [];

        try {
            // Fix 1: Ensure storage directories exist and are writable
            $storageDirs = [
                storage_path('app'),
                storage_path('framework/cache'),
                storage_path('framework/sessions'),
                storage_path('framework/views'),
                storage_path('logs'),
            ];

            foreach ($storageDirs as $dir) {
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                    $fixes[] = "Created directory: {$dir}";
                }
            }

            // Fix 2: Clear and optimize caches
            try {
                \Illuminate\Support\Facades\Artisan::call('config:clear');
                \Illuminate\Support\Facades\Artisan::call('cache:clear');
                \Illuminate\Support\Facades\Artisan::call('view:clear');
                $fixes[] = 'Cleared application caches';
            } catch (Exception $e) {
                Log::warning('Cache clearing failed: ' . $e->getMessage());
            }

            // Fix 3: Ensure database connection is optimal
            if (Config::get('database.default') === 'mysql') {
                try {
                    $connection = DB::connection('mysql');
                    $pdo = $connection->getPdo();
                    $pdo->exec("SET SESSION sql_mode = 'TRADITIONAL'");
                    $fixes[] = 'Optimized MySQL connection settings';
                } catch (Exception $e) {
                    Log::warning('MySQL optimization failed: ' . $e->getMessage());
                }
            }

            return [
                'status' => 'success',
                'message' => 'Auto-fix completed successfully',
                'fixes_applied' => $fixes
            ];

        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Auto-fix failed: ' . $e->getMessage(),
                'fixes_applied' => $fixes
            ];
        }
    }

    /**
     * Get system information
     */
    public function getSystemInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_driver' => Config::get('database.default'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'extensions' => get_loaded_extensions(),
        ];
    }
}
