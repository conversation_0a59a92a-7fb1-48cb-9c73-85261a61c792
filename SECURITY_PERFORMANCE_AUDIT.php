<?php
/**
 * 🔒 COMPREHENSIVE SECURITY & PERFORMANCE AUDIT
 * 
 * This script performs a detailed security and performance analysis
 * of the Laravel invoice management system.
 * 
 * Usage: php SECURITY_PERFORMANCE_AUDIT.php
 */

echo "🔒 COMPREHENSIVE SECURITY & PERFORMANCE AUDIT\n";
echo str_repeat("=", 60) . "\n\n";

// Bootstrap Laravel
try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ Lara<PERSON> bootstrapped successfully\n";
} catch (Exception $e) {
    echo "❌ Laravel bootstrap failed: " . $e->getMessage() . "\n";
    exit(1);
}

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use App\Models\User;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Payment;

echo "\n🔍 SECTION 1: SECURITY AUDIT\n";
echo str_repeat("-", 40) . "\n";

// 1.1 Authentication & Authorization
echo "1.1 Authentication & Authorization:\n";

try {
    // Check password hashing configuration
    $hashRounds = config('hashing.bcrypt.rounds', 10);
    echo "   Password Hash Rounds: {$hashRounds} " . ($hashRounds >= 12 ? "✅" : "⚠️  (Recommend 12+)") . "\n";
    
    // Check session security
    $sessionSecure = config('session.secure');
    $sessionHttpOnly = config('session.http_only');
    $sessionSameSite = config('session.same_site');
    
    echo "   Session Secure Cookie: " . ($sessionSecure ? "✅ Enabled" : "⚠️  Disabled") . "\n";
    echo "   Session HTTP Only: " . ($sessionHttpOnly ? "✅ Enabled" : "❌ Disabled") . "\n";
    echo "   Session SameSite: {$sessionSameSite} " . ($sessionSameSite === 'strict' ? "✅" : "⚠️") . "\n";
    
    // Check for default admin users
    $defaultAdmins = User::where('is_default_admin', true)->count();
    echo "   Default Admin Users: {$defaultAdmins} " . ($defaultAdmins <= 1 ? "✅" : "⚠️  Multiple defaults") . "\n";
    
} catch (Exception $e) {
    echo "   ❌ Authentication audit error: " . $e->getMessage() . "\n";
}

// 1.2 Database Security
echo "\n1.2 Database Security:\n";

try {
    // Check for SQL injection vulnerabilities (basic check)
    $connection = config('database.default');
    $dbConfig = config("database.connections.{$connection}");
    
    echo "   Database Connection: {$connection}\n";
    echo "   Prepared Statements: ✅ Eloquent ORM (Protected)\n";
    
    // Check for sensitive data exposure
    $sensitiveColumns = [
        'users' => ['password', 'remember_token'],
        'payments' => ['transaction_id', 'meta'],
        'settings' => ['value'] // May contain API keys
    ];
    
    foreach ($sensitiveColumns as $table => $columns) {
        if (DB::getSchemaBuilder()->hasTable($table)) {
            echo "   Table '{$table}': ✅ Exists with sensitive columns\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Database security audit error: " . $e->getMessage() . "\n";
}

// 1.3 File Security
echo "\n1.3 File Security:\n";

try {
    // Check critical file permissions
    $criticalFiles = [
        '.env' => 'Environment configuration',
        'storage/logs' => 'Log directory',
        'storage/app' => 'Application storage',
        'bootstrap/cache' => 'Bootstrap cache'
    ];
    
    foreach ($criticalFiles as $file => $description) {
        if (file_exists($file)) {
            $perms = substr(sprintf('%o', fileperms($file)), -4);
            $isSecure = in_array($perms, ['0644', '0755', '0600', '0700']);
            echo "   {$file}: {$perms} " . ($isSecure ? "✅" : "⚠️  Check permissions") . "\n";
        } else {
            echo "   {$file}: ❌ Not found\n";
        }
    }
    
    // Check for exposed sensitive files
    $exposedFiles = [
        'public/.env',
        'public/composer.json',
        'public/artisan',
        'public/storage/logs'
    ];
    
    $exposedCount = 0;
    foreach ($exposedFiles as $file) {
        if (file_exists($file)) {
            $exposedCount++;
            echo "   ❌ EXPOSED: {$file}\n";
        }
    }
    
    if ($exposedCount === 0) {
        echo "   ✅ No sensitive files exposed in public directory\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ File security audit error: " . $e->getMessage() . "\n";
}

// 1.4 Input Validation & CSRF
echo "\n1.4 Input Validation & CSRF:\n";

try {
    // Check CSRF middleware configuration
    $middlewareFile = file_get_contents(base_path('bootstrap/app.php'));
    $csrfEnabled = strpos($middlewareFile, 'validateCsrfTokens') !== false;
    echo "   CSRF Protection: " . ($csrfEnabled ? "✅ Enabled" : "❌ Disabled") . "\n";
    
    // Check for CSRF exceptions
    if (preg_match('/except:\s*\[(.*?)\]/', $middlewareFile, $matches)) {
        $exceptions = $matches[1];
        echo "   CSRF Exceptions: {$exceptions}\n";
    }
    
    // Check validation rules in models (sample check)
    $modelFiles = glob(app_path('Models/*.php'));
    $validatedModels = 0;
    foreach ($modelFiles as $file) {
        $content = file_get_contents($file);
        if (strpos($content, 'fillable') !== false || strpos($content, 'guarded') !== false) {
            $validatedModels++;
        }
    }
    echo "   Models with Mass Assignment Protection: {$validatedModels}/" . count($modelFiles) . "\n";
    
} catch (Exception $e) {
    echo "   ❌ Input validation audit error: " . $e->getMessage() . "\n";
}

echo "\n🚀 SECTION 2: PERFORMANCE AUDIT\n";
echo str_repeat("-", 40) . "\n";

// 2.1 Database Performance
echo "2.1 Database Performance:\n";

try {
    // Check database connection and basic performance
    $start = microtime(true);
    $userCount = User::count();
    $queryTime = (microtime(true) - $start) * 1000;
    echo "   Basic Query Performance: {$queryTime}ms " . ($queryTime < 100 ? "✅" : "⚠️") . "\n";
    
    // Check for N+1 query potential
    $invoicesWithClients = Invoice::with('client')->limit(10)->get();
    echo "   Eager Loading: ✅ Implemented (with relationships)\n";
    
    // Check database size and table counts
    $tables = DB::select('SHOW TABLES');
    echo "   Database Tables: " . count($tables) . "\n";
    
    // Check for indexes on foreign keys
    $foreignKeyTables = ['invoices', 'invoice_items', 'payments', 'clients'];
    foreach ($foreignKeyTables as $table) {
        if (DB::getSchemaBuilder()->hasTable($table)) {
            $indexes = DB::select("SHOW INDEX FROM {$table}");
            $indexCount = count($indexes);
            echo "   {$table} indexes: {$indexCount} " . ($indexCount > 1 ? "✅" : "⚠️") . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Database performance audit error: " . $e->getMessage() . "\n";
}

// 2.2 Caching Performance
echo "\n2.2 Caching Performance:\n";

try {
    $cacheDriver = config('cache.default');
    echo "   Cache Driver: {$cacheDriver}\n";
    
    // Test cache performance
    $start = microtime(true);
    cache()->put('test_key', 'test_value', 60);
    $cached = cache()->get('test_key');
    $cacheTime = (microtime(true) - $start) * 1000;
    echo "   Cache Write/Read: {$cacheTime}ms " . ($cacheTime < 10 ? "✅" : "⚠️") . "\n";
    
    // Check for config caching
    $configCached = file_exists(base_path('bootstrap/cache/config.php'));
    echo "   Config Cache: " . ($configCached ? "✅ Enabled" : "⚠️  Not cached") . "\n";
    
    // Check for route caching
    $routeCached = file_exists(base_path('bootstrap/cache/routes-v7.php'));
    echo "   Route Cache: " . ($routeCached ? "✅ Enabled" : "⚠️  Not cached") . "\n";
    
} catch (Exception $e) {
    echo "   ❌ Caching audit error: " . $e->getMessage() . "\n";
}

// 2.3 Memory & Resource Usage
echo "\n2.3 Memory & Resource Usage:\n";

try {
    $memoryUsage = memory_get_usage(true) / 1024 / 1024;
    $peakMemory = memory_get_peak_usage(true) / 1024 / 1024;
    
    echo "   Current Memory: " . number_format($memoryUsage, 2) . " MB\n";
    echo "   Peak Memory: " . number_format($peakMemory, 2) . " MB " . ($peakMemory < 128 ? "✅" : "⚠️") . "\n";
    
    // Check PHP configuration
    $maxExecutionTime = ini_get('max_execution_time');
    $memoryLimit = ini_get('memory_limit');
    $uploadMaxSize = ini_get('upload_max_filesize');
    
    echo "   Max Execution Time: {$maxExecutionTime}s\n";
    echo "   Memory Limit: {$memoryLimit}\n";
    echo "   Upload Max Size: {$uploadMaxSize}\n";
    
} catch (Exception $e) {
    echo "   ❌ Resource usage audit error: " . $e->getMessage() . "\n";
}

// 2.4 Asset Optimization
echo "\n2.4 Asset Optimization:\n";

try {
    // Check for compiled assets
    $viteManifest = public_path('build/manifest.json');
    $hasViteBuild = file_exists($viteManifest);
    echo "   Vite Build: " . ($hasViteBuild ? "✅ Compiled" : "⚠️  Not compiled") . "\n";
    
    // Check asset sizes
    $cssFiles = glob(public_path('build/assets/*.css'));
    $jsFiles = glob(public_path('build/assets/*.js'));
    
    echo "   CSS Files: " . count($cssFiles) . "\n";
    echo "   JS Files: " . count($jsFiles) . "\n";
    
    if (!empty($cssFiles)) {
        $cssSize = filesize($cssFiles[0]) / 1024;
        echo "   Main CSS Size: " . number_format($cssSize, 2) . " KB " . ($cssSize < 500 ? "✅" : "⚠️") . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Asset optimization audit error: " . $e->getMessage() . "\n";
}

echo "\n📊 SECTION 3: AUDIT SUMMARY\n";
echo str_repeat("-", 40) . "\n";

// Generate recommendations
$recommendations = [
    "🔒 SECURITY RECOMMENDATIONS:",
    "1. Enable HTTPS and secure cookies in production",
    "2. Implement rate limiting for authentication endpoints",
    "3. Add two-factor authentication for admin users",
    "4. Regular security updates and dependency scanning",
    "5. Implement audit logging for sensitive operations",
    "",
    "🚀 PERFORMANCE RECOMMENDATIONS:",
    "1. Enable Redis/Memcached for session and cache storage",
    "2. Implement database query optimization and indexing",
    "3. Add CDN for static assets in production",
    "4. Enable OPcache for PHP performance",
    "5. Implement background job processing for heavy operations",
    "",
    "🛠️ IMMEDIATE ACTIONS:",
    "1. Run: php artisan config:cache",
    "2. Run: php artisan route:cache",
    "3. Run: php artisan view:cache",
    "4. Run: npm run build (for production assets)",
    "5. Review and update .env security settings"
];

foreach ($recommendations as $recommendation) {
    echo $recommendation . "\n";
}

echo "\n✅ Security & Performance audit completed!\n";
echo "📋 Review the recommendations above for system optimization.\n";
