<?php

namespace App\Filament\Pages;

use App\Services\ReportingService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class ClientReports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static string $view = 'filament.pages.client-reports';
    protected static ?string $navigationGroup = 'Reports';
    protected static ?int $navigationSort = 3;

    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $sortBy = 'total_invoice_amount';
    public ?string $sortDirection = 'desc';

    protected ReportingService $reportingService;

    public function boot(ReportingService $reportingService): void
    {
        $this->reportingService = $reportingService;
    }

    public function mount(): void
    {
        $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
    }

    public static function getNavigationLabel(): string
    {
        return __('Client Reports');
    }

    public function getTitle(): string | Htmlable
    {
        return __('Client Performance & Analytics');
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(4)->schema([
                    DatePicker::make('startDate')
                        ->label('Start Date')
                        ->required()
                        ->default(Carbon::now()->startOfMonth())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'before_or_equal:endDate'])
                        ->validationAttribute('Start Date'),
                    DatePicker::make('endDate')
                        ->label('End Date')
                        ->required()
                        ->default(Carbon::now())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'after_or_equal:startDate'])
                        ->validationAttribute('End Date'),
                    Select::make('sortBy')
                        ->label('Sort By')
                        ->options([
                            'total_invoice_amount' => 'Total Invoice Amount',
                            'total_payments' => 'Total Payments',
                            'outstanding_amount' => 'Outstanding Amount',
                            'payment_ratio' => 'Payment Ratio',
                            'total_invoices' => 'Total Invoices',
                        ])
                        ->default('total_invoice_amount')
                        ->required()
                        ->rules(['required', 'in:total_invoice_amount,total_payments,outstanding_amount,payment_ratio,total_invoices'])
                        ->validationAttribute('Sort By'),
                    Select::make('sortDirection')
                        ->label('Sort Direction')
                        ->options([
                            'desc' => 'Descending',
                            'asc' => 'Ascending',
                        ])
                        ->default('desc')
                        ->required()
                        ->rules(['required', 'in:desc,asc'])
                        ->validationAttribute('Sort Direction'),
                ]),
            ])
            ->statePath('data');
    }

    /**
     * Get validation rules for the form
     */
    protected function getValidationRules(): array
    {
        return [
            'data.startDate' => ['required', 'date', 'before_or_equal:data.endDate'],
            'data.endDate' => ['required', 'date', 'after_or_equal:data.startDate'],
            'data.sortBy' => ['required', 'in:total_invoice_amount,total_payments,outstanding_amount,payment_ratio,total_invoices'],
            'data.sortDirection' => ['required', 'in:desc,asc'],
        ];
    }

    /**
     * Get validation attributes for better error messages
     */
    protected function getValidationAttributes(): array
    {
        return [
            'data.startDate' => 'Start Date',
            'data.endDate' => 'End Date',
            'data.sortBy' => 'Sort By',
            'data.sortDirection' => 'Sort Direction',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateReport')
                ->label('Generate Report')
                ->icon('heroicon-o-document-chart-bar')
                ->color('primary')
                ->action('generateReport'),
            Action::make('exportCSV')
                ->label('Export CSV')
                ->icon('heroicon-o-table-cells')
                ->color('success')
                ->action('exportCSV'),
            Action::make('exportPDF')
                ->label('Export PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->color('warning')
                ->action('exportPDF'),
        ];
    }

    public function generateReport(): void
    {
        try {
            $this->validate();

            // Access form data through the data property
            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);
            $sortBy = $this->data['sortBy'];
            $sortDirection = $this->data['sortDirection'];

            $this->dispatch('client-report-generated', [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
                'sortBy' => $sortBy,
                'sortDirection' => $sortDirection,
            ]);
        } catch (\Exception $e) {
            \Log::error('Client Report generation error: ' . $e->getMessage());

            $this->addError('general', 'Failed to generate report: ' . $e->getMessage());
        }
    }

    public function exportCSV()
    {
        try {
            $this->validate();

            // Access form data through the data property
            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            $clients = $this->getClientPerformance();

            return response()->streamDownload(function () use ($clients) {
                echo $this->generateCSV($clients);
            }, 'client-performance-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.csv');
        } catch (\Exception $e) {
            \Log::error('Client Report CSV export error: ' . $e->getMessage());

            $this->addError('general', 'Failed to export CSV: ' . $e->getMessage());
            return null;
        }
    }

    public function exportPDF()
    {
        try {
            $this->validate();

            // Access form data through the data property
            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            return response()->streamDownload(function () use ($startDate, $endDate) {
                echo $this->generatePDFReport($startDate, $endDate);
            }, 'client-performance-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            \Log::error('Client Report PDF export error: ' . $e->getMessage());

            $this->addError('general', 'Failed to export PDF: ' . $e->getMessage());
            return null;
        }
    }

    public function getClientPerformance(): array
    {
        try {
            // Access form data through the data property
            if (!isset($this->data['startDate']) || !isset($this->data['endDate'])) {
                return [];
            }

            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);
            $sortBy = $this->data['sortBy'] ?? 'total_invoice_amount';
            $sortDirection = $this->data['sortDirection'] ?? 'desc';

            $clients = $this->reportingService->getClientPerformance($startDate, $endDate);

            // Apply sorting
            if ($sortDirection === 'desc') {
                $clients = $clients->sortByDesc($sortBy);
            } else {
                $clients = $clients->sortBy($sortBy);
            }

            return $clients->values()->toArray();
        } catch (\Exception $e) {
            \Log::error('Client Performance retrieval error: ' . $e->getMessage());
            return [];
        }
    }

    public function getTopPerformingClients(): array
    {
        $clients = $this->getClientPerformance();
        return array_slice($clients, 0, 5);
    }

    public function getClientSummaryStats(): array
    {
        $clients = $this->getClientPerformance();
        
        if (empty($clients)) {
            return [
                'total_clients' => 0,
                'total_revenue' => 0,
                'total_outstanding' => 0,
                'avg_payment_ratio' => 0,
            ];
        }

        return [
            'total_clients' => count($clients),
            'total_revenue' => array_sum(array_column($clients, 'total_invoice_amount')),
            'total_outstanding' => array_sum(array_column($clients, 'outstanding_amount')),
            'avg_payment_ratio' => array_sum(array_column($clients, 'payment_ratio')) / count($clients),
        ];
    }

    public function getPaymentRatioDistribution(): array
    {
        $clients = $this->getClientPerformance();
        
        $distribution = [
            'excellent' => ['min' => 90, 'max' => 100, 'count' => 0, 'label' => 'Excellent (90-100%)'],
            'good' => ['min' => 70, 'max' => 89, 'count' => 0, 'label' => 'Good (70-89%)'],
            'fair' => ['min' => 50, 'max' => 69, 'count' => 0, 'label' => 'Fair (50-69%)'],
            'poor' => ['min' => 0, 'max' => 49, 'count' => 0, 'label' => 'Poor (0-49%)'],
        ];

        foreach ($clients as $client) {
            $ratio = $client['payment_ratio'];
            
            foreach ($distribution as $key => &$range) {
                if ($ratio >= $range['min'] && $ratio <= $range['max']) {
                    $range['count']++;
                    break;
                }
            }
        }

        return $distribution;
    }

    private function generateCSV(array $clients): string
    {
        $output = fopen('php://temp', 'r+');
        
        // CSV Headers
        fputcsv($output, [
            'Client Name',
            'Email',
            'Total Invoices',
            'Total Invoice Amount',
            'Total Payments',
            'Outstanding Amount',
            'Payment Ratio (%)',
            'Average Invoice Value',
            'Last Invoice Date',
            'Last Payment Date',
        ]);
        
        // CSV Data
        foreach ($clients as $client) {
            fputcsv($output, [
                $client['client_name'],
                $client['client_email'],
                $client['total_invoices'],
                number_format($client['total_invoice_amount'], 2),
                number_format($client['total_payments'], 2),
                number_format($client['outstanding_amount'], 2),
                number_format($client['payment_ratio'], 2),
                number_format($client['avg_invoice_value'], 2),
                $client['last_invoice_date'] ?? 'N/A',
                $client['last_payment_date'] ?? 'N/A',
            ]);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }

    private function generatePDFReport(Carbon $startDate, Carbon $endDate): string
    {
        $data = [
            'clients' => $this->getClientPerformance(),
            'summary_stats' => $this->getClientSummaryStats(),
            'payment_distribution' => $this->getPaymentRatioDistribution(),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
        ];

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.client-performance-pdf', $data);
        
        return $pdf->output();
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
