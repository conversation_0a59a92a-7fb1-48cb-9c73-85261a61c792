<?php

namespace App\Filament\Resources\AuditLogResource\Pages;

use App\Filament\Resources\AuditLogResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\KeyValueEntry;

class ViewAuditLog extends ViewRecord
{
    protected static string $resource = AuditLogResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Audit Log Details')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('user.name')
                                    ->label('User')
                                    ->default('System'),
                                
                                TextEntry::make('action')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'created' => 'success',
                                        'updated' => 'info',
                                        'deleted' => 'danger',
                                        'login_success' => 'success',
                                        'login_failed' => 'danger',
                                        default => 'gray',
                                    }),
                            ]),
                        
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('model_type')
                                    ->label('Model Type')
                                    ->formatStateUsing(fn (?string $state): string => 
                                        $state ? class_basename($state) : 'N/A'
                                    ),
                                
                                TextEntry::make('model_id')
                                    ->label('Model ID'),
                            ]),
                        
                        TextEntry::make('description')
                            ->columnSpanFull(),
                        
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('severity')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'low' => 'success',
                                        'medium' => 'info',
                                        'high' => 'warning',
                                        'critical' => 'danger',
                                        default => 'gray',
                                    }),
                                
                                TextEntry::make('created_at')
                                    ->label('Date & Time')
                                    ->dateTime(),
                            ]),
                    ]),

                Section::make('Request Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('ip_address')
                                    ->label('IP Address'),
                                
                                TextEntry::make('method')
                                    ->label('HTTP Method')
                                    ->badge(),
                            ]),
                        
                        TextEntry::make('url')
                            ->label('URL')
                            ->columnSpanFull(),
                        
                        TextEntry::make('user_agent')
                            ->label('User Agent')
                            ->columnSpanFull(),
                    ]),

                Section::make('Data Changes')
                    ->schema([
                        KeyValueEntry::make('old_values')
                            ->label('Old Values')
                            ->columnSpanFull(),
                        
                        KeyValueEntry::make('new_values')
                            ->label('New Values')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => !empty($record->old_values) || !empty($record->new_values)),

                Section::make('Additional Information')
                    ->schema([
                        TextEntry::make('tags')
                            ->label('Tags')
                            ->badge()
                            ->separator(',')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => !empty($record->tags)),
            ]);
    }
}
