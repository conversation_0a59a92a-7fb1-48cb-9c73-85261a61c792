<?php

namespace App\Filament\Pages;

use App\Services\ReportingService;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Grid;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;

class ProductAnalytics extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static string $view = 'filament.pages.product-analytics';
    protected static ?string $navigationGroup = 'Reports';
    protected static ?int $navigationSort = 4;

    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $analysisType = 'performance';
    public ?string $sortBy = 'total_amount';

    protected ReportingService $reportingService;

    public function boot(ReportingService $reportingService): void
    {
        $this->reportingService = $reportingService;
    }

    public function mount(): void
    {
        $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
    }

    public static function getNavigationLabel(): string
    {
        return __('Product Analytics');
    }

    public function getTitle(): string | Htmlable
    {
        return __('Product & Service Analytics');
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(4)->schema([
                    DatePicker::make('startDate')
                        ->label('Start Date')
                        ->required()
                        ->default(Carbon::now()->startOfMonth())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'before_or_equal:endDate'])
                        ->validationAttribute('Start Date'),
                    DatePicker::make('endDate')
                        ->label('End Date')
                        ->required()
                        ->default(Carbon::now())
                        ->maxDate(Carbon::now())
                        ->rules(['required', 'date', 'after_or_equal:startDate'])
                        ->validationAttribute('End Date'),
                    Select::make('analysisType')
                        ->label('Analysis Type')
                        ->options([
                            'performance' => 'Performance Analysis',
                            'trends' => 'Sales Trends',
                            'comparison' => 'Product vs Freeform',
                        ])
                        ->default('performance')
                        ->required()
                        ->rules(['required', 'in:performance,trends,comparison'])
                        ->validationAttribute('Analysis Type'),
                    Select::make('sortBy')
                        ->label('Sort By')
                        ->options([
                            'total_amount' => 'Total Revenue',
                            'total_quantity' => 'Total Quantity',
                            'invoice_count' => 'Invoice Count',
                            'avg_price' => 'Average Price',
                        ])
                        ->default('total_amount')
                        ->required()
                        ->rules(['required', 'in:total_amount,total_quantity,invoice_count,avg_price'])
                        ->validationAttribute('Sort By'),
                ]),
            ])
            ->statePath('data');
    }

    protected function getValidationRules(): array
    {
        return [
            'data.startDate' => ['required', 'date', 'before_or_equal:data.endDate'],
            'data.endDate' => ['required', 'date', 'after_or_equal:data.startDate'],
            'data.analysisType' => ['required', 'in:performance,trends,comparison'],
            'data.sortBy' => ['required', 'in:total_amount,total_quantity,invoice_count,avg_price'],
        ];
    }

    protected function getValidationAttributes(): array
    {
        return [
            'data.startDate' => 'Start Date',
            'data.endDate' => 'End Date',
            'data.analysisType' => 'Analysis Type',
            'data.sortBy' => 'Sort By',
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateAnalysis')
                ->label('Generate Analysis')
                ->icon('heroicon-o-chart-bar-square')
                ->color('primary')
                ->action('generateAnalysis'),
            Action::make('exportReport')
                ->label('Export Report')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->action('exportReport'),
        ];
    }

    public function generateAnalysis(): void
    {
        try {
            $this->validate();

            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);
            $analysisType = $this->data['analysisType'];
            $sortBy = $this->data['sortBy'];

            $this->dispatch('product-analysis-generated', [
                'analysisType' => $analysisType,
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
                'sortBy' => $sortBy,
            ]);
        } catch (\Exception $e) {
            \Log::error('Product Analysis generation error: ' . $e->getMessage());
            $this->addError('general', 'Failed to generate analysis: ' . $e->getMessage());
        }
    }

    public function exportReport()
    {
        try {
            $this->validate();

            $startDate = Carbon::parse($this->data['startDate']);
            $endDate = Carbon::parse($this->data['endDate']);

            return response()->streamDownload(function () use ($startDate, $endDate) {
                echo $this->generatePDFReport($startDate, $endDate);
            }, 'product-analytics-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            \Log::error('Product Analytics export error: ' . $e->getMessage());
            $this->addError('general', 'Failed to export report: ' . $e->getMessage());
            return null;
        }
    }

    public function getProductPerformance(): array
    {
        if (!$this->startDate || !$this->endDate) {
            return [];
        }

        $startDate = Carbon::parse($this->startDate);
        $endDate = Carbon::parse($this->endDate);

        $products = $this->reportingService->getProductPerformance($startDate, $endDate);
        
        // Apply sorting
        $products = $products->sortByDesc($this->sortBy);

        return $products->values()->toArray();
    }

    public function getTopProducts(): array
    {
        $products = $this->getProductPerformance();
        return array_slice($products, 0, 10);
    }

    public function getProductSummaryStats(): array
    {
        $products = $this->getProductPerformance();
        
        if (empty($products)) {
            return [
                'total_products' => 0,
                'total_revenue' => 0,
                'total_quantity' => 0,
                'avg_price' => 0,
                'database_products' => 0,
                'freeform_products' => 0,
            ];
        }

        $databaseProducts = array_filter($products, fn($p) => $p['type'] === 'product');
        $freeformProducts = array_filter($products, fn($p) => $p['type'] === 'freeform');

        return [
            'total_products' => count($products),
            'total_revenue' => array_sum(array_column($products, 'total_amount')),
            'total_quantity' => array_sum(array_column($products, 'total_quantity')),
            'avg_price' => array_sum(array_column($products, 'avg_price')) / count($products),
            'database_products' => count($databaseProducts),
            'freeform_products' => count($freeformProducts),
        ];
    }

    public function getProductTypeComparison(): array
    {
        $products = $this->getProductPerformance();
        
        $databaseProducts = array_filter($products, fn($p) => $p['type'] === 'product');
        $freeformProducts = array_filter($products, fn($p) => $p['type'] === 'freeform');

        return [
            'database' => [
                'count' => count($databaseProducts),
                'total_amount' => array_sum(array_column($databaseProducts, 'total_amount')),
                'total_quantity' => array_sum(array_column($databaseProducts, 'total_quantity')),
                'avg_price' => count($databaseProducts) > 0 ? array_sum(array_column($databaseProducts, 'avg_price')) / count($databaseProducts) : 0,
            ],
            'freeform' => [
                'count' => count($freeformProducts),
                'total_amount' => array_sum(array_column($freeformProducts, 'total_amount')),
                'total_quantity' => array_sum(array_column($freeformProducts, 'total_quantity')),
                'avg_price' => count($freeformProducts) > 0 ? array_sum(array_column($freeformProducts, 'avg_price')) / count($freeformProducts) : 0,
            ],
        ];
    }

    public function getRevenueDistribution(): array
    {
        $products = $this->getProductPerformance();
        $totalRevenue = array_sum(array_column($products, 'total_amount'));
        
        if ($totalRevenue == 0) {
            return [];
        }

        $distribution = [];
        foreach (array_slice($products, 0, 10) as $product) {
            $distribution[] = [
                'name' => $product['product_name'],
                'amount' => $product['total_amount'],
                'percentage' => ($product['total_amount'] / $totalRevenue) * 100,
            ];
        }

        return $distribution;
    }

    public function getQuantityAnalysis(): array
    {
        $products = $this->getProductPerformance();
        
        // Sort by quantity for this analysis
        usort($products, fn($a, $b) => $b['total_quantity'] <=> $a['total_quantity']);
        
        return array_slice($products, 0, 10);
    }

    private function generatePDFReport(Carbon $startDate, Carbon $endDate): string
    {
        $data = [
            'products' => $this->getProductPerformance(),
            'summary_stats' => $this->getProductSummaryStats(),
            'type_comparison' => $this->getProductTypeComparison(),
            'revenue_distribution' => $this->getRevenueDistribution(),
            'quantity_analysis' => $this->getQuantityAnalysis(),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
        ];

        $pdf = app('dompdf.wrapper');
        $pdf->loadView('reports.product-analytics-pdf', $data);
        
        return $pdf->output();
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
