<?php

namespace App\Filament\Resources\AuditLogResource\Pages;

use App\Filament\Resources\AuditLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\Action;
use App\Models\AuditLog;
use Carbon\Carbon;

class ListAuditLogs extends ListRecords
{
    protected static string $resource = AuditLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('cleanup_old_logs')
                ->label('Cleanup Old Logs')
                ->icon('heroicon-o-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Cleanup Old Audit Logs')
                ->modalDescription('This will delete audit logs older than 90 days. This action cannot be undone.')
                ->action(function (): void {
                    $deleted = AuditLog::where('created_at', '<', Carbon::now()->subDays(90))->delete();
                    
                    $this->notify('success', "Deleted {$deleted} old audit log entries");
                }),
            
            Action::make('export_logs')
                ->label('Export Logs')
                ->icon('heroicon-o-document-arrow-down')
                ->color('info')
                ->action(function (): void {
                    // Implementation for exporting logs
                    $this->notify('info', 'Export functionality coming soon');
                }),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            AuditLogResource\Widgets\AuditLogStatsWidget::class,
        ];
    }
}
