<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class InvoiceStatusService
{
    /**
     * Update invoice status based on payment information
     */
    public function updateStatusFromPayment(Invoice $invoice, float $paymentAmount = 0): int
    {
        // Get total approved payments including the new payment
        $totalPaid = $invoice->payments()
            ->where('is_approved', Payment::APPROVED)
            ->sum('amount') + $paymentAmount;

        $finalAmount = $invoice->final_amount;

        // Determine new status based on payment amount
        if ($totalPaid >= $finalAmount) {
            $newStatus = Invoice::PAID;
        } elseif ($totalPaid > 0) {
            $newStatus = Invoice::PARTIALLY;
        } else {
            $newStatus = Invoice::UNPAID;
        }

        // Check for overdue status
        if ($newStatus !== Invoice::PAID && $this->isOverdue($invoice)) {
            $newStatus = Invoice::OVERDUE;
        }

        $this->updateInvoiceStatus($invoice, $newStatus);
        
        return $newStatus;
    }

    /**
     * Update invoice status with validation
     */
    public function updateInvoiceStatus(Invoice $invoice, int $newStatus): bool
    {
        $currentStatus = $invoice->status;

        if (!$this->isValidStatusTransition($currentStatus, $newStatus)) {
            Log::warning("Invalid status transition attempted", [
                'invoice_id' => $invoice->id,
                'current_status' => $currentStatus,
                'new_status' => $newStatus
            ]);
            return false;
        }

        $invoice->update(['status' => $newStatus]);

        // Clear dashboard cache when invoice status changes
        $this->clearDashboardCache();

        Log::info("Invoice status updated", [
            'invoice_id' => $invoice->id,
            'old_status' => $currentStatus,
            'new_status' => $newStatus
        ]);

        return true;
    }

    /**
     * Clear dashboard cache for all users
     */
    private function clearDashboardCache(): void
    {
        try {
            // Clear common dashboard cache patterns
            \Cache::forget('dashboard_payment_overview_' . auth()->id());
            \Cache::forget('dashboard_invoice_overview_' . auth()->id());

            // Clear cache for all users (if needed for admin dashboards)
            $cacheKeys = [
                'dashboard_payment_overview_*',
                'dashboard_invoice_overview_*',
                'income_overview_*',
                'reporting_*'
            ];

            foreach ($cacheKeys as $pattern) {
                \Cache::flush(); // For simplicity, flush all cache
            }
        } catch (\Exception $e) {
            Log::warning("Failed to clear dashboard cache: " . $e->getMessage());
        }
    }

    /**
     * Check if a status transition is valid
     */
    public function isValidStatusTransition(int $currentStatus, int $newStatus): bool
    {
        // Define valid status transitions
        $validTransitions = [
            Invoice::DRAFT => [Invoice::DRAFT, Invoice::UNPAID],
            Invoice::UNPAID => [Invoice::UNPAID, Invoice::PAID, Invoice::PARTIALLY, Invoice::PROCESSING, Invoice::OVERDUE],
            Invoice::PAID => [Invoice::PAID, Invoice::PARTIALLY], // Allow partial refunds
            Invoice::PARTIALLY => [Invoice::PARTIALLY, Invoice::PAID, Invoice::UNPAID, Invoice::OVERDUE],
            Invoice::PROCESSING => [Invoice::PROCESSING, Invoice::PAID, Invoice::PARTIALLY, Invoice::UNPAID, Invoice::OVERDUE],
            Invoice::OVERDUE => [Invoice::OVERDUE, Invoice::PAID, Invoice::PARTIALLY, Invoice::PROCESSING, Invoice::UNPAID],
        ];

        return isset($validTransitions[$currentStatus]) && 
               in_array($newStatus, $validTransitions[$currentStatus]);
    }

    /**
     * Check if invoice is overdue
     */
    public function isOverdue(Invoice $invoice): bool
    {
        return Carbon::parse($invoice->due_date)->isPast();
    }

    /**
     * Get human-readable status name
     */
    public function getStatusName(int $status): string
    {
        return Invoice::STATUS_ARR[$status] ?? 'Unknown';
    }

    /**
     * Get all invoices that should be marked as overdue
     */
    public function getOverdueInvoices()
    {
        return Invoice::where('status', '!=', Invoice::PAID)
            ->where('status', '!=', Invoice::DRAFT)
            ->where('due_date', '<', Carbon::now())
            ->get();
    }

    /**
     * Update overdue invoices (can be run as a scheduled task)
     */
    public function updateOverdueInvoices(): int
    {
        $overdueInvoices = $this->getOverdueInvoices();
        $updated = 0;

        foreach ($overdueInvoices as $invoice) {
            if ($this->updateInvoiceStatus($invoice, Invoice::OVERDUE)) {
                $updated++;
            }
        }

        return $updated;
    }

    /**
     * Calculate invoice status based on current payments
     */
    public function calculateCurrentStatus(Invoice $invoice): int
    {
        $totalPaid = $invoice->payments()
            ->where('is_approved', Payment::APPROVED)
            ->sum('amount');

        if ($totalPaid >= $invoice->final_amount) {
            return Invoice::PAID;
        } elseif ($totalPaid > 0) {
            return Invoice::PARTIALLY;
        } elseif ($this->isOverdue($invoice)) {
            return Invoice::OVERDUE;
        } else {
            return Invoice::UNPAID;
        }
    }
}
