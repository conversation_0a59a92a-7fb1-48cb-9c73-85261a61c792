<?php

namespace App\Filament\Widgets;

use App\Models\Invoice;
use App\Models\Client;
use App\Models\Payment;
use App\Models\User;
use Filament\Widgets\Widget;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ActivityFeedWidget extends Widget
{
    protected static string $view = 'filament.widgets.activity-feed';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public function getActivities(): Collection
    {
        $activities = collect();

        // Recent invoices
        $recentInvoices = Invoice::with('client')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($invoice) {
                return [
                    'type' => 'invoice',
                    'title' => "Invoice #{$invoice->invoice_number} created",
                    'description' => "For {$invoice->client->name} - $" . number_format($invoice->total, 2),
                    'time' => $invoice->created_at,
                    'icon' => 'heroicon-o-document-text',
                    'color' => $this->getInvoiceStatusColor($invoice->status),
                    'url' => route('filament.admin.resources.invoices.view', $invoice),
                ];
            });

        // Recent payments
        $recentPayments = Payment::with('invoice.client')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($payment) {
                return [
                    'type' => 'payment',
                    'title' => "Payment received",
                    'description' => "From {$payment->invoice->client->name} - $" . number_format($payment->amount, 2),
                    'time' => $payment->created_at,
                    'icon' => 'heroicon-o-banknotes',
                    'color' => 'success',
                    'url' => route('filament.admin.resources.payments.view', $payment),
                ];
            });

        // Recent clients
        $recentClients = Client::latest()
            ->limit(3)
            ->get()
            ->map(function ($client) {
                return [
                    'type' => 'client',
                    'title' => "New client added",
                    'description' => $client->name . " - " . $client->email,
                    'time' => $client->created_at,
                    'icon' => 'heroicon-o-user-plus',
                    'color' => 'info',
                    'url' => route('filament.admin.resources.clients.view', $client),
                ];
            });

        // Recent users (admin users only)
        $recentUsers = User::whereHas('roles', fn($q) => $q->where('name', \App\Models\Role::ROLE_ADMIN))
            ->latest()
            ->limit(2)
            ->get()
            ->map(function ($user) {
                return [
                    'type' => 'user',
                    'title' => "New admin registered",
                    'description' => $user->name . " - " . $user->email,
                    'time' => $user->created_at,
                    'icon' => 'heroicon-o-user',
                    'color' => 'primary',
                    'url' => route('filament.admin.resources.admins.view', $user),
                ];
            });

        // Merge and sort activities
        $activities = $activities
            ->merge($recentInvoices)
            ->merge($recentPayments)
            ->merge($recentClients)
            ->merge($recentUsers)
            ->sortByDesc('time')
            ->take(15);

        return $activities;
    }

    private function getInvoiceStatusColor(string $status): string
    {
        return match ($status) {
            'paid' => 'success',
            'sent' => 'info',
            'overdue' => 'danger',
            'cancelled' => 'gray',
            default => 'warning',
        };
    }

    public function getQuickStats(): array
    {
        $today = Carbon::today();
        
        return [
            'today_invoices' => Invoice::whereDate('created_at', $today)->count(),
            'today_payments' => Payment::whereDate('created_at', $today)->count(),
            'today_revenue' => Payment::whereDate('created_at', $today)->sum('amount'),
            'pending_invoices' => Invoice::where('status', 'sent')->count(),
            'overdue_invoices' => Invoice::where('status', 'overdue')->count(),
        ];
    }
}
