<?php

namespace App\Repositories;

use App\Models\Client;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Service;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;
use SebastianBergmann\CodeCoverage\Report\Html\Dashboard;

/**
 * Class DashboardRepository
 */
class DashboardRepository
{
    public function getFieldsSearchable()
    {
        // TODO: Implement getFieldsSearchable() method.
    }

    public function model(): string
    {
        return Dashboard::class;
    }

    public function getPaymentOverviewData(): array
    {
        try {
            // Cache key for performance optimization
            $cacheKey = 'dashboard_payment_overview_' . auth()->id();

            // Clear cache for real-time data
            \Cache::forget($cacheKey);

            return \Cache::remember($cacheKey, 60, function () { // 1 minute cache for real-time feel
                $data = [];

                // Optimized query: Use aggregation instead of loading all records
                $invoiceStats = Invoice::where('status', '!=', Invoice::DRAFT)
                    ->selectRaw('COUNT(*) as total_records, SUM(final_amount) as invoice_amount')
                    ->first();

                $data['total_records'] = $invoiceStats->total_records ?? 0;
                $data['invoice_amount'] = (float) ($invoiceStats->invoice_amount ?? 0);

                // Optimized payment query
                $data['received_amount'] = (float) Payment::where('is_approved', Payment::APPROVED)
                    ->sum('amount');

                $data['due_amount'] = max(0, $data['invoice_amount'] - $data['received_amount']);

                $data['labels'] = [
                    __('messages.received_amount'),
                    __('messages.invoice.due_amount'),
                ];
                $data['dataPoints'] = [$data['received_amount'], $data['due_amount']];

                return $data;
            });
        } catch (\Exception $e) {
            \Log::error('DashboardRepository getPaymentOverviewData error: ' . $e->getMessage());

            return [
                'total_records' => 0,
                'received_amount' => 0,
                'invoice_amount' => 0,
                'due_amount' => 0,
                'labels' => [
                    __('messages.received_amount'),
                    __('messages.invoice.due_amount'),
                ],
                'dataPoints' => [0, 0],
            ];
        }
    }

    public function getInvoiceOverviewData(): array
    {
        try {
            // Cache key for performance optimization
            $cacheKey = 'dashboard_invoice_overview_' . auth()->id();

            // Clear cache for real-time data
            \Cache::forget($cacheKey);

            return \Cache::remember($cacheKey, 60, function () { // 1 minute cache for real-time feel
                // Optimized query: Use single query with groupBy instead of multiple queries
                $statusCounts = Invoice::where('status', '!=', Invoice::DRAFT)
                    ->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status')
                    ->toArray();

                $data = [];
                $data['total_paid_invoices'] = $statusCounts[Invoice::PAID] ?? 0;
                $data['total_unpaid_invoices'] = $statusCounts[Invoice::UNPAID] ?? 0;
                $data['total_partially_paid'] = $statusCounts[Invoice::PARTIALLY] ?? 0;
                $data['total_overdue'] = $statusCounts[Invoice::OVERDUE] ?? 0;
                $data['total_processing'] = $statusCounts[Invoice::PROCESSING] ?? 0;

                $data['labels'] = [
                    __('messages.paid_invoices'),
                    __('messages.unpaid_invoices'),
                    __('messages.partially_paid_invoices'),
                    __('messages.overdue_invoices'),
                    __('messages.processing_invoices'),
                ];

                $data['dataPoints'] = [
                    $data['total_paid_invoices'],
                    $data['total_unpaid_invoices'],
                    $data['total_partially_paid'],
                    $data['total_overdue'],
                    $data['total_processing']
                ];

                return $data;
            });
        } catch (\Exception $e) {
            \Log::error('DashboardRepository getInvoiceOverviewData error: ' . $e->getMessage());

            return [
                'total_paid_invoices' => 0,
                'total_unpaid_invoices' => 0,
                'total_partially_paid' => 0,
                'total_overdue' => 0,
                'total_processing' => 0,
                'labels' => [
                    __('messages.paid_invoices'),
                    __('messages.unpaid_invoices'),
                    __('messages.partially_paid_invoices'),
                    __('messages.overdue_invoices'),
                    __('messages.processing_invoices'),
                ],
                'dataPoints' => [0, 0, 0, 0, 0],
            ];
        }
    }

    public function prepareYearlyIncomeChartData($input): array
    {
        $start_date = Carbon::parse($input['start_date'])->format('Y-m-d');
        $end_date = Carbon::parse($input['end_date'])->format('Y-m-d');

        $income = Payment::whereIsApproved(Payment::APPROVED)->whereBetween(
            'payment_date',
            [date($start_date), date($end_date)]
        )
            ->selectRaw('DATE_FORMAT(payment_date,"%b %d") as month,SUM(amount) as total_income')
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month');

        $period = CarbonPeriod::create($start_date, $end_date);
        $labelsData = array_map(function ($datePeriod) {
            return $datePeriod->format('M d');
        }, iterator_to_array($period));

        $incomeOverviewData = array_map(function ($datePeriod) use ($income) {
            $month = $datePeriod->format('M d');

            return $income->has($month) ? $income->get($month)->total_income : 0;
        }, iterator_to_array($period));

        $data['labels'] = $labelsData;
        $data['yearly_income'] = $incomeOverviewData;

        return $data;
    }

    public function getAdminCurrencyData()
    {
        try {
            $user = getLogInUser();

            if ($user && $user->hasRole('client')) {
                $invoice = Invoice::whereClientId($user->client->id);
            } else {
                $invoice = Invoice::query();
            }

            $totalInvoices = $invoice->where('status', '!=', Invoice::DRAFT)
                ->get()
                ->groupBy('currency_id');
            $invoiceIds = $invoice->pluck('id')->toArray();

            $paidInvoices = Payment::with('invoice')
                ->where('is_approved', Payment::APPROVED)
                ->whereIn('invoice_id', $invoiceIds)
                ->get()
                ->groupBy('invoice.currency_id');

            $totalInvoiceAmountArr = [];
            $paidInvoicesArr = [];
            $dueInvoicesArr = [];
            $defaultCurrencyId = getSettingValue('current_currency') ?? 1;

            foreach ($totalInvoices as $currencyId => $totalInvoice) {
                $effectiveCurrencyId = empty($currencyId) ? $defaultCurrencyId : $currencyId;
                $totalInvoiceAmountArr[$effectiveCurrencyId] = ($totalInvoiceAmountArr[$effectiveCurrencyId] ?? 0) + $totalInvoice->sum('final_amount');
            }

            foreach ($paidInvoices as $currencyId => $paidInvoice) {
                $effectiveCurrencyId = empty($currencyId) ? $defaultCurrencyId : $currencyId;
                $paidAmount = $paidInvoice->sum('amount');
                $paidInvoicesArr[$effectiveCurrencyId] = ($paidInvoicesArr[$effectiveCurrencyId] ?? 0) + $paidAmount;
                $dueInvoicesArr[$effectiveCurrencyId] = ($totalInvoiceAmountArr[$effectiveCurrencyId] ?? 0) - ($paidInvoicesArr[$effectiveCurrencyId] ?? 0);
            }

            // Ensure all arrays have the same keys
            $allCurrencyIds = array_unique(array_merge(
                array_keys($totalInvoiceAmountArr),
                array_keys($paidInvoicesArr)
            ));

            foreach ($allCurrencyIds as $currencyId) {
                if (!isset($paidInvoicesArr[$currencyId])) {
                    $paidInvoicesArr[$currencyId] = 0;
                }
                if (!isset($dueInvoicesArr[$currencyId])) {
                    $dueInvoicesArr[$currencyId] = ($totalInvoiceAmountArr[$currencyId] ?? 0) - ($paidInvoicesArr[$currencyId] ?? 0);
                }
            }

            ksort($totalInvoiceAmountArr);
            ksort($paidInvoicesArr);
            ksort($dueInvoicesArr);

            $data['totalInvoices'] = $totalInvoiceAmountArr;
            $data['paidInvoices'] = $paidInvoicesArr;
            $data['dueInvoices'] = $dueInvoicesArr;
            $data['currencyIds'] = $allCurrencyIds;

            $currencyDetails = [];
            foreach ($allCurrencyIds as $currencyId) {
                $currencyDetails[$currencyId] = [
                    'total' => (float) ($totalInvoiceAmountArr[$currencyId] ?? 0),
                    'paid'  => (float) ($paidInvoicesArr[$currencyId] ?? 0),
                    'due'   => (float) ($dueInvoicesArr[$currencyId] ?? 0),
                ];
            }

            $data['currencyDetails'] = $currencyDetails;

            return $data;
        } catch (\Exception $e) {
            \Log::error('DashboardRepository getAdminCurrencyData error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'totalInvoices' => [],
                'paidInvoices' => [],
                'dueInvoices' => [],
                'currencyIds' => [],
                'currencyDetails' => [],
            ];
        }
    }
}
