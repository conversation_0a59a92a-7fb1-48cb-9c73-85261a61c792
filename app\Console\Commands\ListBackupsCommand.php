<?php

namespace App\Console\Commands;

use App\Services\DatabaseBackupService;
use Illuminate\Console\Command;

class ListBackupsCommand extends Command
{
    protected $signature = 'backup:list';
    protected $description = 'List all available backups';

    protected DatabaseBackupService $backupService;

    public function __construct(DatabaseBackupService $backupService)
    {
        parent::__construct();
        $this->backupService = $backupService;
    }

    public function handle(): int
    {
        $this->info('📋 Available Backups');
        $this->newLine();

        $backups = $this->backupService->listBackups();

        if (empty($backups)) {
            $this->warn('No backups found.');
            $this->info('💡 Create your first backup with: php artisan backup:create');
            return self::SUCCESS;
        }

        $tableData = [];
        foreach ($backups as $backup) {
            $tableData[] = [
                $backup['name'],
                $backup['created_at']->format('Y-m-d H:i:s'),
                $this->formatBytes($backup['size']),
                $backup['file'],
            ];
        }

        $this->table([
            'Name',
            'Created At',
            'Size',
            'File',
        ], $tableData);

        $this->newLine();
        $this->info('📁 Backup location: storage/app/backups/');
        $this->info('💡 Download backups via admin panel or copy files directly');

        return self::SUCCESS;
    }

    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
