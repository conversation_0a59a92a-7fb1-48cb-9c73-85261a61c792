<?php
/**
 * 🚨 COMPLETE MYSQL DRIVER ERROR ERADICATION SCRIPT
 * 
 * This script completely resolves the persistent MySQL driver error:
 * "could not find driver (Connection: mysql, SQL: select * from `sessions`...)"
 * 
 * The error occurs during login page access when <PERSON><PERSON> tries to read sessions.
 * 
 * Usage: php ERADICATE_MYSQL_DRIVER_ERROR.php
 */

echo "🚨 COMPLETE MYSQL DRIVER ERROR ERADICATION\n";
echo str_repeat("=", 60) . "\n\n";

echo "🎯 TARGET ERROR: could not find driver (Connection: mysql)\n";
echo "📍 LOCATION: Login page access, session reading\n";
echo "🔍 CAUSE: PDO MySQL driver configuration issue\n\n";

// Step 1: Check current PHP configuration
echo "🔍 STEP 1: DETAILED PHP CONFIGURATION ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

$phpVersion = phpversion();
$phpIniPath = php_ini_loaded_file();
$phpExtDir = ini_get('extension_dir');

echo "   PHP Version: {$phpVersion}\n";
echo "   PHP ini file: {$phpIniPath}\n";
echo "   Extension directory: {$phpExtDir}\n";

// Check all MySQL-related extensions
$mysqlExtensions = [
    'pdo' => 'PDO extension',
    'pdo_mysql' => 'PDO MySQL driver (CRITICAL)',
    'mysqli' => 'MySQLi extension',
    'mysqlnd' => 'MySQL Native Driver'
];

$loadedExtensions = get_loaded_extensions();
echo "\n   Loaded Extensions Analysis:\n";

foreach ($mysqlExtensions as $ext => $description) {
    $isLoaded = extension_loaded($ext);
    $inArray = in_array($ext, $loadedExtensions);
    
    echo "   - {$ext}: ";
    if ($isLoaded && $inArray) {
        echo "✅ LOADED\n";
    } elseif ($isLoaded) {
        echo "⚠️  LOADED (not in array)\n";
    } else {
        echo "❌ NOT LOADED - {$description}\n";
    }
}

// Check PDO drivers specifically
if (class_exists('PDO')) {
    $pdoDrivers = PDO::getAvailableDrivers();
    echo "\n   Available PDO Drivers: " . implode(', ', $pdoDrivers) . "\n";
    
    if (in_array('mysql', $pdoDrivers)) {
        echo "   ✅ MySQL PDO driver: AVAILABLE\n";
    } else {
        echo "   ❌ MySQL PDO driver: NOT AVAILABLE (ROOT CAUSE!)\n";
    }
} else {
    echo "   ❌ PDO class: NOT AVAILABLE\n";
}

// Step 2: Check XAMPP specific configuration
echo "\n🔍 STEP 2: XAMPP CONFIGURATION DEEP DIVE\n";
echo str_repeat("-", 50) . "\n";

if ($phpIniPath && file_exists($phpIniPath)) {
    $phpIniContent = file_get_contents($phpIniPath);
    
    // Check for MySQL extension lines
    $extensionLines = [
        'extension=pdo_mysql' => 'PDO MySQL extension',
        ';extension=pdo_mysql' => 'PDO MySQL extension (COMMENTED)',
        'extension=mysqli' => 'MySQLi extension',
        ';extension=mysqli' => 'MySQLi extension (COMMENTED)',
        'extension=mysqlnd' => 'MySQL Native Driver',
        ';extension=mysqlnd' => 'MySQL Native Driver (COMMENTED)'
    ];
    
    echo "   PHP.ini MySQL Extension Configuration:\n";
    foreach ($extensionLines as $line => $description) {
        if (strpos($phpIniContent, $line) !== false) {
            $status = (strpos($line, ';') === 0) ? "❌ COMMENTED OUT" : "✅ ENABLED";
            echo "   - {$line}: {$status}\n";
        }
    }
    
    // Check extension_dir setting
    if (preg_match('/extension_dir\s*=\s*"([^"]*)"/', $phpIniContent, $matches)) {
        $configExtDir = $matches[1];
        echo "\n   Configured extension_dir: {$configExtDir}\n";
        
        if (is_dir($configExtDir)) {
            echo "   ✅ Extension directory exists\n";
            
            // Check for MySQL extension files
            $mysqlExtFiles = [
                'php_pdo_mysql.dll',
                'php_mysqli.dll',
                'php_mysqlnd.dll'
            ];
            
            foreach ($mysqlExtFiles as $file) {
                $filePath = $configExtDir . DIRECTORY_SEPARATOR . $file;
                if (file_exists($filePath)) {
                    echo "   ✅ {$file}: EXISTS\n";
                } else {
                    echo "   ❌ {$file}: MISSING\n";
                }
            }
        } else {
            echo "   ❌ Extension directory does not exist\n";
        }
    }
} else {
    echo "   ❌ Cannot read php.ini file\n";
}

// Step 3: Test direct database connection
echo "\n🔍 STEP 3: DIRECT DATABASE CONNECTION TEST\n";
echo str_repeat("-", 50) . "\n";

try {
    // Test with different connection methods
    $host = '127.0.0.1';
    $dbname = 'invoicemod';
    $username = 'root';
    $password = '';
    
    echo "   Testing connection to: {$host}/{$dbname}\n";
    
    // Method 1: PDO MySQL
    try {
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        echo "   ✅ PDO MySQL connection: SUCCESS\n";
        
        // Test sessions table specifically
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM sessions");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   ✅ Sessions table access: SUCCESS ({$result['count']} records)\n";
        
    } catch (PDOException $e) {
        echo "   ❌ PDO MySQL connection: FAILED - " . $e->getMessage() . "\n";
        
        if (strpos($e->getMessage(), 'could not find driver') !== false) {
            echo "   🚨 CONFIRMED: This is the exact error causing the issue!\n";
        }
    }
    
    // Method 2: MySQLi (alternative)
    if (extension_loaded('mysqli')) {
        try {
            $mysqli = new mysqli($host, $username, $password, $dbname);
            if ($mysqli->connect_error) {
                echo "   ❌ MySQLi connection: FAILED - " . $mysqli->connect_error . "\n";
            } else {
                echo "   ✅ MySQLi connection: SUCCESS (alternative working)\n";
                $mysqli->close();
            }
        } catch (Exception $e) {
            echo "   ❌ MySQLi connection: FAILED - " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ⚠️  MySQLi extension not loaded\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Database connection test failed: " . $e->getMessage() . "\n";
}

// Step 4: Automatic fixes
echo "\n🔧 STEP 4: AUTOMATIC FIXES\n";
echo str_repeat("-", 50) . "\n";

$fixesApplied = [];

// Fix 1: Enable PDO MySQL in php.ini
if ($phpIniPath && is_writable($phpIniPath)) {
    echo "   📝 Attempting to fix php.ini configuration...\n";
    
    $phpIniContent = file_get_contents($phpIniPath);
    $originalContent = $phpIniContent;
    
    // Uncomment PDO MySQL extension
    if (strpos($phpIniContent, ';extension=pdo_mysql') !== false) {
        $phpIniContent = str_replace(';extension=pdo_mysql', 'extension=pdo_mysql', $phpIniContent);
        $fixesApplied[] = "Enabled pdo_mysql extension";
        echo "   ✅ Enabled: extension=pdo_mysql\n";
    }
    
    // Uncomment MySQLi extension
    if (strpos($phpIniContent, ';extension=mysqli') !== false) {
        $phpIniContent = str_replace(';extension=mysqli', 'extension=mysqli', $phpIniContent);
        $fixesApplied[] = "Enabled mysqli extension";
        echo "   ✅ Enabled: extension=mysqli\n";
    }
    
    // Add extensions if not present
    if (strpos($phpIniContent, 'extension=pdo_mysql') === false) {
        $phpIniContent .= "\n; Added by error fix script\nextension=pdo_mysql\n";
        $fixesApplied[] = "Added pdo_mysql extension";
        echo "   ✅ Added: extension=pdo_mysql\n";
    }
    
    if ($phpIniContent !== $originalContent) {
        file_put_contents($phpIniPath, $phpIniContent);
        echo "   ✅ php.ini updated successfully\n";
        echo "   🔄 RESTART REQUIRED: Please restart XAMPP Apache\n";
    } else {
        echo "   ℹ️  php.ini already correctly configured\n";
    }
} else {
    echo "   ❌ Cannot modify php.ini (not writable or not found)\n";
}

// Fix 2: Alternative session driver
echo "\n   🔄 Implementing alternative session configuration...\n";

try {
    // Check current Laravel configuration
    if (file_exists('.env')) {
        $envContent = file_get_contents('.env');
        
        // Temporarily switch to file sessions as fallback
        if (strpos($envContent, 'SESSION_DRIVER=database') !== false) {
            $newEnvContent = str_replace('SESSION_DRIVER=database', 'SESSION_DRIVER=file', $envContent);
            file_put_contents('.env.backup', $envContent); // Backup original
            file_put_contents('.env', $newEnvContent);
            $fixesApplied[] = "Switched to file sessions as temporary fallback";
            echo "   ✅ Temporarily switched to file sessions\n";
            echo "   📝 Original .env backed up to .env.backup\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ Could not modify session configuration: " . $e->getMessage() . "\n";
}

// Step 5: Clear all Laravel caches
echo "\n🧹 STEP 5: COMPREHENSIVE CACHE CLEARING\n";
echo str_repeat("-", 50) . "\n";

$cacheCommands = [
    'config:clear' => 'Configuration cache',
    'cache:clear' => 'Application cache',
    'route:clear' => 'Route cache',
    'view:clear' => 'View cache'
];

foreach ($cacheCommands as $command => $description) {
    try {
        $output = shell_exec("C:\\xampp\\php\\php.exe artisan {$command} 2>&1");
        if (strpos($output, 'successfully') !== false || strpos($output, 'cleared') !== false) {
            echo "   ✅ {$description}: CLEARED\n";
        } else {
            echo "   ⚠️  {$description}: " . trim($output) . "\n";
        }
    } catch (Exception $e) {
        echo "   ❌ {$description}: FAILED - " . $e->getMessage() . "\n";
    }
}

// Step 6: Manual instructions
echo "\n🛠️ STEP 6: MANUAL INSTRUCTIONS (IF AUTOMATIC FIXES FAILED)\n";
echo str_repeat("-", 50) . "\n";

echo "1. XAMPP Control Panel Method:\n";
echo "   - Stop Apache in XAMPP Control Panel\n";
echo "   - Click 'Config' next to Apache → PHP (php.ini)\n";
echo "   - Find these lines and remove semicolon (;):\n";
echo "     ;extension=pdo_mysql  →  extension=pdo_mysql\n";
echo "     ;extension=mysqli     →  extension=mysqli\n";
echo "   - Save file and restart Apache\n\n";

echo "2. Alternative: Use MySQLi instead of PDO:\n";
echo "   - Edit config/database.php\n";
echo "   - Change 'mysql' connection driver from 'pdo' to 'mysqli'\n\n";

echo "3. Nuclear Option - Reinstall XAMPP:\n";
echo "   - Download latest XAMPP with PHP 8.2\n";
echo "   - Fresh installation with MySQL extensions enabled\n\n";

// Step 7: Verification test
echo "🧪 STEP 7: POST-FIX VERIFICATION\n";
echo str_repeat("-", 50) . "\n";

if (!empty($fixesApplied)) {
    echo "   Fixes applied:\n";
    foreach ($fixesApplied as $fix) {
        echo "   - {$fix}\n";
    }
    
    echo "\n   🔄 RESTART XAMPP APACHE NOW!\n";
    echo "   Then run this script again to verify fixes.\n";
} else {
    echo "   ℹ️  No automatic fixes were applied.\n";
    echo "   Please follow manual instructions above.\n";
}

echo "\n📋 SUMMARY\n";
echo str_repeat("-", 50) . "\n";

if (extension_loaded('pdo') && in_array('mysql', PDO::getAvailableDrivers())) {
    echo "✅ PDO MySQL driver is available - error might be intermittent\n";
    echo "💡 Try restarting XAMPP and clearing Laravel caches\n";
} else {
    echo "❌ PDO MySQL driver is NOT available - this is the root cause\n";
    echo "🔧 Follow the manual instructions above to enable it\n";
}

echo "\n🎯 NEXT STEPS:\n";
echo "1. Restart XAMPP Apache service\n";
echo "2. Run this script again to verify\n";
echo "3. Test login page access: http://127.0.0.1:8000/admin/login\n";
echo "4. If still failing, follow manual instructions\n";

echo "\n✅ MySQL driver error eradication script completed!\n";
