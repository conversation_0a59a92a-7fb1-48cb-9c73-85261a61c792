<?php

namespace App\Filament\Widgets;

use App\Models\Client;
use App\Models\Invoice;
use App\Models\Service;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DashboardOverview extends BaseWidget
{

    protected static string $view = 'filament.widgets.dashboard';

    public static function canView(): bool
    {
        return auth()->user()->hasRole('admin');
    }
    protected function getViewData(): array
    {
        try {
            // Get all invoices excluding drafts for accurate counts
            $invoices = Invoice::where('status', '!=', Invoice::DRAFT)->get();
            $totalInvoices = $invoices->count();
            $totalClients = Client::count();
            $totalProducts = Service::count();

            // Use collection methods for better performance and accuracy
            $paidInvoices = $invoices->where('status', Invoice::PAID)->count();
            $unpaidInvoices = $invoices->where('status', Invoice::UNPAID)->count();
            $partiallyPaidInvoices = $invoices->where('status', Invoice::PARTIALLY)->count();
            $overdueInvoices = $invoices->where('status', Invoice::OVERDUE)->count();
            $processingInvoices = $invoices->where('status', Invoice::PROCESSING)->count();

            return [
                'totalInvoices' => formatTotalAmount($totalInvoices),
                'totalClients' => formatTotalAmount($totalClients),
                'totalProducts' => formatTotalAmount($totalProducts),
                'paidInvoices' => formatTotalAmount($paidInvoices),
                'unpaidInvoices' => formatTotalAmount($unpaidInvoices),
                'partiallyPaidInvoices' => formatTotalAmount($partiallyPaidInvoices),
                'overdueInvoices' => formatTotalAmount($overdueInvoices),
                'processingInvoices' => formatTotalAmount($processingInvoices),
            ];
        } catch (\Exception $e) {
            \Log::error('DashboardOverview widget error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'totalInvoices' => formatTotalAmount(0),
                'totalClients' => formatTotalAmount(0),
                'totalProducts' => formatTotalAmount(0),
                'paidInvoices' => formatTotalAmount(0),
                'unpaidInvoices' => formatTotalAmount(0),
                'partiallyPaidInvoices' => formatTotalAmount(0),
                'overdueInvoices' => formatTotalAmount(0),
                'processingInvoices' => formatTotalAmount(0),
            ];
        }
    }
}
