<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;

class ReportsExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $data;
    protected array $headings;
    protected string $title;

    public function __construct(array $data, array $headings, string $title = 'Report')
    {
        $this->data = $data;
        $this->headings = $headings;
        $this->title = $title;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return $this->headings;
    }

    public function title(): string
    {
        return $this->title;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as header
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4F46E5'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}

class FinancialReportExport extends ReportsExport
{
    public function __construct(array $financialData, string $startDate, string $endDate)
    {
        $data = [
            ['Total Invoices', $financialData['total_invoices']],
            ['Total Invoice Amount', number_format($financialData['total_invoice_amount'], 2)],
            ['Total Payments Received', number_format($financialData['total_payments_received'], 2)],
            ['Outstanding Amount', number_format($financialData['outstanding_amount'], 2)],
            ['Paid Invoices', $financialData['paid_invoices']],
            ['Unpaid Invoices', $financialData['unpaid_invoices']],
            ['Partially Paid Invoices', $financialData['partially_paid_invoices']],
            ['Average Invoice Value', number_format($financialData['average_invoice_value'], 2)],
            ['Payment Success Rate', number_format($financialData['payment_success_rate'], 2) . '%'],
        ];

        $headings = ['Metric', 'Value'];
        $title = "Financial Report ({$startDate} to {$endDate})";

        parent::__construct($data, $headings, $title);
    }
}

class ClientPerformanceExport extends ReportsExport
{
    public function __construct(array $clientData, string $startDate, string $endDate)
    {
        $data = [];
        foreach ($clientData as $client) {
            $data[] = [
                $client['client_name'],
                $client['client_email'],
                $client['total_invoices'],
                number_format($client['total_invoice_amount'], 2),
                number_format($client['total_payments'], 2),
                number_format($client['outstanding_amount'], 2),
                number_format($client['payment_ratio'], 2) . '%',
                number_format($client['avg_invoice_value'], 2),
                $client['last_invoice_date'] ?? 'N/A',
                $client['last_payment_date'] ?? 'N/A',
            ];
        }

        $headings = [
            'Client Name',
            'Email',
            'Total Invoices',
            'Total Invoice Amount',
            'Total Payments',
            'Outstanding Amount',
            'Payment Ratio (%)',
            'Average Invoice Value',
            'Last Invoice Date',
            'Last Payment Date',
        ];

        $title = "Client Performance Report ({$startDate} to {$endDate})";

        parent::__construct($data, $headings, $title);
    }
}

class ProductAnalyticsExport extends ReportsExport
{
    public function __construct(array $productData, string $startDate, string $endDate)
    {
        $data = [];
        foreach ($productData as $product) {
            $data[] = [
                $product['product_name'],
                $product['type'] === 'product' ? 'Database Product' : 'Free-form Entry',
                $product['product_code'] ?? 'N/A',
                $product['total_quantity'],
                number_format($product['total_amount'], 2),
                $product['invoice_count'],
                number_format($product['avg_price'], 2),
                number_format($product['avg_quantity'], 2),
            ];
        }

        $headings = [
            'Product/Service Name',
            'Type',
            'Product Code',
            'Total Quantity',
            'Total Amount',
            'Invoice Count',
            'Average Price',
            'Average Quantity',
        ];

        $title = "Product Analytics Report ({$startDate} to {$endDate})";

        parent::__construct($data, $headings, $title);
    }
}

class InvoiceAnalyticsExport extends ReportsExport
{
    public function __construct(array $analyticsData, string $startDate, string $endDate)
    {
        $data = [];
        
        // Status Distribution
        if (isset($analyticsData['status_distribution'])) {
            $data[] = ['INVOICE STATUS DISTRIBUTION', '', '', ''];
            $data[] = ['Status', 'Count', 'Total Amount', 'Percentage'];
            foreach ($analyticsData['status_distribution'] as $status) {
                $data[] = [
                    $status['status'],
                    $status['count'],
                    number_format($status['total_amount'], 2),
                    number_format($status['percentage'], 2) . '%',
                ];
            }
            $data[] = ['', '', '', '']; // Empty row
        }

        // Overdue Analysis
        if (isset($analyticsData['overdue_analysis'])) {
            $overdue = $analyticsData['overdue_analysis'];
            $data[] = ['OVERDUE ANALYSIS', '', '', ''];
            $data[] = ['Total Overdue Invoices', $overdue['total_overdue'], '', ''];
            $data[] = ['Total Overdue Amount', number_format($overdue['total_overdue_amount'], 2), '', ''];
            $data[] = ['', '', '', '']; // Empty row
            
            $data[] = ['Days Overdue Range', 'Count', 'Amount', ''];
            foreach ($overdue['ranges'] as $range => $details) {
                $data[] = [
                    $range . ' days',
                    $details['count'],
                    number_format($details['amount'], 2),
                    '',
                ];
            }
        }

        $headings = ['Metric', 'Value', 'Additional', 'Notes'];
        $title = "Invoice Analytics Report ({$startDate} to {$endDate})";

        parent::__construct($data, $headings, $title);
    }
}

class OverdueInvoicesExport extends ReportsExport
{
    public function __construct(array $overdueData)
    {
        $data = [];
        foreach ($overdueData as $invoice) {
            $data[] = [
                $invoice['invoice_id'],
                $invoice['client_name'],
                $invoice['client_email'],
                $invoice['invoice_date'],
                $invoice['due_date'],
                number_format($invoice['total_amount'], 2),
                number_format($invoice['paid_amount'], 2),
                number_format($invoice['outstanding_amount'], 2),
                $invoice['days_overdue'],
                ucfirst($invoice['status']),
            ];
        }

        $headings = [
            'Invoice ID',
            'Client Name',
            'Client Email',
            'Invoice Date',
            'Due Date',
            'Total Amount',
            'Paid Amount',
            'Outstanding Amount',
            'Days Overdue',
            'Status',
        ];

        $title = 'Overdue Invoices Report';

        parent::__construct($data, $headings, $title);
    }
}

class TaxSummaryExport extends ReportsExport
{
    public function __construct(array $taxData, string $startDate, string $endDate)
    {
        $data = [];
        
        // Summary
        $data[] = ['Total Tax Amount', number_format($taxData['total_tax_amount'], 2)];
        $data[] = ['Total Invoices with Tax', $taxData['total_invoices_with_tax']];
        $data[] = ['', '']; // Empty row
        
        // Tax Breakdown
        $data[] = ['TAX BREAKDOWN', ''];
        $data[] = ['Tax Name', 'Rate (%)', 'Total Amount', 'Invoice Count'];
        
        foreach ($taxData['tax_breakdown'] as $tax) {
            $data[] = [
                $tax['tax_name'],
                $tax['tax_rate'],
                number_format($tax['total_amount'], 2),
                $tax['invoice_count'],
            ];
        }

        $headings = ['Description', 'Value', 'Amount', 'Count'];
        $title = "Tax Summary Report ({$startDate} to {$endDate})";

        parent::__construct($data, $headings, $title);
    }
}
