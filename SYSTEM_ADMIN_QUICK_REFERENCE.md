# 🔥 SYSTEM ADMIN QUICK REFERENCE GUIDE 🔥

## Emergency Commands

### System Health Check
```bash
# Quick health check
php artisan system:health-check

# Detailed health check with notifications
php artisan system:health-check --detailed --notify

# Health check with automatic fixes
php artisan system:health-check --fix
```

### Cache Management
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear

# Optimize for production
php artisan optimize
```

## Troubleshooting Guide

### Export Issues
**Problem**: Export functionality not working
**Solution**:
1. Check if export classes exist in `app/Exports/`
2. Verify view files exist in `resources/views/reports/`
3. Check logs: `storage/logs/laravel.log`
4. Run: `php artisan system:health-check`

### PDF Generation Issues
**Problem**: PDF generation failing
**Solution**:
1. Check invoice template exists
2. Verify DomPDF package installed
3. Check file permissions on storage directory
4. Review error logs for specific issues

### Date Validation Errors
**Problem**: Date validation failing in reports
**Solution**:
1. Ensure dates are in correct format (Y-m-d)
2. Check start date is before end date
3. Verify dates are not in the future
4. Check validation rules in Filament pages

### Database Performance Issues
**Problem**: Slow query performance
**Solution**:
1. Run: `php artisan optimize:clear`
2. Check database indexes
3. Review slow query log
4. Consider query optimization

## File Locations

### Critical System Files
```
app/Services/
├── ReportingService.php          # Main reporting logic
├── ExportService.php             # Export functionality
└── SystemHealthService.php       # Health monitoring

app/Exports/
├── FinancialReportExport.php     # Financial reports
├── ClientPerformanceExport.php   # Client analytics
├── ProductAnalyticsExport.php    # Product reports
├── InvoiceAnalyticsExport.php    # Invoice analytics
├── OverdueInvoicesExport.php     # Overdue tracking
└── TaxSummaryExport.php          # Tax reports

resources/views/reports/
├── financial-report-pdf.blade.php
├── client-performance-pdf.blade.php
├── invoice-analytics-pdf.blade.php
└── product-analytics-pdf.blade.php

app/Filament/Pages/
├── FinancialReports.php          # Financial reporting page
├── ClientReports.php             # Client reporting page
├── InvoiceAnalytics.php          # Invoice analytics page
└── ProductAnalytics.php          # Product analytics page
```

### Configuration Files
```
config/
├── app.php                       # Application config
├── database.php                  # Database config
├── filesystems.php               # File storage config
└── mail.php                      # Email config
```

## Monitoring & Alerts

### Key Metrics to Monitor
- Export success rate (should be >95%)
- PDF generation success rate (should be >98%)
- Database response time (should be <500ms)
- Error rate (should be <1%)
- System uptime (should be >99.9%)

### Log Files to Watch
```
storage/logs/
├── laravel.log                   # Application logs
├── export-errors.log             # Export-specific errors
└── system-health.log             # Health check results
```

### Performance Thresholds
- Dashboard load time: <2 seconds
- Report generation: <10 seconds
- Export operations: <30 seconds
- PDF generation: <15 seconds

## Backup & Recovery

### Critical Data to Backup
- Database (invoices, clients, payments)
- File uploads (logos, attachments)
- Configuration files
- Custom templates

### Backup Commands
```bash
# Database backup
mysqldump -u username -p database_name > backup.sql

# File backup
tar -czf files_backup.tar.gz storage/app/public/

# Full application backup
tar -czf full_backup.tar.gz --exclude=node_modules --exclude=vendor .
```

## Security Checklist

### Daily Checks
- [ ] Review error logs for suspicious activity
- [ ] Check failed login attempts
- [ ] Verify backup completion
- [ ] Monitor system resource usage

### Weekly Checks
- [ ] Update dependencies if needed
- [ ] Review user access permissions
- [ ] Check SSL certificate status
- [ ] Analyze performance metrics

### Monthly Checks
- [ ] Full security audit
- [ ] Database optimization
- [ ] Clean up old log files
- [ ] Review and update documentation

## Common Issues & Solutions

### Issue: "Class not found" errors
**Solution**: Run `composer dump-autoload`

### Issue: Permission denied errors
**Solution**: 
```bash
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### Issue: Memory limit exceeded
**Solution**: Increase PHP memory limit in `php.ini`
```ini
memory_limit = 512M
```

### Issue: Export timeout
**Solution**: Increase execution time
```ini
max_execution_time = 300
```

## Contact Information

### Support Escalation
1. **Level 1**: Check logs and run health check
2. **Level 2**: Review this guide and apply solutions
3. **Level 3**: Contact development team with:
   - Error logs
   - Health check results
   - Steps to reproduce issue
   - System environment details

### Emergency Contacts
- System Administrator: [Your Contact]
- Development Team: [Dev Team Contact]
- Database Administrator: [DBA Contact]

---

*Keep this guide handy for quick reference during system administration tasks.*
