<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SystemHealthService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

/**
 * 🔥 SYSTEM HEALTH CHECK COMMAND - BEAST MODE MONITORING 🔥
 * 
 * Automated system health monitoring command
 */
class SystemHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'system:health-check 
                            {--notify : Send notification if issues are found}
                            {--detailed : Show detailed output}
                            {--fix : Attempt to fix minor issues automatically}';

    /**
     * The console command description.
     */
    protected $description = 'Perform comprehensive system health check and monitoring';

    protected SystemHealthService $healthService;

    public function __construct(SystemHealthService $healthService)
    {
        parent::__construct();
        $this->healthService = $healthService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔥 STARTING BEAST MODE SYSTEM HEALTH CHECK 🔥');
        $this->newLine();

        try {
            // Perform comprehensive health check
            $healthReport = $this->healthService->performHealthCheck();
            
            // Display results
            $this->displayHealthReport($healthReport);
            
            // Handle notifications
            if ($this->option('notify') && $healthReport['overall_status'] !== 'healthy') {
                $this->sendHealthNotification($healthReport);
            }
            
            // Attempt fixes if requested
            if ($this->option('fix')) {
                $this->attemptAutomaticFixes($healthReport);
            }
            
            // Log the health check
            Log::info('System health check completed', [
                'status' => $healthReport['overall_status'],
                'timestamp' => $healthReport['timestamp'],
            ]);
            
            return $healthReport['overall_status'] === 'healthy' ? 0 : 1;
            
        } catch (\Exception $e) {
            $this->error('Health check failed: ' . $e->getMessage());
            Log::error('System health check failed: ' . $e->getMessage());
            return 2;
        }
    }

    /**
     * Display health report in console
     */
    private function displayHealthReport(array $report): void
    {
        // Overall status
        $statusColor = match($report['overall_status']) {
            'healthy' => 'green',
            'warning' => 'yellow',
            'critical' => 'red',
            default => 'white'
        };
        
        $this->line("Overall Status: <fg={$statusColor}>" . strtoupper($report['overall_status']) . "</>");
        $this->line("Timestamp: " . $report['timestamp']);
        $this->newLine();

        // Individual checks
        foreach ($report['checks'] as $checkName => $checkResult) {
            $this->displayCheckResult($checkName, $checkResult);
        }
    }

    /**
     * Display individual check result
     */
    private function displayCheckResult(string $checkName, array $result): void
    {
        $statusColor = match($result['status']) {
            'healthy' => 'green',
            'warning' => 'yellow',
            'unhealthy' => 'red',
            default => 'white'
        };

        $this->line("<fg=cyan>" . ucwords(str_replace('_', ' ', $checkName)) . ":</> <fg={$statusColor}>" . strtoupper($result['status']) . "</>");

        if ($this->option('detailed')) {
            foreach ($result as $key => $value) {
                if ($key === 'status') continue;
                
                if (is_array($value)) {
                    $this->line("  {$key}:");
                    foreach ($value as $subKey => $subValue) {
                        $this->line("    {$subKey}: " . (is_bool($subValue) ? ($subValue ? 'Yes' : 'No') : $subValue));
                    }
                } else {
                    $this->line("  {$key}: {$value}");
                }
            }
        }
        
        $this->newLine();
    }

    /**
     * Send health notification
     */
    private function sendHealthNotification(array $report): void
    {
        try {
            $this->info('Sending health notification...');
            
            // In a real implementation, you would send email/slack notifications
            // For now, we'll just log it
            Log::warning('System health notification', [
                'status' => $report['overall_status'],
                'checks' => array_map(fn($check) => $check['status'], $report['checks']),
                'timestamp' => $report['timestamp'],
            ]);
            
            $this->info('Health notification sent successfully');
            
        } catch (\Exception $e) {
            $this->error('Failed to send health notification: ' . $e->getMessage());
        }
    }

    /**
     * Attempt automatic fixes for minor issues
     */
    private function attemptAutomaticFixes(array $report): void
    {
        $this->info('Attempting automatic fixes...');
        
        $fixesApplied = 0;
        
        // Fix 1: Clear expired cache entries
        try {
            \Illuminate\Support\Facades\Cache::flush();
            $this->line('✓ Cleared system cache');
            $fixesApplied++;
        } catch (\Exception $e) {
            $this->line('✗ Failed to clear cache: ' . $e->getMessage());
        }
        
        // Fix 2: Optimize database tables (if needed)
        if (isset($report['checks']['performance']['status']) && $report['checks']['performance']['status'] !== 'healthy') {
            try {
                \Illuminate\Support\Facades\DB::statement('OPTIMIZE TABLE invoices, payments, clients');
                $this->line('✓ Optimized database tables');
                $fixesApplied++;
            } catch (\Exception $e) {
                $this->line('✗ Failed to optimize database: ' . $e->getMessage());
            }
        }
        
        // Fix 3: Clean up temporary files
        try {
            $tempPath = storage_path('app/temp');
            if (is_dir($tempPath)) {
                $files = glob($tempPath . '/*');
                foreach ($files as $file) {
                    if (is_file($file) && filemtime($file) < time() - 3600) { // 1 hour old
                        unlink($file);
                    }
                }
                $this->line('✓ Cleaned up temporary files');
                $fixesApplied++;
            }
        } catch (\Exception $e) {
            $this->line('✗ Failed to clean temporary files: ' . $e->getMessage());
        }
        
        if ($fixesApplied > 0) {
            $this->info("Applied {$fixesApplied} automatic fixes");
        } else {
            $this->line('No automatic fixes were applied');
        }
    }
}
