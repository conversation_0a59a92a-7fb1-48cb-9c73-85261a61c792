<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Product Analytics Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 20px;
            color: #666;
            margin-bottom: 10px;
        }
        .date-range {
            font-size: 14px;
            color: #888;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .metric-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            text-align: center;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 12px;
        }
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        .table th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        .table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $company_name ?? 'DCF - Digital Clearing and Forwarding Agency' }}</div>
        <div class="report-title">{{ $report_title ?? 'Product Analytics Report' }}</div>
        <div class="date-range">Period: {{ $start_date }} to {{ $end_date }}</div>
    </div>

    @if(isset($summary_stats))
    <div class="section">
        <div class="section-title">Summary Statistics</div>
        <div class="summary-grid">
            <div class="metric-card">
                <div class="metric-label">Total Products</div>
                <div class="metric-value">{{ $summary_stats['total_products'] ?? 0 }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Revenue</div>
                <div class="metric-value">${{ number_format($summary_stats['total_revenue'] ?? 0, 2) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Quantity</div>
                <div class="metric-value">{{ number_format($summary_stats['total_quantity'] ?? 0) }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Avg Price</div>
                <div class="metric-value">${{ number_format($summary_stats['avg_price'] ?? 0, 2) }}</div>
            </div>
        </div>
    </div>
    @endif

    @if(isset($products) && count($products) > 0)
    <div class="section">
        <div class="section-title">Product Performance Details</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th>Product Code</th>
                    <th>Total Quantity</th>
                    <th>Total Revenue</th>
                    <th>Average Price</th>
                    <th>Invoice Count</th>
                    <th>Revenue %</th>
                </tr>
            </thead>
            <tbody>
                @foreach($products as $product)
                <tr>
                    <td>{{ $product['product_name'] ?? 'Unknown Product' }}</td>
                    <td>{{ $product['product_code'] ?? 'N/A' }}</td>
                    <td>{{ number_format($product['total_quantity'] ?? 0) }}</td>
                    <td>${{ number_format($product['total_amount'] ?? 0, 2) }}</td>
                    <td>${{ number_format($product['avg_price'] ?? 0, 2) }}</td>
                    <td>{{ $product['total_invoices'] ?? 0 }}</td>
                    <td>{{ number_format($product['revenue_percentage'] ?? 0, 2) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($type_comparison) && count($type_comparison) > 0)
    <div class="section">
        <div class="section-title">Product Type Comparison</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Count</th>
                    <th>Total Revenue</th>
                    <th>Average Revenue</th>
                    <th>Percentage</th>
                </tr>
            </thead>
            <tbody>
                @foreach($type_comparison as $type)
                <tr>
                    <td>{{ ucfirst($type['type']) }}</td>
                    <td>{{ $type['count'] }}</td>
                    <td>${{ number_format($type['total_revenue'], 2) }}</td>
                    <td>${{ number_format($type['avg_revenue'], 2) }}</td>
                    <td>{{ number_format($type['percentage'], 2) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(isset($revenue_distribution) && count($revenue_distribution) > 0)
    <div class="section">
        <div class="section-title">Revenue Distribution</div>
        <table class="table">
            <thead>
                <tr>
                    <th>Revenue Range</th>
                    <th>Product Count</th>
                    <th>Total Revenue</th>
                    <th>Percentage</th>
                </tr>
            </thead>
            <tbody>
                @foreach($revenue_distribution as $range)
                <tr>
                    <td>{{ $range['range'] }}</td>
                    <td>{{ $range['count'] }}</td>
                    <td>${{ number_format($range['total_revenue'], 2) }}</td>
                    <td>{{ number_format($range['percentage'], 2) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <div class="footer">
        <p>Generated on {{ $generated_at }}</p>
        <p>This report is confidential and intended for internal use only.</p>
    </div>
</body>
</html>
