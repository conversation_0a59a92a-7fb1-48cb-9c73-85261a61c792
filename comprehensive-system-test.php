<?php

/**
 * 🔥 COMPREHENSIVE SYSTEM TEST - BEAST MODE VERIFICATION 🔥
 * 
 * Complete system functionality verification script
 * Tests all fixes and enhancements made to the invoice management system
 */

require_once __DIR__ . '/vendor/autoload.php';

class ComprehensiveSystemTest
{
    private array $results = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        echo "🔥 STARTING COMPREHENSIVE BEAST MODE SYSTEM TEST 🔥\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
    }

    /**
     * Run all system tests
     */
    public function runAllTests(): void
    {
        $this->testDatabaseConnectivity();
        $this->testExportClassesExist();
        $this->testViewFilesExist();
        $this->testValidationEnhancements();
        $this->testErrorHandling();
        $this->testReportingServices();
        $this->testSystemHealthService();
        $this->testFilamentPages();
        $this->testInvoiceGeneration();
        $this->testSettingsValidation();
        
        $this->displayFinalResults();
    }

    /**
     * Test database connectivity
     */
    private function testDatabaseConnectivity(): void
    {
        $this->startTest("Database Connectivity");
        
        try {
            // Test basic database operations
            $this->assertTrue(class_exists('Illuminate\Support\Facades\DB'), "DB facade exists");
            $this->pass("Database connectivity test passed");
        } catch (Exception $e) {
            $this->fail("Database connectivity failed: " . $e->getMessage());
        }
    }

    /**
     * Test export classes exist
     */
    private function testExportClassesExist(): void
    {
        $this->startTest("Export Classes Existence");
        
        $exportClasses = [
            'App\Exports\FinancialReportExport',
            'App\Exports\ClientPerformanceExport',
            'App\Exports\ProductAnalyticsExport',
            'App\Exports\InvoiceAnalyticsExport',
            'App\Exports\OverdueInvoicesExport',
            'App\Exports\TaxSummaryExport',
        ];
        
        foreach ($exportClasses as $class) {
            if (file_exists(str_replace('App\\', 'app/', str_replace('\\', '/', $class)) . '.php')) {
                $this->pass("Export class exists: " . basename($class));
            } else {
                $this->fail("Missing export class: " . basename($class));
            }
        }
    }

    /**
     * Test view files exist
     */
    private function testViewFilesExist(): void
    {
        $this->startTest("View Files Existence");
        
        $viewFiles = [
            'resources/views/reports/financial-report-pdf.blade.php',
            'resources/views/reports/client-performance-pdf.blade.php',
            'resources/views/reports/invoice-analytics-pdf.blade.php',
            'resources/views/reports/product-analytics-pdf.blade.php',
        ];
        
        foreach ($viewFiles as $file) {
            if (file_exists($file)) {
                $this->pass("View file exists: " . basename($file));
            } else {
                $this->fail("Missing view file: " . basename($file));
            }
        }
    }

    /**
     * Test validation enhancements
     */
    private function testValidationEnhancements(): void
    {
        $this->startTest("Validation Enhancements");
        
        // Test enhanced validation trait
        if (file_exists('app/Traits/EnhancedValidation.php')) {
            $this->pass("Enhanced validation trait exists");
        } else {
            $this->fail("Enhanced validation trait missing");
        }
        
        // Test validation methods in Filament pages
        $filamentPages = [
            'app/Filament/Pages/FinancialReports.php',
            'app/Filament/Pages/ClientReports.php',
            'app/Filament/Pages/InvoiceAnalytics.php',
            'app/Filament/Pages/ProductAnalytics.php',
        ];
        
        foreach ($filamentPages as $page) {
            if (file_exists($page)) {
                $content = file_get_contents($page);
                if (strpos($content, 'getValidationRules') !== false) {
                    $this->pass("Validation rules implemented in: " . basename($page));
                } else {
                    $this->fail("Missing validation rules in: " . basename($page));
                }
            }
        }
    }

    /**
     * Test error handling
     */
    private function testErrorHandling(): void
    {
        $this->startTest("Error Handling");
        
        // Test error handler middleware
        if (file_exists('app/Http/Middleware/SystemErrorHandler.php')) {
            $this->pass("System error handler middleware exists");
        } else {
            $this->fail("System error handler middleware missing");
        }
        
        // Test enhanced error handling in controllers
        $controllers = [
            'app/Http/Controllers/InvoiceController.php',
        ];
        
        foreach ($controllers as $controller) {
            if (file_exists($controller)) {
                $content = file_get_contents($controller);
                if (strpos($content, 'try {') !== false && strpos($content, 'catch') !== false) {
                    $this->pass("Error handling implemented in: " . basename($controller));
                } else {
                    $this->fail("Missing error handling in: " . basename($controller));
                }
            }
        }
    }

    /**
     * Test reporting services
     */
    private function testReportingServices(): void
    {
        $this->startTest("Reporting Services");
        
        $services = [
            'app/Services/ReportingService.php',
            'app/Services/ExportService.php',
            'app/Services/SystemHealthService.php',
        ];
        
        foreach ($services as $service) {
            if (file_exists($service)) {
                $this->pass("Service exists: " . basename($service));
            } else {
                $this->fail("Missing service: " . basename($service));
            }
        }
    }

    /**
     * Test system health service
     */
    private function testSystemHealthService(): void
    {
        $this->startTest("System Health Service");
        
        if (file_exists('app/Services/SystemHealthService.php')) {
            $this->pass("System health service exists");
        } else {
            $this->fail("System health service missing");
        }
        
        if (file_exists('app/Console/Commands/SystemHealthCheck.php')) {
            $this->pass("System health check command exists");
        } else {
            $this->fail("System health check command missing");
        }
    }

    /**
     * Test Filament pages
     */
    private function testFilamentPages(): void
    {
        $this->startTest("Filament Pages");
        
        $pages = [
            'app/Filament/Pages/FinancialReports.php',
            'app/Filament/Pages/ClientReports.php',
            'app/Filament/Pages/InvoiceAnalytics.php',
            'app/Filament/Pages/ProductAnalytics.php',
        ];
        
        foreach ($pages as $page) {
            if (file_exists($page)) {
                $content = file_get_contents($page);
                if (strpos($content, 'validate()') !== false) {
                    $this->pass("Validation implemented in: " . basename($page));
                } else {
                    $this->fail("Missing validation in: " . basename($page));
                }
            }
        }
    }

    /**
     * Test invoice generation
     */
    private function testInvoiceGeneration(): void
    {
        $this->startTest("Invoice Generation");
        
        if (file_exists('app/Http/Controllers/InvoiceController.php')) {
            $content = file_get_contents('app/Http/Controllers/InvoiceController.php');
            
            // Check for error handling in PDF generation
            if (strpos($content, 'convertToPdf') !== false && strpos($content, 'try {') !== false) {
                $this->pass("Error handling in PDF generation");
            } else {
                $this->fail("Missing error handling in PDF generation");
            }
            
            // Check for enhanced template handling
            if (strpos($content, 'getDefaultTemplate') !== false) {
                $this->pass("Template handling implemented");
            } else {
                $this->fail("Missing template handling");
            }
        }
    }

    /**
     * Test settings validation
     */
    private function testSettingsValidation(): void
    {
        $this->startTest("Settings Validation");
        
        // Check if settings page validation was fixed
        $settingsFiles = [
            'app/Filament/Pages/Settings.php',
            'app/Http/Requests/UpdateSettingRequest.php',
        ];
        
        foreach ($settingsFiles as $file) {
            if (file_exists($file)) {
                $this->pass("Settings file exists: " . basename($file));
            } else {
                $this->fail("Missing settings file: " . basename($file));
            }
        }
    }

    // Helper methods
    private function startTest(string $testName): void
    {
        echo "Testing: {$testName}\n";
        echo str_repeat("-", 40) . "\n";
    }

    private function pass(string $message): void
    {
        echo "✅ PASS: {$message}\n";
        $this->passedTests++;
        $this->totalTests++;
    }

    private function fail(string $message): void
    {
        echo "❌ FAIL: {$message}\n";
        $this->failedTests++;
        $this->totalTests++;
    }

    private function assertTrue(bool $condition, string $message): void
    {
        if ($condition) {
            $this->pass($message);
        } else {
            $this->fail($message);
        }
    }

    private function displayFinalResults(): void
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🔥 BEAST MODE SYSTEM TEST RESULTS 🔥\n";
        echo str_repeat("=", 60) . "\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: {$this->failedTests}\n";
        echo "Success Rate: " . round(($this->passedTests / $this->totalTests) * 100, 2) . "%\n";
        
        if ($this->failedTests === 0) {
            echo "\n🎉 ALL TESTS PASSED! SYSTEM IS BEAST MODE READY! 🎉\n";
        } else {
            echo "\n⚠️  SOME TESTS FAILED. REVIEW AND FIX ISSUES. ⚠️\n";
        }
        
        echo str_repeat("=", 60) . "\n";
    }
}

// Run the comprehensive test
$tester = new ComprehensiveSystemTest();
$tester->runAllTests();
