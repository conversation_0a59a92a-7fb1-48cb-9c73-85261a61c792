<?php

namespace App\Services;

use App\Models\User;
use App\Models\Service;
use App\Models\Tax;
use App\Models\Client;
use Illuminate\Support\Facades\Cache;

class InvoicePerformanceService
{
    /**
     * Cache duration in seconds (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Get cached client options for dropdown
     */
    public static function getCachedClientOptions(int $limit = 100): array
    {
        return Cache::remember('invoice_client_options', self::CACHE_DURATION, function () use ($limit) {
            return User::whereHas('client')
                ->with('client:id,user_id,company_name')
                ->select('id', 'first_name', 'last_name', 'email')
                ->limit($limit)
                ->get()
                ->pluck('full_name', 'id')
                ->toArray();
        });
    }

    /**
     * Search clients efficiently
     */
    public static function searchClients(string $search, int $limit = 50): array
    {
        $cacheKey = 'client_search_' . md5($search);
        
        return Cache::remember($cacheKey, 60, function () use ($search, $limit) {
            return User::whereHas('client', function ($query) use ($search) {
                    $query->where('company_name', 'like', "%{$search}%");
                })
                ->orWhere('first_name', 'like', "%{$search}%")
                ->orWhere('last_name', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%")
                ->with('client:id,user_id,company_name')
                ->select('id', 'first_name', 'last_name', 'email')
                ->limit($limit)
                ->get()
                ->pluck('full_name', 'id')
                ->toArray();
        });
    }

    /**
     * Get cached service options
     */
    public static function getCachedServiceOptions(int $limit = 20): array
    {
        return Cache::remember('invoice_service_options', self::CACHE_DURATION, function () use ($limit) {
            return Service::select('name')
                ->orderBy('name')
                ->limit($limit)
                ->pluck('name', 'name')
                ->toArray();
        });
    }

    /**
     * Search services efficiently
     */
    public static function searchServices(string $search, int $limit = 50): array
    {
        $cacheKey = 'service_search_' . md5($search);

        return Cache::remember($cacheKey, 60, function () use ($search, $limit) {
            return Service::where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%")
                ->select('name')
                ->limit($limit)
                ->pluck('name', 'name')
                ->toArray();
        });
    }

    /**
     * Get cached tax options
     */
    public static function getCachedTaxOptions(): array
    {
        return Cache::remember('invoice_tax_options', self::CACHE_DURATION, function () {
            return Tax::select('id', 'name')
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();
        });
    }

    /**
     * Get service details efficiently
     */
    public static function getServiceDetails(int $serviceId): ?array
    {
        $cacheKey = 'service_details_' . $serviceId;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($serviceId) {
            $service = Service::select('id', 'name', 'unit_price', 'description')
                ->find($serviceId);

            return $service ? $service->toArray() : null;
        });
    }

    /**
     * Clear all invoice-related caches
     */
    public static function clearCaches(): void
    {
        $patterns = [
            'invoice_client_options',
            'invoice_service_options',
            'invoice_tax_options',
            'client_search_*',
            'service_search_*',
            'service_details_*'
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // For wildcard patterns, we'd need to implement cache tag clearing
                // For now, just clear the main caches
                continue;
            }
            Cache::forget($pattern);
        }
    }

    /**
     * Warm up caches with frequently accessed data
     */
    public static function warmUpCaches(): void
    {
        // Pre-load frequently accessed data
        self::getCachedClientOptions();
        self::getCachedServiceOptions();
        self::getCachedTaxOptions();
    }
}
