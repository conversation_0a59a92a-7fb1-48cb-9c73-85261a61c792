<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add general invoice description field (US-006)
            $table->text('description')->nullable()->after('term')
                ->comment('General invoice description header field for B2B transactions');

            // Add comprehensive shipment details fields (US-007)
            $table->decimal('shipment_weight', 10, 2)->nullable()->after('description')
                ->comment('Shipment weight');
            $table->string('shipment_weight_unit', 10)->default('kg')->after('shipment_weight')
                ->comment('Weight unit (kg, lbs, etc.)');
            $table->string('transportation_type', 100)->nullable()->after('shipment_weight_unit')
                ->comment('Transportation type (air, sea, land, etc.)');
            $table->string('origin_location', 255)->nullable()->after('transportation_type')
                ->comment('Shipment origin location');
            $table->string('destination_location', 255)->nullable()->after('origin_location')
                ->comment('Shipment destination location');
            $table->integer('package_quantity')->nullable()->after('destination_location')
                ->comment('Number of packages');
            $table->string('tracking_number', 100)->nullable()->after('package_quantity')
                ->comment('Shipment tracking number');

            // Add indexes for searchability
            $table->index(['tracking_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['tracking_number']);

            // Drop columns
            $table->dropColumn([
                'description',
                'shipment_weight',
                'shipment_weight_unit',
                'transportation_type',
                'origin_location',
                'destination_location',
                'package_quantity',
                'tracking_number'
            ]);
        });
    }
};
