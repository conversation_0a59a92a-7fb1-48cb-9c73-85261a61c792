<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;
use ZipArchive;

class DatabaseBackupService
{
    protected string $backupPath;
    protected string $tempPath;

    public function __construct()
    {
        $this->backupPath = storage_path('app/backups');
        $this->tempPath = storage_path('app/temp');
        
        // Ensure directories exist
        if (!File::exists($this->backupPath)) {
            File::makeDirectory($this->backupPath, 0755, true);
        }
        
        if (!File::exists($this->tempPath)) {
            File::makeDirectory($this->tempPath, 0755, true);
        }
    }

    /**
     * Create a complete system backup
     */
    public function createFullBackup(array $options = []): array
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $backupName = $options['name'] ?? "invoice_system_backup_{$timestamp}";
            $backupDir = $this->tempPath . '/' . $backupName;
            
            // Create backup directory
            File::makeDirectory($backupDir, 0755, true);
            
            $result = [
                'success' => true,
                'backup_name' => $backupName,
                'timestamp' => $timestamp,
                'components' => []
            ];

            // 1. Export Database
            if ($options['include_database'] ?? true) {
                $dbFile = $this->exportDatabase($backupDir);
                $result['components']['database'] = $dbFile;
            }

            // 2. Export Configuration Files
            if ($options['include_config'] ?? true) {
                $configFiles = $this->exportConfiguration($backupDir);
                $result['components']['config'] = $configFiles;
            }

            // 3. Export Storage Files
            if ($options['include_storage'] ?? true) {
                $storageFiles = $this->exportStorage($backupDir);
                $result['components']['storage'] = $storageFiles;
            }

            // 4. Export System Information
            $systemInfo = $this->exportSystemInfo($backupDir);
            $result['components']['system_info'] = $systemInfo;

            // 5. Create ZIP Archive
            $zipFile = $this->createZipArchive($backupDir, $backupName);
            $result['zip_file'] = $zipFile;
            $result['file_size'] = File::size($zipFile);

            // Clean up temp directory
            File::deleteDirectory($backupDir);

            return $result;

        } catch (\Exception $e) {
            \Log::error('Backup creation failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Export database to SQL file with enhanced error handling
     */
    protected function exportDatabase(string $backupDir): string
    {
        try {
            $dbConfig = config('database.connections.' . config('database.default'));
            $dbFile = $backupDir . '/database.sql';

            // Check if mysqldump is available
            $mysqldumpPath = $this->findMysqldumpPath();

            if (!$mysqldumpPath) {
                // Fallback to Laravel's database export
                return $this->exportDatabaseFallback($backupDir);
            }

            // Create SQL dump with enhanced error handling
            $command = sprintf(
                '"%s" --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers --add-drop-table --create-options %s > "%s" 2>&1',
                $mysqldumpPath,
                $dbConfig['host'],
                $dbConfig['port'] ?? 3306,
                $dbConfig['username'],
                $dbConfig['password'],
                $dbConfig['database'],
                $dbFile
            );

            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                \Log::error('mysqldump failed', ['command' => $command, 'output' => $output, 'return_code' => $returnCode]);
                // Try fallback method
                return $this->exportDatabaseFallback($backupDir);
            }

            // Verify the file was created and has content
            if (!File::exists($dbFile) || File::size($dbFile) < 100) {
                throw new \Exception('Database export file is empty or corrupted');
            }

            return $dbFile;

        } catch (\Exception $e) {
            \Log::error('Database export error: ' . $e->getMessage());
            // Try fallback method
            return $this->exportDatabaseFallback($backupDir);
        }
    }

    /**
     * Find mysqldump executable path
     */
    protected function findMysqldumpPath(): ?string
    {
        $possiblePaths = [
            'C:\\xampp\\mysql\\bin\\mysqldump.exe',
            'C:\\wamp\\bin\\mysql\\mysql8.0.21\\bin\\mysqldump.exe',
            'C:\\laragon\\bin\\mysql\\mysql-8.0.30-winx64\\bin\\mysqldump.exe',
            'C:\\Program Files\\MySQL\\MySQL Server 8.0\\bin\\mysqldump.exe',
            'C:\\Program Files (x86)\\MySQL\\MySQL Server 8.0\\bin\\mysqldump.exe',
            '/usr/bin/mysqldump',
            '/usr/local/bin/mysqldump',
            '/opt/homebrew/bin/mysqldump',
        ];

        // First check explicit paths
        foreach ($possiblePaths as $path) {
            if (File::exists($path)) {
                \Log::info('Found mysqldump at: ' . $path);
                return $path;
            }
        }

        // Then check system PATH
        if ($this->commandExists('mysqldump')) {
            return 'mysqldump';
        }

        \Log::warning('mysqldump not found in any known location');
        return null;
    }

    /**
     * Check if command exists in system PATH
     */
    protected function commandExists(string $command): bool
    {
        $testCommand = PHP_OS_FAMILY === 'Windows' ? "where $command" : "which $command";
        exec($testCommand, $output, $returnCode);
        return $returnCode === 0;
    }

    /**
     * Fallback database export using Laravel's database connection
     */
    protected function exportDatabaseFallback(string $backupDir): string
    {
        $dbFile = $backupDir . '/database_fallback.sql';
        $content = "-- Database Export (Fallback Method)\n";
        $content .= "-- Generated on: " . Carbon::now()->toISOString() . "\n\n";

        try {
            // Get all table names
            $tables = DB::select('SHOW TABLES');
            $dbName = config('database.connections.' . config('database.default') . '.database');

            foreach ($tables as $table) {
                $tableName = array_values((array) $table)[0];

                // Get table structure
                $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`");
                if (!empty($createTable)) {
                    $content .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
                    $content .= $createTable[0]->{'Create Table'} . ";\n\n";
                }

                // Get table data
                $rows = DB::table($tableName)->get();
                if ($rows->count() > 0) {
                    $content .= "INSERT INTO `{$tableName}` VALUES\n";
                    $values = [];
                    foreach ($rows as $row) {
                        $rowData = array_map(function($value) {
                            return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
                        }, (array) $row);
                        $values[] = '(' . implode(',', $rowData) . ')';
                    }
                    $content .= implode(",\n", $values) . ";\n\n";
                }
            }

            File::put($dbFile, $content);
            return $dbFile;

        } catch (\Exception $e) {
            \Log::error('Fallback database export failed: ' . $e->getMessage());
            throw new \Exception('Both mysqldump and fallback export methods failed');
        }
    }

    /**
     * Export configuration files
     */
    protected function exportConfiguration(string $backupDir): array
    {
        $configDir = $backupDir . '/config';
        File::makeDirectory($configDir, 0755, true);

        $configFiles = [
            '.env' => base_path('.env'),
            'app.php' => config_path('app.php'),
            'database.php' => config_path('database.php'),
            'mail.php' => config_path('mail.php'),
            'filesystems.php' => config_path('filesystems.php'),
        ];

        $exported = [];
        foreach ($configFiles as $name => $source) {
            if (File::exists($source)) {
                $destination = $configDir . '/' . $name;
                File::copy($source, $destination);
                $exported[] = $destination;
            }
        }

        return $exported;
    }

    /**
     * Export storage files
     */
    protected function exportStorage(string $backupDir): array
    {
        $storageDir = $backupDir . '/storage';
        File::makeDirectory($storageDir, 0755, true);

        $storagePaths = [
            'public' => storage_path('app/public'),
            'invoices' => storage_path('app/invoices'),
            'quotes' => storage_path('app/quotes'),
            'uploads' => storage_path('app/uploads'),
        ];

        $exported = [];
        foreach ($storagePaths as $name => $source) {
            if (File::exists($source)) {
                $destination = $storageDir . '/' . $name;
                File::copyDirectory($source, $destination);
                $exported[] = $destination;
            }
        }

        return $exported;
    }

    /**
     * Export system information
     */
    protected function exportSystemInfo(string $backupDir): string
    {
        $infoFile = $backupDir . '/system_info.json';
        
        $systemInfo = [
            'backup_created_at' => Carbon::now()->toISOString(),
            'laravel_version' => app()->version(),
            'php_version' => PHP_VERSION,
            'database_version' => DB::select('SELECT VERSION() as version')[0]->version ?? 'Unknown',
            'environment' => app()->environment(),
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
            'tables' => $this->getTableInfo(),
            'migrations' => $this->getMigrationInfo(),
        ];

        File::put($infoFile, json_encode($systemInfo, JSON_PRETTY_PRINT));
        
        return $infoFile;
    }

    /**
     * Get table information
     */
    protected function getTableInfo(): array
    {
        $tables = DB::select('SHOW TABLES');
        $tableInfo = [];

        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            $count = DB::table($tableName)->count();
            $tableInfo[$tableName] = $count;
        }

        return $tableInfo;
    }

    /**
     * Get migration information
     */
    protected function getMigrationInfo(): array
    {
        try {
            $migrations = DB::table('migrations')->orderBy('batch')->get();
            return $migrations->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Create ZIP archive
     */
    protected function createZipArchive(string $sourceDir, string $backupName): string
    {
        $zipFile = $this->backupPath . '/' . $backupName . '.zip';
        $zip = new ZipArchive();

        if ($zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception('Cannot create ZIP file');
        }

        $this->addDirectoryToZip($zip, $sourceDir, '');
        $zip->close();

        return $zipFile;
    }

    /**
     * Add directory to ZIP recursively
     */
    protected function addDirectoryToZip(ZipArchive $zip, string $sourceDir, string $zipPath): void
    {
        $files = File::allFiles($sourceDir);
        
        foreach ($files as $file) {
            $relativePath = $zipPath . str_replace($sourceDir, '', $file->getPathname());
            $zip->addFile($file->getPathname(), ltrim($relativePath, '/'));
        }
    }

    /**
     * List available backups
     */
    public function listBackups(): array
    {
        $backups = [];
        $files = File::files($this->backupPath);

        foreach ($files as $file) {
            if ($file->getExtension() === 'zip') {
                $backups[] = [
                    'name' => $file->getFilenameWithoutExtension(),
                    'file' => $file->getFilename(),
                    'size' => $file->getSize(),
                    'created_at' => Carbon::createFromTimestamp($file->getMTime()),
                    'path' => $file->getPathname(),
                ];
            }
        }

        return collect($backups)->sortByDesc('created_at')->values()->toArray();
    }

    /**
     * Delete backup
     */
    public function deleteBackup(string $filename): bool
    {
        $filePath = $this->backupPath . '/' . $filename;
        
        if (File::exists($filePath)) {
            return File::delete($filePath);
        }
        
        return false;
    }

    /**
     * Download backup
     */
    public function downloadBackup(string $filename): string
    {
        $filePath = $this->backupPath . '/' . $filename;

        if (!File::exists($filePath)) {
            throw new \Exception('Backup file not found');
        }

        return $filePath;
    }

    /**
     * Import/Restore backup from uploaded file
     */
    public function importBackup(string $backupFilePath, array $options = []): array
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $extractDir = $this->tempPath . '/restore_' . $timestamp;

            // Extract ZIP file
            $zip = new ZipArchive();
            if ($zip->open($backupFilePath) !== TRUE) {
                throw new \Exception('Cannot open backup file');
            }

            $zip->extractTo($extractDir);
            $zip->close();

            $result = [
                'success' => true,
                'timestamp' => $timestamp,
                'restored_components' => []
            ];

            // 1. Restore Database
            if ($options['restore_database'] ?? true) {
                $dbFile = $extractDir . '/database.sql';
                if (File::exists($dbFile)) {
                    $this->importDatabase($dbFile);
                    $result['restored_components'][] = 'database';
                }
            }

            // 2. Restore Configuration (with caution)
            if ($options['restore_config'] ?? false) {
                $configDir = $extractDir . '/config';
                if (File::exists($configDir)) {
                    $this->importConfiguration($configDir, $options);
                    $result['restored_components'][] = 'configuration';
                }
            }

            // 3. Restore Storage Files
            if ($options['restore_storage'] ?? true) {
                $storageDir = $extractDir . '/storage';
                if (File::exists($storageDir)) {
                    $this->importStorage($storageDir);
                    $result['restored_components'][] = 'storage';
                }
            }

            // Clean up
            File::deleteDirectory($extractDir);

            return $result;

        } catch (\Exception $e) {
            \Log::error('Backup import failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Import database from SQL file
     */
    protected function importDatabase(string $sqlFile): void
    {
        $dbConfig = config('database.connections.' . config('database.default'));

        $command = sprintf(
            'mysql --host=%s --port=%s --user=%s --password=%s %s < %s',
            $dbConfig['host'],
            $dbConfig['port'] ?? 3306,
            $dbConfig['username'],
            $dbConfig['password'],
            $dbConfig['database'],
            $sqlFile
        );

        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new \Exception('Database import failed');
        }

        // Run migrations to ensure schema is up to date
        Artisan::call('migrate', ['--force' => true]);
    }

    /**
     * Import configuration files (with safety checks)
     */
    protected function importConfiguration(string $configDir, array $options): void
    {
        $configFiles = [
            'app.php' => config_path('app.php'),
            'database.php' => config_path('database.php'),
            'mail.php' => config_path('mail.php'),
            'filesystems.php' => config_path('filesystems.php'),
        ];

        // Only restore .env if explicitly requested and confirmed
        if ($options['restore_env'] ?? false) {
            $configFiles['.env'] = base_path('.env');
        }

        foreach ($configFiles as $name => $destination) {
            $source = $configDir . '/' . $name;
            if (File::exists($source)) {
                // Create backup of existing file
                if (File::exists($destination)) {
                    File::copy($destination, $destination . '.backup.' . time());
                }
                File::copy($source, $destination);
            }
        }
    }

    /**
     * Import storage files
     */
    protected function importStorage(string $storageDir): void
    {
        $storagePaths = [
            'public' => storage_path('app/public'),
            'invoices' => storage_path('app/invoices'),
            'quotes' => storage_path('app/quotes'),
            'uploads' => storage_path('app/uploads'),
        ];

        foreach ($storagePaths as $name => $destination) {
            $source = $storageDir . '/' . $name;
            if (File::exists($source)) {
                // Create backup of existing directory
                if (File::exists($destination)) {
                    $backupPath = $destination . '_backup_' . time();
                    File::moveDirectory($destination, $backupPath);
                }
                File::copyDirectory($source, $destination);
            }
        }
    }

    /**
     * Validate backup file
     */
    public function validateBackup(string $backupFilePath): array
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($backupFilePath) !== TRUE) {
                return ['valid' => false, 'error' => 'Cannot open backup file'];
            }

            $requiredFiles = ['system_info.json'];
            $foundFiles = [];

            for ($i = 0; $i < $zip->numFiles; $i++) {
                $filename = $zip->getNameIndex($i);
                $foundFiles[] = $filename;
            }

            $zip->close();

            $hasSystemInfo = in_array('system_info.json', $foundFiles);
            $hasDatabase = in_array('database.sql', $foundFiles);
            $hasConfig = count(array_filter($foundFiles, fn($f) => str_contains($f, 'config/'))) > 0;
            $hasStorage = count(array_filter($foundFiles, fn($f) => str_contains($f, 'storage/'))) > 0;

            return [
                'valid' => $hasSystemInfo,
                'components' => [
                    'database' => $hasDatabase,
                    'config' => $hasConfig,
                    'storage' => $hasStorage,
                ],
                'files' => $foundFiles
            ];

        } catch (\Exception $e) {
            return ['valid' => false, 'error' => $e->getMessage()];
        }
    }
}
