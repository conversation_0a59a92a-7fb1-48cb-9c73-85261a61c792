<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class DatabaseExportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:export {--format=sql : Export format (sql)} {--path= : Custom export path}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export the complete database to /exportDatabase/ folder with timestamp';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('🚀 Starting database export...');

            // Create export directory
            $exportDir = base_path('exportDatabase');
            if (!File::exists($exportDir)) {
                File::makeDirectory($exportDir, 0755, true);
                $this->info("✅ Created export directory: {$exportDir}");
            }

            // Generate filename with timestamp
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "database_backup_{$timestamp}.sql";
            $filepath = $exportDir . DIRECTORY_SEPARATOR . $filename;

            // Get database configuration
            $database = config('database.default');
            $config = config("database.connections.{$database}");

            $this->info("📊 Database: {$config['database']} ({$database})");
            $this->info("📁 Export file: {$filename}");

            // Create progress bar
            $this->output->progressStart(100);

            // Export database using different methods based on driver
            if ($database === 'mysql') {
                $success = $this->exportMysqlDatabase($config, $filepath);
            } else {
                $success = $this->exportGenericDatabase($filepath);
            }

            $this->output->progressFinish();

            if ($success) {
                $fileSize = File::size($filepath);
                $this->info("\n🎉 Database export completed successfully!");
                $this->info("📁 File: {$filepath}");
                $this->info("📊 Size: " . $this->formatBytes($fileSize));
                $this->info("⏰ Timestamp: " . Carbon::now()->format('Y-m-d H:i:s'));
                return Command::SUCCESS;
            } else {
                $this->error("\n❌ Database export failed!");
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error("\n💥 Export failed with error: " . $e->getMessage());
            $this->error("🔍 Stack trace: " . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }

    /**
     * Export MySQL database using mysqldump
     */
    private function exportMysqlDatabase(array $config, string $filepath): bool
    {
        try {
            // Try to find mysqldump executable
            $mysqldumpPaths = [
                'C:\xampp\mysql\bin\mysqldump.exe',
                'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe',
                'C:\Program Files\MySQL\MySQL Server 5.7\bin\mysqldump.exe',
                'mysqldump', // System PATH
            ];

            $mysqldump = null;
            foreach ($mysqldumpPaths as $path) {
                if (file_exists($path) || $path === 'mysqldump') {
                    $mysqldump = $path;
                    break;
                }
            }

            if (!$mysqldump) {
                $this->warn("⚠️  mysqldump not found, falling back to Laravel export...");
                return $this->exportGenericDatabase($filepath);
            }

            // Build mysqldump command
            $host = $config['host'] ?? 'localhost';
            $port = $config['port'] ?? 3306;
            $username = $config['username'] ?? '';
            $password = $config['password'] ?? '';
            $database = $config['database'] ?? '';

            $command = sprintf(
                '"%s" --host=%s --port=%d --user=%s --password=%s --single-transaction --routines --triggers --add-drop-table --complete-insert %s > "%s"',
                $mysqldump,
                escapeshellarg($host),
                $port,
                escapeshellarg($username),
                escapeshellarg($password),
                escapeshellarg($database),
                escapeshellarg($filepath)
            );

            $this->output->progressAdvance(30);

            // Execute mysqldump
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            $this->output->progressAdvance(60);

            if ($returnCode === 0 && file_exists($filepath) && filesize($filepath) > 0) {
                $this->output->progressAdvance(10);
                return true;
            } else {
                $this->warn("⚠️  mysqldump failed, falling back to Laravel export...");
                $this->warn("Command output: " . implode("\n", $output));
                return $this->exportGenericDatabase($filepath);
            }

        } catch (\Exception $e) {
            $this->warn("⚠️  mysqldump error: " . $e->getMessage());
            return $this->exportGenericDatabase($filepath);
        }
    }

    /**
     * Export database using Laravel's database connection
     */
    private function exportGenericDatabase(string $filepath): bool
    {
        try {
            $this->info("📝 Using Laravel database export...");

            // Get all table names
            $tables = $this->getAllTables();
            $this->output->progressAdvance(10);

            $sql = "-- Database Export\n";
            $sql .= "-- Generated on: " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
            $sql .= "-- Laravel Invoice Management System\n\n";
            $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

            $totalTables = count($tables);
            $progressPerTable = 80 / max($totalTables, 1);

            foreach ($tables as $table) {
                $this->info("📊 Exporting table: {$table}");

                // Export table structure
                $sql .= $this->getCreateTableStatement($table);
                $sql .= "\n\n";

                // Export table data
                $sql .= $this->getTableData($table);
                $sql .= "\n\n";

                $this->output->progressAdvance($progressPerTable);
            }

            $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";

            // Write to file
            File::put($filepath, $sql);
            $this->output->progressAdvance(10);

            return file_exists($filepath) && filesize($filepath) > 0;

        } catch (\Exception $e) {
            $this->error("❌ Generic export failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all table names
     */
    private function getAllTables(): array
    {
        $database = config('database.default');

        if ($database === 'mysql') {
            return DB::select('SHOW TABLES');
        } elseif ($database === 'pgsql') {
            return DB::select("SELECT tablename FROM pg_tables WHERE schemaname = 'public'");
        } else {
            // For SQLite and others, get from information schema
            return DB::select("SELECT name FROM sqlite_master WHERE type='table'");
        }
    }

    /**
     * Get CREATE TABLE statement
     */
    private function getCreateTableStatement(string $table): string
    {
        try {
            $database = config('database.default');

            if ($database === 'mysql') {
                $result = DB::select("SHOW CREATE TABLE `{$table}`");
                return "DROP TABLE IF EXISTS `{$table}`;\n" . $result[0]->{'Create Table'} . ";\n";
            } else {
                // For non-MySQL databases, create a basic structure
                return "-- Table structure for {$table} (generic export)\n";
            }
        } catch (\Exception $e) {
            return "-- Error getting structure for table {$table}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get table data as INSERT statements
     */
    private function getTableData(string $table): string
    {
        try {
            $rows = DB::table($table)->get();

            if ($rows->isEmpty()) {
                return "-- No data for table {$table}\n";
            }

            $sql = "-- Data for table {$table}\n";
            $sql .= "INSERT INTO `{$table}` VALUES\n";

            $values = [];
            foreach ($rows as $row) {
                $rowData = [];
                foreach ((array)$row as $value) {
                    if (is_null($value)) {
                        $rowData[] = 'NULL';
                    } else {
                        $rowData[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $rowData) . ')';
            }

            $sql .= implode(",\n", $values) . ";\n";

            return $sql;

        } catch (\Exception $e) {
            return "-- Error getting data for table {$table}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
