<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use App\Services\RoleManagementService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterCreate(): void
    {
        $roleManagementService = app(RoleManagementService::class);
        
        // Clear role cache after creation
        cache()->forget('permissions_by_category');
        cache()->forget('spatie.permission.cache');
        
        Notification::make()
            ->title('Role created successfully')
            ->body("The role '{$this->record->name}' has been created with " . $this->record->permissions()->count() . " permissions.")
            ->success()
            ->send();
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure guard_name is set
        $data['guard_name'] = $data['guard_name'] ?? 'web';
        
        return $data;
    }
}
