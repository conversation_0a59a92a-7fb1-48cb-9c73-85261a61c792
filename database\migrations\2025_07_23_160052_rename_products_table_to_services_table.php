<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Rename products table to services for logistics service-centric architecture
     */
    public function up(): void
    {
        // Step 1: Rename the table
        Schema::rename('products', 'services');

        // Step 2: Update foreign key references in invoice_items table
        Schema::table('invoice_items', function (Blueprint $table) {
            // Drop existing foreign key constraint if it exists
            try {
                $table->dropForeign(['product_id']);
            } catch (\Exception $e) {
                // Foreign key might not exist, continue
            }

            // Rename the column
            $table->renameColumn('product_id', 'service_id');

            // Add new foreign key constraint
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });

        // Step 3: Update performance indexes
        DB::statement('DROP INDEX IF EXISTS idx_products_name');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_services_name ON services (name)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the process
        Schema::table('invoice_items', function (Blueprint $table) {
            try {
                $table->dropForeign(['service_id']);
            } catch (\Exception $e) {
                // Foreign key might not exist, continue
            }
            $table->renameColumn('service_id', 'product_id');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });

        Schema::rename('services', 'products');

        // Restore indexes
        DB::statement('DROP INDEX IF EXISTS idx_services_name');
        DB::statement('CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)');
    }
};
