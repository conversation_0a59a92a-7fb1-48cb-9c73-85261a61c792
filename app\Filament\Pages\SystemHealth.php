<?php

namespace App\Filament\Pages;

use App\Services\SystemStabilityService;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;

class SystemHealth extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-heart';
    protected static ?string $navigationLabel = 'System Health';
    protected static ?string $title = 'System Health Dashboard';
    protected static string $view = 'filament.pages.system-health';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'System';

    public ?array $healthData = null;
    public ?array $systemInfo = null;

    protected SystemStabilityService $stabilityService;

    public function boot(SystemStabilityService $stabilityService): void
    {
        $this->stabilityService = $stabilityService;
    }

    public function mount(): void
    {
        $this->refreshHealthData();
    }

    public function refreshHealthData(): void
    {
        $this->healthData = $this->stabilityService->performHealthCheck();
        $this->systemInfo = $this->stabilityService->getSystemInfo();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->refreshHealthData();
                    Notification::make()
                        ->title('System health data refreshed')
                        ->success()
                        ->send();
                }),

            Action::make('auto_fix')
                ->label('Auto Fix Issues')
                ->icon('heroicon-o-wrench-screwdriver')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Auto Fix System Issues')
                ->modalDescription('This will attempt to automatically fix common system issues. Continue?')
                ->action(function () {
                    $result = $this->stabilityService->autoFixIssues();
                    
                    if ($result['status'] === 'success') {
                        Notification::make()
                            ->title('Auto fix completed')
                            ->body(count($result['fixes_applied']) . ' issues fixed')
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Auto fix failed')
                            ->body($result['message'])
                            ->danger()
                            ->send();
                    }
                    
                    $this->refreshHealthData();
                }),

            Action::make('system_info')
                ->label('System Info')
                ->icon('heroicon-o-information-circle')
                ->color('info')
                ->modalHeading('System Information')
                ->modalWidth(MaxWidth::FourExtraLarge)
                ->modalContent(function (): View {
                    return view('filament.pages.system-info', [
                        'systemInfo' => $this->systemInfo
                    ]);
                }),

            Action::make('export_report')
                ->label('Export Report')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->action(function () {
                    // Implementation for exporting health report
                    Notification::make()
                        ->title('Export functionality coming soon')
                        ->info()
                        ->send();
                }),
        ];
    }

    public function getOverallStatusColor(): string
    {
        if (!$this->healthData) {
            return 'gray';
        }

        return match ($this->healthData['overall_status']) {
            'healthy' => 'success',
            'warning' => 'warning',
            'critical' => 'danger',
            default => 'gray',
        };
    }

    public function getOverallStatusIcon(): string
    {
        if (!$this->healthData) {
            return 'heroicon-o-question-mark-circle';
        }

        return match ($this->healthData['overall_status']) {
            'healthy' => 'heroicon-o-check-circle',
            'warning' => 'heroicon-o-exclamation-triangle',
            'critical' => 'heroicon-o-x-circle',
            default => 'heroicon-o-question-mark-circle',
        };
    }

    public function getCheckStatusColor(string $status): string
    {
        return match ($status) {
            'healthy' => 'success',
            'warning' => 'warning',
            'error' => 'danger',
            default => 'gray',
        };
    }

    public function getCheckStatusIcon(string $status): string
    {
        return match ($status) {
            'healthy' => 'heroicon-o-check-circle',
            'warning' => 'heroicon-o-exclamation-triangle',
            'error' => 'heroicon-o-x-circle',
            default => 'heroicon-o-question-mark-circle',
        };
    }

    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
