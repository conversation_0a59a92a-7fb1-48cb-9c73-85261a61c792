<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\InvoicePerformanceService;

class WarmInvoiceCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoice:warm-caches {--clear : Clear caches before warming}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up invoice-related caches for better performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('clear')) {
            $this->info('Clearing invoice caches...');
            InvoicePerformanceService::clearCaches();
        }

        $this->info('Warming up invoice caches...');

        $startTime = microtime(true);

        InvoicePerformanceService::warmUpCaches();

        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);

        $this->info("Invoice caches warmed up successfully in {$duration}ms");

        return Command::SUCCESS;
    }
}
