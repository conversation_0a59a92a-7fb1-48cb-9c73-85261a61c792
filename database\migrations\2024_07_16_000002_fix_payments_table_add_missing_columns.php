<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Add missing payment_method column if it doesn't exist
            if (!Schema::hasColumn('payments', 'payment_method')) {
                $table->string('payment_method')->default('cash')->after('amount');
            }
            
            // Add other potentially missing columns
            if (!Schema::hasColumn('payments', 'payment_date')) {
                $table->date('payment_date')->default(now())->after('payment_method');
            }
            
            if (!Schema::hasColumn('payments', 'reference_number')) {
                $table->string('reference_number')->nullable()->after('payment_date');
            }
            
            if (!Schema::hasColumn('payments', 'notes')) {
                $table->text('notes')->nullable()->after('reference_number');
            }
            
            // Add indexes for better performance
            $table->index('payment_method');
            $table->index('payment_date');
            $table->index(['invoice_id', 'payment_date']);
        });
    }

    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn(['payment_method', 'payment_date', 'reference_number', 'notes']);
            $table->dropIndex(['payment_method']);
            $table->dropIndex(['payment_date']);
            $table->dropIndex(['invoice_id', 'payment_date']);
        });
    }
};
