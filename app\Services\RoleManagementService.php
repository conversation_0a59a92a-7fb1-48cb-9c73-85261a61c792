<?php

namespace App\Services;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class RoleManagementService
{
    /**
     * Get all available permissions grouped by category
     */
    public function getPermissionsByCategory(): array
    {
        return Cache::remember('permissions_by_category', 3600, function () {
            $permissions = Permission::all();
            
            $categories = [
                'User Management' => [
                    'view_users',
                    'create_users',
                    'edit_users',
                    'delete_users',
                    'manage_user_roles',
                    'impersonate_users',
                ],
                'Invoice Management' => [
                    'view_invoices',
                    'create_invoices',
                    'edit_invoices',
                    'delete_invoices',
                    'send_invoices',
                    'approve_invoices',
                    'cancel_invoices',
                    'duplicate_invoices',
                ],
                'Client Management' => [
                    'view_clients',
                    'create_clients',
                    'edit_clients',
                    'delete_clients',
                    'manage_client_contacts',
                    'view_client_history',
                ],
                'Payment Management' => [
                    'view_payments',
                    'create_payments',
                    'edit_payments',
                    'delete_payments',
                    'process_refunds',
                    'manage_payment_methods',
                ],
                'Product Management' => [
                    'view_products',
                    'create_products',
                    'edit_products',
                    'delete_products',
                    'manage_categories',
                    'manage_taxes',
                ],
                'Reporting & Analytics' => [
                    'view_reports',
                    'create_reports',
                    'export_reports',
                    'view_analytics',
                    'view_financial_data',
                    'access_advanced_reports',
                ],
                'System Administration' => [
                    'manage_settings',
                    'manage_roles',
                    'manage_permissions',
                    'view_audit_logs',
                    'manage_system_config',
                    'access_developer_tools',
                ],
                'Quote Management' => [
                    'view_quotes',
                    'create_quotes',
                    'edit_quotes',
                    'delete_quotes',
                    'convert_quotes',
                    'send_quotes',
                ],
            ];

            $result = [];
            foreach ($categories as $category => $permissionNames) {
                $categoryPermissions = $permissions->whereIn('name', $permissionNames);
                if ($categoryPermissions->isNotEmpty()) {
                    $result[$category] = $categoryPermissions->values();
                }
            }

            return $result;
        });
    }

    /**
     * Create a new role with permissions
     */
    public function createRole(string $name, string $description = null, array $permissions = []): Role
    {
        DB::beginTransaction();
        
        try {
            $role = Role::create([
                'name' => $name,
                'guard_name' => 'web',
                'description' => $description,
            ]);

            if (!empty($permissions)) {
                $role->syncPermissions($permissions);
            }

            DB::commit();
            $this->clearRoleCache();
            
            return $role;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update role permissions
     */
    public function updateRolePermissions(Role $role, array $permissions): Role
    {
        DB::beginTransaction();
        
        try {
            $role->syncPermissions($permissions);
            DB::commit();
            $this->clearRoleCache();
            
            return $role;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Assign role to user
     */
    public function assignRoleToUser(User $user, string $roleName): User
    {
        $user->assignRole($roleName);
        $this->clearUserCache($user);
        
        return $user;
    }

    /**
     * Remove role from user
     */
    public function removeRoleFromUser(User $user, string $roleName): User
    {
        $user->removeRole($roleName);
        $this->clearUserCache($user);
        
        return $user;
    }

    /**
     * Get role hierarchy and relationships
     */
    public function getRoleHierarchy(): array
    {
        $roles = Role::with('permissions')->get();
        
        $hierarchy = [
            'Super Admin' => [
                'level' => 1,
                'description' => 'Full system access with all permissions',
                'color' => 'red',
                'icon' => 'heroicon-o-shield-check',
            ],
            'Admin' => [
                'level' => 2,
                'description' => 'Administrative access with most permissions',
                'color' => 'orange',
                'icon' => 'heroicon-o-user-group',
            ],
            'Manager' => [
                'level' => 3,
                'description' => 'Management access with business operations',
                'color' => 'blue',
                'icon' => 'heroicon-o-briefcase',
            ],
            'Accountant' => [
                'level' => 4,
                'description' => 'Financial and reporting access',
                'color' => 'green',
                'icon' => 'heroicon-o-calculator',
            ],
            'Sales' => [
                'level' => 5,
                'description' => 'Client and invoice management',
                'color' => 'purple',
                'icon' => 'heroicon-o-chart-bar',
            ],
            'Client' => [
                'level' => 6,
                'description' => 'Limited client portal access',
                'color' => 'gray',
                'icon' => 'heroicon-o-user',
            ],
        ];

        foreach ($roles as $role) {
            if (isset($hierarchy[$role->name])) {
                $hierarchy[$role->name]['role'] = $role;
                $hierarchy[$role->name]['user_count'] = $role->users()->count();
                $hierarchy[$role->name]['permission_count'] = $role->permissions()->count();
            }
        }

        return $hierarchy;
    }

    /**
     * Get user role analytics
     */
    public function getUserRoleAnalytics(): array
    {
        $roleStats = Role::withCount(['users', 'permissions'])->get();
        
        $analytics = [
            'total_roles' => $roleStats->count(),
            'total_users_with_roles' => User::role(Role::all())->count(),
            'users_without_roles' => User::doesntHave('roles')->count(),
            'role_distribution' => [],
            'permission_usage' => $this->getPermissionUsageStats(),
        ];

        foreach ($roleStats as $role) {
            $analytics['role_distribution'][] = [
                'name' => $role->name,
                'user_count' => $role->users_count,
                'permission_count' => $role->permissions_count,
                'percentage' => $analytics['total_users_with_roles'] > 0 
                    ? round(($role->users_count / $analytics['total_users_with_roles']) * 100, 1) 
                    : 0,
            ];
        }

        return $analytics;
    }

    /**
     * Get permission usage statistics
     */
    public function getPermissionUsageStats(): array
    {
        $permissions = Permission::withCount('roles')->get();
        
        return $permissions->map(function ($permission) {
            return [
                'name' => $permission->name,
                'role_count' => $permission->roles_count,
                'usage_percentage' => Role::count() > 0 
                    ? round(($permission->roles_count / Role::count()) * 100, 1) 
                    : 0,
            ];
        })->sortByDesc('role_count')->values()->toArray();
    }

    /**
     * Create default system roles
     */
    public function createDefaultRoles(): void
    {
        $defaultRoles = [
            'Super Admin' => [
                'description' => 'Full system access with all permissions',
                'permissions' => Permission::all()->pluck('name')->toArray(),
            ],
            'Admin' => [
                'description' => 'Administrative access with most permissions',
                'permissions' => [
                    'view_users', 'create_users', 'edit_users',
                    'view_invoices', 'create_invoices', 'edit_invoices', 'send_invoices',
                    'view_clients', 'create_clients', 'edit_clients',
                    'view_payments', 'create_payments', 'edit_payments',
                    'view_products', 'create_products', 'edit_products',
                    'view_reports', 'create_reports', 'export_reports',
                    'view_quotes', 'create_quotes', 'edit_quotes',
                ],
            ],
            'Manager' => [
                'description' => 'Management access with business operations',
                'permissions' => [
                    'view_users',
                    'view_invoices', 'create_invoices', 'edit_invoices', 'send_invoices',
                    'view_clients', 'create_clients', 'edit_clients',
                    'view_payments', 'create_payments',
                    'view_products', 'create_products', 'edit_products',
                    'view_reports', 'export_reports',
                    'view_quotes', 'create_quotes', 'edit_quotes',
                ],
            ],
            'Accountant' => [
                'description' => 'Financial and reporting access',
                'permissions' => [
                    'view_invoices', 'edit_invoices',
                    'view_clients',
                    'view_payments', 'create_payments', 'edit_payments',
                    'view_reports', 'create_reports', 'export_reports', 'view_financial_data',
                ],
            ],
            'Sales' => [
                'description' => 'Client and invoice management',
                'permissions' => [
                    'view_invoices', 'create_invoices', 'edit_invoices', 'send_invoices',
                    'view_clients', 'create_clients', 'edit_clients',
                    'view_products',
                    'view_quotes', 'create_quotes', 'edit_quotes', 'send_quotes',
                ],
            ],
            'Client' => [
                'description' => 'Limited client portal access',
                'permissions' => [
                    'view_invoices',
                    'view_payments',
                ],
            ],
        ];

        foreach ($defaultRoles as $roleName => $roleData) {
            if (!Role::where('name', $roleName)->exists()) {
                $this->createRole($roleName, $roleData['description'], $roleData['permissions']);
            }
        }
    }

    /**
     * Create default permissions
     */
    public function createDefaultPermissions(): void
    {
        $permissions = [
            // User Management
            'view_users' => 'View users',
            'create_users' => 'Create users',
            'edit_users' => 'Edit users',
            'delete_users' => 'Delete users',
            'manage_user_roles' => 'Manage user roles',
            'impersonate_users' => 'Impersonate users',

            // Invoice Management
            'view_invoices' => 'View invoices',
            'create_invoices' => 'Create invoices',
            'edit_invoices' => 'Edit invoices',
            'delete_invoices' => 'Delete invoices',
            'send_invoices' => 'Send invoices',
            'approve_invoices' => 'Approve invoices',
            'cancel_invoices' => 'Cancel invoices',
            'duplicate_invoices' => 'Duplicate invoices',

            // Client Management
            'view_clients' => 'View clients',
            'create_clients' => 'Create clients',
            'edit_clients' => 'Edit clients',
            'delete_clients' => 'Delete clients',
            'manage_client_contacts' => 'Manage client contacts',
            'view_client_history' => 'View client history',

            // Payment Management
            'view_payments' => 'View payments',
            'create_payments' => 'Create payments',
            'edit_payments' => 'Edit payments',
            'delete_payments' => 'Delete payments',
            'process_refunds' => 'Process refunds',
            'manage_payment_methods' => 'Manage payment methods',

            // Product Management
            'view_products' => 'View products',
            'create_products' => 'Create products',
            'edit_products' => 'Edit products',
            'delete_products' => 'Delete products',
            'manage_categories' => 'Manage categories',
            'manage_taxes' => 'Manage taxes',

            // Reporting & Analytics
            'view_reports' => 'View reports',
            'create_reports' => 'Create reports',
            'export_reports' => 'Export reports',
            'view_analytics' => 'View analytics',
            'view_financial_data' => 'View financial data',
            'access_advanced_reports' => 'Access advanced reports',

            // System Administration
            'manage_settings' => 'Manage settings',
            'manage_roles' => 'Manage roles',
            'manage_permissions' => 'Manage permissions',
            'view_audit_logs' => 'View audit logs',
            'manage_system_config' => 'Manage system configuration',
            'access_developer_tools' => 'Access developer tools',

            // Quote Management
            'view_quotes' => 'View quotes',
            'create_quotes' => 'Create quotes',
            'edit_quotes' => 'Edit quotes',
            'delete_quotes' => 'Delete quotes',
            'convert_quotes' => 'Convert quotes to invoices',
            'send_quotes' => 'Send quotes',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(
                ['name' => $name, 'guard_name' => 'web'],
                ['description' => $description]
            );
        }
    }

    /**
     * Check if user has specific permission
     */
    public function userHasPermission(User $user, string $permission): bool
    {
        return $user->hasPermissionTo($permission);
    }

    /**
     * Get user's effective permissions
     */
    public function getUserEffectivePermissions(User $user): Collection
    {
        return $user->getAllPermissions();
    }

    /**
     * Clear role-related caches
     */
    private function clearRoleCache(): void
    {
        Cache::forget('permissions_by_category');
        Cache::forget('spatie.permission.cache');
    }

    /**
     * Clear user-specific caches
     */
    private function clearUserCache(User $user): void
    {
        Cache::forget("user_permissions_{$user->id}");
        Cache::forget('spatie.permission.cache');
    }

    /**
     * Validate role permissions
     */
    public function validateRolePermissions(array $permissions): array
    {
        $validPermissions = Permission::whereIn('name', $permissions)->pluck('name')->toArray();
        $invalidPermissions = array_diff($permissions, $validPermissions);

        return [
            'valid' => $validPermissions,
            'invalid' => $invalidPermissions,
        ];
    }

    /**
     * Get role suggestions based on permissions
     */
    public function getRoleSuggestions(array $permissions): array
    {
        $roles = Role::with('permissions')->get();
        $suggestions = [];

        foreach ($roles as $role) {
            $rolePermissions = $role->permissions->pluck('name')->toArray();
            $matchingPermissions = array_intersect($permissions, $rolePermissions);
            $matchPercentage = count($rolePermissions) > 0
                ? (count($matchingPermissions) / count($rolePermissions)) * 100
                : 0;

            if ($matchPercentage > 50) {
                $suggestions[] = [
                    'role' => $role,
                    'match_percentage' => round($matchPercentage, 1),
                    'matching_permissions' => $matchingPermissions,
                ];
            }
        }

        return collect($suggestions)->sortByDesc('match_percentage')->values()->toArray();
    }

    /**
     * Bulk assign roles to users
     */
    public function bulkAssignRoles(array $userIds, array $roleNames): array
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        DB::beginTransaction();

        try {
            $users = User::whereIn('id', $userIds)->get();

            foreach ($users as $user) {
                try {
                    $user->syncRoles($roleNames);
                    $results['success']++;
                    $this->clearUserCache($user);
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "User {$user->email}: " . $e->getMessage();
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return $results;
    }
}
