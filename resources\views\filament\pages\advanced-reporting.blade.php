<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Section - System Health Style -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400">
                        <x-heroicon-o-chart-bar class="w-8 h-8" />
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Advanced Analytics</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Comprehensive business intelligence & insights</p>
                    </div>
                </div>

                <!-- Export Actions -->
                <div class="flex items-center space-x-3">
                    <button wire:click="exportReport('pdf')" class="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200">
                        <x-heroicon-o-document-arrow-down class="h-4 w-4 mr-2" />
                        PDF Export
                    </button>
                    <button wire:click="exportReport('excel')" class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200">
                        <x-heroicon-o-table-cells class="h-4 w-4 mr-2" />
                        Excel Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters Section - System Health Style -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <x-heroicon-o-funnel class="w-5 h-5 inline mr-2" />
                Report Filters
            </h3>
            {{ $this->form }}
        </div>

        @if($reportData)
            <!-- KPI Dashboard - System Health Style -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($this->getKpiMetrics() as $key => $metric)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                                {{ $metric['label'] }}
                            </h3>
                            <div class="p-2 rounded-full {{ $metric['color'] === 'success' ? 'bg-green-100 text-green-600' : ($metric['color'] === 'danger' ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600') }}">
                                <x-heroicon-o-currency-dollar class="w-5 h-5" />
                            </div>
                        </div>

                        <div class="space-y-2">
                            <p class="text-3xl font-bold {{ $metric['color'] === 'success' ? 'text-green-600' : ($metric['color'] === 'danger' ? 'text-red-600' : 'text-blue-600') }}">
                                {{ $metric['value'] }}
                            </p>

                            @if($metric['growth'] !== null)
                                <div class="flex items-center text-sm">
                                    @if($metric['growth'] > 0)
                                        <x-heroicon-o-arrow-trending-up class="h-4 w-4 text-green-500 mr-1" />
                                        <span class="text-green-600 font-medium">+{{ $metric['growth'] }}%</span>
                                    @else
                                        <x-heroicon-o-arrow-trending-down class="h-4 w-4 text-red-500 mr-1" />
                                        <span class="text-red-600 font-medium">{{ $metric['growth'] }}%</span>
                                    @endif
                                    <span class="text-gray-500 dark:text-gray-400 ml-1">vs last period</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Charts Section - System Health Style -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Revenue Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <x-heroicon-o-chart-bar class="w-5 h-5 inline mr-2" />
                        Revenue Trend
                    </h3>
                    <div class="h-80 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                        <canvas id="revenueChart" wire:ignore></canvas>
                    </div>
                </div>

                <!-- Invoice Status Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <x-heroicon-o-document-check class="w-5 h-5 inline mr-2" />
                        Invoice Status Distribution
                    </h3>
                    <div class="h-80 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                        <canvas id="invoiceStatusChart" wire:ignore></canvas>
                    </div>
                </div>

                <!-- Payment Methods Chart -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <x-heroicon-o-credit-card class="w-5 h-5 inline mr-2" />
                        Payment Methods
                    </h3>
                            <div class="px-3 py-1 bg-purple-500/20 backdrop-blur-sm rounded-full border border-purple-300/30">
                                <span class="text-xs font-bold text-purple-700 dark:text-purple-300">ANALYTICS</span>
                            </div>
                        </div>
                        <div class="h-80 bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-700/20 p-4">
                            <canvas id="paymentMethodsChart" wire:ignore></canvas>
                        </div>
                    </div>
                </div>

                <!-- Top Clients -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Clients by Revenue</h3>
                    <div class="space-y-4">
                        @foreach($this->getTopClientsData() as $client)
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $client['name'] }}</p>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $client['percentage'] }}%"></div>
                                    </div>
                                </div>
                                <div class="ml-4 text-right">
                                    <p class="text-sm font-semibold text-gray-900 dark:text-white">${{ $client['revenue'] }}</p>
                                    <p class="text-xs text-gray-500">{{ $client['percentage'] }}%</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Detailed Tables Section -->
            @if(isset($reportData['invoices']['aging_analysis']))
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Invoice Aging Analysis</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Age Range</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Count</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @php
                                    $agingData = [
                                        'current' => ['label' => 'Current (Not Due)', 'color' => 'green'],
                                        '1_30_days' => ['label' => '1-30 Days Overdue', 'color' => 'yellow'],
                                        '31_60_days' => ['label' => '31-60 Days Overdue', 'color' => 'orange'],
                                        '61_90_days' => ['label' => '61-90 Days Overdue', 'color' => 'red'],
                                        'over_90_days' => ['label' => 'Over 90 Days Overdue', 'color' => 'red'],
                                    ];
                                @endphp
                                @foreach($agingData as $key => $info)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ $info['label'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{{ $reportData['invoices']['aging_analysis'][$key] ?? 0 }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                {{ $info['color'] === 'green' ? 'bg-green-100 text-green-800' : 
                                                   ($info['color'] === 'yellow' ? 'bg-yellow-100 text-yellow-800' : 
                                                   ($info['color'] === 'orange' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800')) }}">
                                                {{ $info['color'] === 'green' ? 'Good' : 'Attention Needed' }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            <!-- Performance Insights - System Health Style -->
            @if($reportData)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Insights</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        @if(isset($reportData['invoices']['average_payment_time']))
                            <div class="text-center">
                                <p class="text-2xl font-bold text-blue-600">{{ $reportData['invoices']['average_payment_time'] }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Average Payment Time (Days)</p>
                            </div>
                        @endif

                        @if(isset($reportData['clients']['client_retention_rate']))
                            <div class="text-center">
                                <p class="text-2xl font-bold text-green-600">{{ $reportData['clients']['client_retention_rate'] }}%</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Client Retention Rate</p>
                            </div>
                        @endif

                        @if(isset($reportData['revenue']['average_invoice_value']))
                            <div class="text-center">
                                <p class="text-2xl font-bold text-purple-600">${{ number_format($reportData['revenue']['average_invoice_value'], 2) }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Average Invoice Value</p>
                            </div>
                        @endif

                        <div class="text-center">
                            <p class="text-2xl font-bold text-orange-600">{{ $reportData['total_invoices'] ?? 0 }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Total Invoices</p>
                        </div>
                    </div>
                </div>
            @else
                <!-- No Data Message - System Health Style -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
                    <x-heroicon-o-chart-bar class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-600 dark:text-gray-400">No report data available. Please select filters and generate a report.</p>
                </div>
            @endif
        @endif

        {{-- Inline Scripts to avoid multiple root elements --}}
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeCharts();
            });

            document.addEventListener('livewire:navigated', function() {
                initializeCharts();
            });

        Livewire.on('refresh-charts', function() {
            setTimeout(() => {
                initializeCharts();
            }, 100);
        });

        function initializeCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart');
            if (revenueCtx) {
                if (window.revenueChart) {
                    window.revenueChart.destroy();
                }
                
                const revenueData = @json($this->getRevenueChartData());
                
                window.revenueChart = new Chart(revenueCtx, {
                    type: 'line',
                    data: revenueData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                position: 'left',
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                grid: {
                                    drawOnChartArea: false,
                                },
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                            }
                        }
                    }
                });
            }

            // Invoice Status Chart
            const statusCtx = document.getElementById('invoiceStatusChart');
            if (statusCtx) {
                if (window.statusChart) {
                    window.statusChart.destroy();
                }
                
                const statusData = @json($this->getInvoiceStatusChartData());
                
                window.statusChart = new Chart(statusCtx, {
                    type: 'doughnut',
                    data: statusData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'bottom',
                            }
                        }
                    }
                });
            }

            // Payment Methods Chart
            const paymentCtx = document.getElementById('paymentMethodsChart');
            if (paymentCtx) {
                if (window.paymentChart) {
                    window.paymentChart.destroy();
                }
                
                const paymentData = @json($this->getPaymentMethodsChartData());
                
                window.paymentChart = new Chart(paymentCtx, {
                    type: 'pie',
                    data: paymentData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'bottom',
                            }
                        }
                    }
                });
            }
        }
        </script>
    </div>
</x-filament-panels::page>
