<?php

namespace App\Services;

use App\Exports\ReportsExport;
use App\Exports\FinancialReportExport;
use App\Exports\ClientPerformanceExport;
use App\Exports\ProductAnalyticsExport;
use App\Exports\InvoiceAnalyticsExport;
use App\Exports\OverdueInvoicesExport;
use App\Exports\TaxSummaryExport;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class ExportService
{
    protected ReportingService $reportingService;

    public function __construct(ReportingService $reportingService)
    {
        $this->reportingService = $reportingService;
    }

    /**
     * Export financial report to Excel
     */
    public function exportFinancialReportExcel(Carbon $startDate, Carbon $endDate): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        try {
            $financialData = $this->reportingService->getFinancialSummary($startDate, $endDate);

            return Excel::download(
                new FinancialReportExport($financialData, $startDate->format('Y-m-d'), $endDate->format('Y-m-d')),
                'financial-report-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.xlsx'
            );
        } catch (\Exception $e) {
            \Log::error('Financial Report Excel export error: ' . $e->getMessage());
            throw new \Exception('Failed to export financial report: ' . $e->getMessage());
        }
    }

    /**
     * Export client performance to Excel
     */
    public function exportClientPerformanceExcel(Carbon $startDate, Carbon $endDate): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $clientData = $this->reportingService->getClientPerformance($startDate, $endDate)->toArray();
        
        return Excel::download(
            new ClientPerformanceExport($clientData, $startDate->format('Y-m-d'), $endDate->format('Y-m-d')),
            'client-performance-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Export product analytics to Excel
     */
    public function exportProductAnalyticsExcel(Carbon $startDate, Carbon $endDate): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $productData = $this->reportingService->getProductPerformance($startDate, $endDate)->toArray();
        
        return Excel::download(
            new ProductAnalyticsExport($productData, $startDate->format('Y-m-d'), $endDate->format('Y-m-d')),
            'product-analytics-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Export overdue invoices to Excel
     */
    public function exportOverdueInvoicesExcel(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $overdueData = $this->reportingService->getOverdueInvoices()->toArray();
        
        return Excel::download(
            new OverdueInvoicesExport($overdueData),
            'overdue-invoices-' . Carbon::now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Export tax summary to Excel
     */
    public function exportTaxSummaryExcel(Carbon $startDate, Carbon $endDate): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $taxData = $this->reportingService->getTaxSummary($startDate, $endDate);
        
        return Excel::download(
            new TaxSummaryExport($taxData, $startDate->format('Y-m-d'), $endDate->format('Y-m-d')),
            'tax-summary-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Generate financial report PDF
     */
    public function generateFinancialReportPDF(Carbon $startDate, Carbon $endDate): string
    {
        try {
            $data = [
                'summary' => $this->reportingService->getFinancialSummary($startDate, $endDate),
                'revenue_trends' => $this->reportingService->getRevenueTrends($startDate, $endDate),
                'tax_summary' => $this->reportingService->getTaxSummary($startDate, $endDate),
                'payment_methods' => $this->reportingService->getPaymentMethodAnalysis($startDate, $endDate),
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                'company_name' => getSettingValue('company_name') ?? 'DCF - Digital Clearing and Forwarding Agency',
                'report_title' => 'Financial Report',
            ];

            $pdf = Pdf::loadView('reports.financial-report-pdf', $data);
            $pdf->setPaper('A4', 'portrait');

            return $pdf->output();
        } catch (\Exception $e) {
            \Log::error('Financial Report PDF generation error: ' . $e->getMessage());
            throw new \Exception('Failed to generate financial report PDF: ' . $e->getMessage());
        }
    }

    /**
     * Generate client performance PDF
     */
    public function generateClientPerformancePDF(Carbon $startDate, Carbon $endDate): string
    {
        $clients = $this->reportingService->getClientPerformance($startDate, $endDate);
        
        $data = [
            'clients' => $clients->toArray(),
            'summary_stats' => [
                'total_clients' => $clients->count(),
                'total_revenue' => $clients->sum('total_invoice_amount'),
                'total_outstanding' => $clients->sum('outstanding_amount'),
                'avg_payment_ratio' => $clients->avg('payment_ratio'),
            ],
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            'company_name' => 'DCF - Digital Clearing and Forwarding Agency',
            'report_title' => 'Client Performance Report',
        ];

        $pdf = Pdf::loadView('reports.client-performance-pdf', $data);
        $pdf->setPaper('A4', 'portrait');
        
        return $pdf->output();
    }

    /**
     * Generate product analytics PDF
     */
    public function generateProductAnalyticsPDF(Carbon $startDate, Carbon $endDate): string
    {
        $products = $this->reportingService->getProductPerformance($startDate, $endDate);
        
        $data = [
            'products' => $products->toArray(),
            'summary_stats' => [
                'total_products' => $products->count(),
                'total_revenue' => $products->sum('total_amount'),
                'total_quantity' => $products->sum('total_quantity'),
                'avg_price' => $products->avg('avg_price'),
            ],
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            'company_name' => 'DCF - Digital Clearing and Forwarding Agency',
            'report_title' => 'Product Analytics Report',
        ];

        $pdf = Pdf::loadView('reports.product-analytics-pdf', $data);
        $pdf->setPaper('A4', 'portrait');
        
        return $pdf->output();
    }

    /**
     * Generate invoice analytics PDF
     */
    public function generateInvoiceAnalyticsPDF(Carbon $startDate, Carbon $endDate): string
    {
        $data = [
            'status_distribution' => $this->reportingService->getInvoiceStatusDistribution($startDate, $endDate),
            'overdue_analysis' => $this->reportingService->getOverdueInvoices(),
            'payment_trends' => $this->reportingService->getRevenueTrends($startDate, $endDate),
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
            'company_name' => 'DCF - Digital Clearing and Forwarding Agency',
            'report_title' => 'Invoice Analytics Report',
        ];

        $pdf = Pdf::loadView('reports.invoice-analytics-pdf', $data);
        $pdf->setPaper('A4', 'portrait');
        
        return $pdf->output();
    }

    /**
     * Export data to CSV format
     */
    public function exportToCSV(string $reportType, Carbon $startDate, Carbon $endDate): string
    {
        return $this->reportingService->exportToCSV($reportType, $startDate, $endDate);
    }

    /**
     * Get available export formats
     */
    public function getAvailableFormats(): array
    {
        return [
            'pdf' => 'PDF Document',
            'excel' => 'Excel Spreadsheet',
            'csv' => 'CSV File',
        ];
    }

    /**
     * Get available report types
     */
    public function getAvailableReportTypes(): array
    {
        return [
            'financial' => 'Financial Report',
            'clients' => 'Client Performance',
            'products' => 'Product Analytics',
            'invoices' => 'Invoice Analytics',
            'overdue' => 'Overdue Invoices',
            'tax' => 'Tax Summary',
        ];
    }
}
