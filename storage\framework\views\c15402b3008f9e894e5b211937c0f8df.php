<!DOCTYPE HTML>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <link rel="icon" href="<?php echo e(asset('web/media/logos/favicon.ico')); ?>" type="image/png">
    <title><?php echo e(__('messages.invoice.invoice_pdf')); ?></title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/invoice-pdf.css')); ?>" rel="stylesheet" type="text/css" />
    <style>
      * {
          font-family: <PERSON>ja<PERSON><PERSON>, <PERSON><PERSON>, "Helvetica", <PERSON><PERSON>, "Liberation Sans", sans-serif;
          line-height: 1.5;
          margin: 0;
          padding: 0;
      }

      table {
        width: 100%;
        border-collapse: collapse;
      }

      @page {
          margin-top: 40px !important;
      }
      .w-100{
        width:100%;
      }

      <?php if(getInvoiceCurrencyIcon($invoice->currency_id) == '€'): ?>
          .euroCurrency {
              font-family: Arial, "Helvetica", Arial, "Liberation Sans", sans-serif;
          }
      <?php endif; ?>

      @import url(https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap);
      @import url(https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap);
      @import url(https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap);
      .invoice-header {
        text-align: left;
        margin-top: 3rem;
        margin-bottom: 2rem;
      }

      .border-top {
        border-top: 1px solid #83b130 !important;
      }

      .border-bottom {
        border-bottom: 1px solid #83b130 !important;
      }

      .border-bottom-gray {
        border-bottom: 1px solid #c0c0c0 !important;
      }

      .border-0 {
        border: 0px solid white !important;
      }

      .product-table,
      .total-table {
        margin: 0;
      }
      .product-table tr th,
      .product-table tr td,
      .total-table tr th,
      .total-table tr td {
        border: 0px solid white !important;
        padding: 6px 0 !important;
      }

      .text-end {
        text-align: right !important;
      }

      .companylogo {
        text-align: left;
        margin: 0;
        padding: 0;
      }

      .invoice-header-inner {
        text-align: right;
      }

      .invoice-header-inner h3 {
        margin: 0;
        padding: 0;
      }

      .details-section {
        margin-bottom: 3rem;
      }

      .invoice-header p {
        color: #555;
        font-size: 16px;
        margin: 5px 0;
      }

      .text-color {
        color: #999999;
      }

      .invoice-date {
        padding: 15px 0;
        border-top: 1px solid #c0c0c0;
        border-right: 1px solid #c0c0c0;
        border-bottom: 1px solid #c0c0c0;
      }

      .billedto {
        padding: 15px 20px;
        border-top: 1px solid #c0c0c0;
        border-right: 1px solid #c0c0c0;
        border-bottom: 1px solid #c0c0c0;
      }

      .from {
        padding: 15px 20px;
        border-top: 1px solid #c0c0c0;
        border-left: 1px solid #c0c0c0;
        border-bottom: 1px solid #c0c0c0;
      }

      .notes-terms {
        margin-top: 3rem;
        padding: 0 15px;
      }

      .regards {
        margin-top: 2rem;
        padding: 0 15px;
      }

      body {
        font-family: "Lato", DejaVu Sans, sans-serif;
        padding: 30px;
        font-size: 14px;
      }

      .font-color-gray {
        color: #7a7a7a;
      }

      .main-heading {
        font-size: 34px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .header-right {
        text-align: right;
        vertical-align: top;
      }

      .logo,
      .company-name {
        margin-bottom: 8px;
        margin-left: 15px;
      }

      .font-weight-bold {
        font-weight: bold;
      }

      .address {
        margin-top: 60px;
      }

      .address tr:first-child td {
        padding-bottom: 10px;
      }

      .d-items-table {
        width: 100%;
        border: 0;
        border-collapse: collapse;
        margin-top: 40px;
      }

      .d-items-table thead {
        background: #2f353a;
        color: #fff;
      }

      .d-items-table td,
      .d-items-table th {
        padding: 8px;
        font-size: 14px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        vertical-align: top;
      }

      .d-invoice-footer {
        margin-top: 15px;
        width: 80%;
        float: right;
        text-align: right;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 40px;
      }

      .items-table td,
      .items-table th {
        padding: 8px;
        font-size: 14px;
        text-align: left;
        vertical-align: top;
      }

      .invoice-footer {
        margin-top: 15px;
        width: 100%;
        text-align: right;
      }

      .number-align {
        text-align: right !important;
      }

      .invoice-currency-symbol {
        font-family: "DejaVu Sans";
      }

      .vertical-align-top {
        vertical-align: text-top;
      }

      .tu {
        text-transform: uppercase;
      }

      .l-col-66 {
        width: 100%;
      }

      .thank {
        font-size: 45px;
        line-height: 1.2em;
        text-align: center;
        font-style: italic;
        padding-right: 25px;
      }

      .to-font-size {
        font-size: 15px;
      }

      .from-font-size {
        font-size: 15px;
      }

      .right-align {
        text-align: right !important;
      }

      .border-b {
        border-bottom: 1px solid #000000;
      }

      .border-t {
        border-top: 1px solid #000000;
      }

      .bg-black {
        background-color: #000000;
      }

      .bg-gray {
        background-color: #eaebec;
      }

      .bg-gray-100 {
        background-color: #f2f2f2;
      }

      .bg-danger {
        background-color: #d71920;
      }

      .bg-purple {
        background-color: #b57ebf;
      }

      .text-purple {
        color: #b57ebf;
      }

      .border-b-gray {
        border-bottom: 1px solid #bbbdbf;
      }

      .text-end {
        text-align: right !important;
      }

      .ps-5rem {
        padding-left: 5rem;
      }

      .header-section {
        position: relative !important;
        overflow: hidden;
      }
      .header-section::after {
        position: absolute;
        content: "";
        width: 116% !important;
        height: 100%;
        background-color: white;
        top: 0;
        left: -53px;
        transform: skew(35deg);
        z-index: 0;
      }
      .header-section table {
        position: relative;
        z-index: 2;
      }
      .header-section .invoice-text {
        position: relative !important;
      }
      .header-section .invoice-text::after {
        position: absolute;
        content: "";
        width: 26%;
        height: 100%;
        background-color: white;
        top: 0;
        left: 93%;
        transform: skew(35deg);
      }

      .istanbul-template {
        font-family: "Open Sans", sans-serif;
      }
      .istanbul-template strong {
        font-weight: bold;
      }
      .istanbul-template .invoice-header .heading-text {
        position: relative;
        z-index: 2;
      }
      .istanbul-template .invoice-table {
        border-bottom: 0.5px solid #c6c6c6;
        font-family: "Inter", sans-serif;
      }
      .istanbul-template .invoice-table thead {
        background-color: #fb5c3a;
        color: white;
      }
      .istanbul-template .bottom-line {
        height: 30px;
        width: 100%;
        background-color: #fb3f01;
        position: relative;
        overflow: hidden;
      }
      .istanbul-template .bottom-line::after {
        position: absolute;
        content: "";
        width: 62%;
        height: 100%;
        background-color: #0e1c45;
        top: 0;
        left: -15px;
        transform: skew(35deg);
        z-index: 0;
      }

      .font-gray-900 {
        color: #1a1c21 !important;
      }

      .font-gray-600 {
        color: #5e6470 !important;
      }

      .font-orange {
        color: #fb3f01;
      }

      .border-top-gray {
        border-top: 1px solid #c6c6c6;
      }

      .z-10 {
        z-index: 10;
      }

      .px-10 {
        padding-left: 40px;
        padding-right: 40px;
      }

      .h-25px {
        height: 20px;
      }

      .h-125px {
        height: 100px;
      }

      .mumbai-template {
        font-family: "Open Sans", sans-serif;
        background-color: #000 !important;
      }
      .mumbai-template .top-border {
        width: 100%;
        height: 10px;
        background-color: #3f478b;
      }
      .mumbai-template .bottom-border {
        width: 100%;
        height: 15px;
        background-color: #3f478b;
      }
      .mumbai-template .heading-text {
        background-color: #3f478b;
      }
      .mumbai-template .invoice-table {
        font-family: "Inter", sans-serif;
        border-bottom: 0.5px solid #c6c6c6;
      }
      .mumbai-template .invoice-table thead {
        background-color: #3f478b;
        color: white;
      }
      .mumbai-template .invoice-table thead th:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      .mumbai-template .invoice-table thead th:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .mumbai-template .invoice-table tbody tr:nth-child(even) {
        background-color: #ededed;
      }
      .mumbai-template .total-amount {
        background-color: #3f478b;
        color: white;
        border-radius: 6px;
      }
      .mumbai-template .total-amount td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      .mumbai-template .total-amount td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }

      .text-indigo {
        color: #3f478b;
      }

      .hongkong-template {
        font-family: "Open Sans", sans-serif;
        font-size: 12px;
        font-weight: medium;
      }
      .hongkong-template strong {
        font-weight: bold;
      }
      .hongkong-template .invoice-header .heading-text {
        position: relative;
      }
      .hongkong-template .invoice-header .heading-text h1 {
        color: #008fff;
        position: relative;
      }
      .hongkong-template .invoice-table {
        font-family: "Inter", sans-serif;
        border-bottom: 0.5px solid #c6c6c6;
      }
      .hongkong-template .invoice-table thead {
        background-color: #008fff;
        color: white;
        border: 1px solid transparent;
        overflow: hidden;
      }
      .hongkong-template .invoice-table thead th:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      .hongkong-template .invoice-table thead th:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .hongkong-template .invoice-table tbody tr:nth-child(even) {
        background-color: #ededed;
      }
      .hongkong-template .total-amount {
        background-color: #008fff;
        color: white;
        border-radius: 6px;
      }
      .hongkong-template .total-amount td:first-child {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      .hongkong-template .total-amount td:last-child {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }

      .text-yellow {
        color: #f18a1b;
      }

      .text-gray-600 {
        color: #6d6e70;
      }

      .tokyo-template {
        font-family: "Open Sans", sans-serif;
      }
      .tokyo-template strong {
        font-weight: bold;
      }
      .tokyo-template .heading-text h1 {
        font-size: 36px;
        font-weight: 400;
        letter-spacing: 4px;
      }
      @media (max-width: 424px) {
        .tokyo-template .heading-text h1 {
          font-size: 24px;
        }
      }
      .tokyo-template .invoice-table thead {
        text-transform: uppercase;
        background-color: #363b45;
        color: white;
      }
      .tokyo-template .invoice-table thead tr th {
        padding: 10px;
      }
      .tokyo-template .invoice-table tbody tr td {
        border-bottom: 0.5px solid #bbbdbf;
        padding: 10px;
      }
      .tokyo-template .invoice-table tbody tr td:nth-child(1) {
        width: 5%;
      }
      .tokyo-template .invoice-table tbody tr td:nth-child(2) {
        width: 60%;
      }
      .tokyo-template .invoice-table tbody tr td:nth-child(1), .tokyo-template .invoice-table tbody tr td:nth-child(4), .tokyo-template .invoice-table tbody tr td:nth-child(6) {
        background-color: #eaebec;
      }
      .tokyo-template .total-amount {
        border-top: 1px solid #363b45;
      }

      .font-dark-gray {
        color: #363b45;
      }

      .paris-template {
        font-family: "Inter", sans-serif;
        font-size: 12px;
      }
      .paris-template .heading-text {
        padding: 0;
      }
      .paris-template .heading-text h1 {
        font-size: 36px;
        font-weight: 700;
        letter-spacing: 4px;
        display: inline-block;
      }
      @media (max-width: 496px) {
        .paris-template .heading-text h1 {
          font-size: 24px;
        }
      }
      @media (max-width: 424px) {
        .paris-template .heading-text h1 {
          font-size: 24px;
        }
      }
      .paris-template .invoice-table {
        font-family: "Inter", sans-serif;
        border-bottom: 0.5px solid #c6c6c6;
      }
      .paris-template .invoice-table thead {
        background-color: #fab806;
        color: white;
      }
      .paris-template .invoice-table tbody tr:nth-child(even) {
        background-color: #ededed;
      }

      .p-10px {
        padding: 10px;
      }

      .font-black-900 {
        color: #242424;
      }

      .fw-6 {
        font-weight: bolder;
      }

      .text-yellow-500 {
        color: #fab806;
      }

      .text-green {
        color: #9dc23b;
      }

      .bg-light {
        background-color: #f8f9fa;
      }

      .img-logo {
        max-width: 100px;
        max-height: 66px;
      }
      @media (max-width: 424px) {
        .img-logo {
          max-width: 75px;
        }
      }

      .w-10 {
        width: 10%;
      }

      .w-30 {
        width: 30% !important;
      }

      .py-10 {
        padding-top: 2.5rem !important;
        padding-bottom: 2.5rem !important;
      }


      .py-2 {
        /* padding-top: 15px !important; */
        padding-bottom: 10px !important;
      }

      .pe-10 {
        padding-right: 2.5rem !important;
      }

      .ps-sm-10 {
        padding-left: 2.5rem !important;
      }

      .fs-5 {
        font-size: 0.938rem !important;
      }
      .my-4{
        margin-top: 1.5rem !important;
      }
      .pt-2{
        padding-top: 20px !important;
      }
      .pt-1{
        padding-top: 10px !important;
      }
      .py-1{
        padding-top: 5px !important;
        padding-bottom: 5px !important;
      }
      .px-2{
        padding-left: 8px !important;
        padding-right: 8px !important;
      }
      .p-2{
        padding: 10px !important;
      }
      .mt-3{
        margin-top: 20px !important;
      }
      .text-start{
        text-align: left !important;
      }
      .pb-2{
        padding-bottom: 10px !important;
      }
      .mt-5{
        margin-top: 40px !important;
      }
      h2{
        font-size: 16px !important;
      }
    </style>
</head>

<body style="padding: 0rem 0rem !important;">
    <div class="preview-main client-preview hongkong-template">
        <div class="d" id="boxes">
            <div class="d-inner">
                <div class="position-relative" style="padding:0 1.5rem;">
                    <div class="bg-img" style="position:absolute; right:0; top:-40px;">
                        <img src="<?php echo e(public_path('images/hongkong-bg-img.png')); ?>" alt="hongkong-bg-img" />
                    </div>
                    <div style="padding:0 1rem;">
                        <div class="invoice-header pt-10">
                            <table class="overflow-hidden w-100">
                                <tr>
                                    <td>
                                        <div class="pt-4">
                                            <img src="<?php echo e(getPDFLogoUrl()); ?>" class="img-logo" alt="logo">
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <table class="w-100">
                                <tr>
                                    <td class="heading-text">
                                        <div class="text-end">
                                            <h1 class="m-0"
                                                style="font-size: 32px; font-weight:700; letter-spacing:2px;color:<?php echo e($invoice_template_color); ?>;">
                                                <?php echo e(__('messages.common.invoice')); ?></h1>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="mt-3 overflow-auto">
                            <table class="w-100">
                                <tbody>
                                    <tr style="vertical-align:top;">
                                        <td width="40%" class="pe-3">
                                            <p class="mb-1 font-gray-900 fw-bold">
                                                <b><?php echo e(__('messages.common.to') . ':'); ?></b>
                                            </p>
                                            <?php if(!empty($client->company_name)): ?>
                                                <p class="mb-1 text-gray-600" style="white-space:nowrap;">
                                                    <b style="font-size: 14px; color: <?php echo e($invoice_template_color); ?>;"><?php echo e($client->company_name); ?></b>
                                                </p>
                                            <?php endif; ?>
                                            <p class="mb-0 text-gray-600" style="white-space:nowrap;">
                                                <?php echo e(__('messages.common.name') . ':'); ?>&nbsp;<span
                                                    class="font-gray-900"><?php echo e($client->user->full_name); ?></span></p>
                                            <p class="mb-0 text-gray-600" style="white-space:nowrap;">
                                                <?php echo e(__('messages.common.email') . ':'); ?>&nbsp;<span
                                                    class="font-gray-900"><?php echo e($client->user->email); ?></span></p>
                                            <p class="mb-0 text-gray-600">
                                                <?php echo e(__('messages.common.address') . ':'); ?>&nbsp;<span
                                                    class="font-gray-900"><?php echo e($client->address); ?>

                                                </span></p>
                                            <?php if(!empty($client->vat_no)): ?>
                                                <p class="mb-0 text-gray-600"><?php echo e(getVatNoLabel() . ':'); ?>&nbsp;<span
                                                        class="font-gray-900">&lt<?php echo e($client->vat_no); ?>&gt</span></p>
                                            <?php endif; ?>
                                        </td>
                                        <td width="30%" class="pe-3">
                                            <p class="mb-1 font-gray-900"><b><?php echo e(__('messages.common.from')); ?>:</b></p>
                                            <p class=" mb-1 font-color-gray fw-bold fs-6">
                                                <?php echo e(__('messages.common.name') . ':'); ?>&nbsp; <span
                                                    class="font-gray-900"><?php echo $setting['company_name']; ?></span></p>
                                            <p class="mb-0 text-gray-600">
                                                <?php echo e(__('messages.common.address') . ':'); ?>&nbsp;<span
                                                    class="font-gray-900"><?php echo $setting['company_address']; ?></span></p>
                                            <?php if(isset($setting['show_additional_address_in_invoice']) && $setting['show_additional_address_in_invoice'] == 1): ?>
                                                <p class="m-0 font-gray-900">
                                                    <?php echo e($setting['zipcode'] . ',' . $setting['city'] . ', ' . $setting['state'] . ', ' . $setting['country']); ?>

                                                </p>
                                            <?php endif; ?>
                                            <p class="mb-0 text-gray-600" style="white-space:nowrap;">
                                                <?php echo e(__('messages.user.phone') . ':'); ?>&nbsp;<span
                                                    class="font-gray-900"><?php echo e($setting['company_phone']); ?></span>
                                            </p>
                                            <?php if(isset($setting['show_additional_address_in_invoice']) && $setting['show_additional_address_in_invoice'] == 1): ?>
                                                <p class="mb-1 font-gray-600  fw-bold fs-6">
                                                    <?php echo e(__('messages.invoice.fax_no') . ':'); ?>&nbsp;<span
                                                        class="font-gray-900"><?php echo e($setting['fax_no']); ?></span>
                                                <p>
                                            <?php endif; ?>
                                            <?php if(!empty($setting['gst_no'])): ?>
                                                <p class="mb-1 font-gray-600 fw-bold fs-6">
                                                    <?php echo e(getVatNoLabel() . ':'); ?>&nbsp;<span
                                                        class="font-gray-900"><?php echo e($setting['gst_no']); ?></span>
                                                <p>
                                            <?php endif; ?>
                                        </td>
                                        <td width="30%" class="text-end">
                                            <p class="mb-0 fw-6" style="white-space:nowrap;"><span
                                                    class="font-gray-900"><b><?php echo e(__('messages.invoice.invoice_id') . ':'); ?></b>
                                                </span><span class="font-gray-600">#<?php echo e($invoice->invoice_id); ?></span>
                                            </p>
                                            <p class="mb-0 fw-6" style="white-space:nowrap;"><span
                                                    class="font-gray-900"><b><?php echo e(__('messages.invoice.invoice_date') . ':'); ?></b>
                                                </span><span
                                                    class="font-gray-600"><?php echo e(\Carbon\Carbon::parse($invoice->invoice_date)->translatedFormat(currentDateFormat())); ?></span>
                                            </p>
                                            <p class="mb-0 fw-6" style="white-space:nowrap;"><span
                                                    class="font-gray-900"><b><?php echo e(__('messages.invoice.due_date') . ':'); ?></b>
                                                </span><span
                                                    class="font-gray-600"><?php echo e(\Carbon\Carbon::parse($invoice->due_date)->translatedFormat(currentDateFormat())); ?>

                                                </span>
                                            </p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        
                        <?php if($invoice->shipment_weight || $invoice->transportation_type || $invoice->origin_location || $invoice->destination_location || $invoice->package_quantity || $invoice->tracking_number): ?>
                        <div class="overflow-auto mt-3">
                            <table class="invoice-table w-100" style="margin-bottom: 20px;">
                                <thead style="background-color: <?php echo e($invoice_template_color); ?>">
                                    <tr>
                                        <th colspan="4" class="py-2 px-2 fw-6 fs-5 text-center" style="font-weight: bold;">
                                            🚚 <?php echo e(__('Shipment Information')); ?>

                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <?php if($invoice->shipment_weight): ?>
                                        <td class="p-10px font-gray-600"><strong><?php echo e(__('Weight')); ?>:</strong> <?php echo e($invoice->shipment_weight); ?> <?php echo e($invoice->shipment_weight_unit ?? 'kg'); ?></td>
                                        <?php endif; ?>
                                        <?php if($invoice->transportation_type): ?>
                                        <td class="p-10px font-gray-600"><strong><?php echo e(__('Transportation')); ?>:</strong> <?php echo e(ucfirst($invoice->transportation_type)); ?></td>
                                        <?php endif; ?>
                                        <?php if($invoice->package_quantity): ?>
                                        <td class="p-10px font-gray-600"><strong><?php echo e(__('Packages')); ?>:</strong> <?php echo e($invoice->package_quantity); ?></td>
                                        <?php endif; ?>
                                        <?php if(!$invoice->shipment_weight && !$invoice->transportation_type && !$invoice->package_quantity): ?>
                                        <td class="p-10px font-gray-600"></td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php if($invoice->origin_location || $invoice->destination_location): ?>
                                    <tr>
                                        <?php if($invoice->origin_location): ?>
                                        <td class="p-10px font-gray-600"><strong><?php echo e(__('Origin')); ?>:</strong> <?php echo e($invoice->origin_location); ?></td>
                                        <?php endif; ?>
                                        <?php if($invoice->destination_location): ?>
                                        <td class="p-10px font-gray-600"><strong><?php echo e(__('Destination')); ?>:</strong> <?php echo e($invoice->destination_location); ?></td>
                                        <?php endif; ?>
                                        <?php if($invoice->tracking_number): ?>
                                        <td class="p-10px font-gray-600" colspan="2"><strong><?php echo e(__('Tracking')); ?>:</strong> <?php echo e($invoice->tracking_number); ?></td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>

                        <div class="overflow-auto mt-3">
                            <table class="invoice-table w-100">
                                <thead style="background-color: <?php echo e($invoice_template_color); ?>">
                                    <tr>
                                        <th class="py-1 px-2 fw-6 fs-5 text-start">#</th>
                                        <th class="fw-6  fs-5 in-w-2 text-start" width="35%"><?php echo e(__('Service')); ?></th>
                                        <th class="fw-6 fs-5 text-center"><?php echo e(__('messages.invoice.qty')); ?></th>
                                        <th class="fw-6 px-2 fs-5 text-center text-nowrap">
                                            <?php echo e(__('messages.product.unit_price')); ?></th>
                                        <th class="fw-6 fs-5 text-center text-nowrap">
                                            <?php echo e(__('messages.invoice.tax') . '(in %)'); ?></th>
                                        <th class="fw-6 px-2 fs-5 text-end text-nowrap">
                                            <?php echo e(__('messages.invoice.amount')); ?>

                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if(!empty($invoice)): ?>
                                        <?php $__currentLoopData = $invoice->invoiceItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $invoiceItems): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="p-10px font-gray-900 fw-6"><span><?php echo e($key + 1); ?></span>
                                                </td>
                                                <td class="p-10px font-gray-600 in-w-2">
                                                    <p class="fw-6 mb-0 font-gray-900">
                                                        <?php if(isset($invoiceItems->service->name)): ?>
                                                            <?php echo e($invoiceItems->service->name); ?>

                                                        <?php elseif(isset($invoiceItems->product->name)): ?>
                                                            <?php echo e($invoiceItems->product->name); ?>

                                                        <?php else: ?>
                                                            <?php echo e($invoiceItems->product_name ?? __('messages.common.n/a')); ?>

                                                        <?php endif; ?>
                                                    </p>
                                                    <?php if(!empty($invoiceItems->description)): ?>
                                                        <span style="font-size: 12px; word-break: break-all; color: #666;"><?php echo e($invoiceItems->description); ?></span>
                                                    <?php elseif(
                                                        !empty($invoiceItems->service->description) &&
                                                            (isset($setting['show_product_description']) && $setting['show_product_description'] == 1)): ?>
                                                        <span style="font-size: 12px; word-break: break-all !important"><?php echo e($invoiceItems->service->description); ?></span>
                                                    <?php elseif(
                                                        !empty($invoiceItems->product->description) &&
                                                            (isset($setting['show_product_description']) && $setting['show_product_description'] == 1)): ?>
                                                        <span style="font-size: 12px; word-break: break-all !important"><?php echo e($invoiceItems->product->description); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="p-10px font-gray-600 text-center">
                                                    <?php echo e(number_format($invoiceItems->quantity, 2)); ?></td>
                                                <td class="p-10px font-gray-600 text-center text-nowrap">
                                                    <?php echo e(isset($invoiceItems->price) ? getInvoiceCurrencyAmount($invoiceItems->price, $invoice->currency_id, true) : __('messages.common.n/a')); ?>

                                                </td>
                                                <td class="p-10px font-gray-600 text-center">
                                                    <?php $__currentLoopData = $invoiceItems->invoiceItemTax; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $keys => $tax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php echo e($tax->tax ?? '--'); ?>

                                                        <?php if(!$loop->last): ?>
                                                            ,
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </td>
                                                <td class="p-10px font-gray-600 text-end text-nowrap">
                                                    <?php echo e(isset($invoiceItems->total) ? getInvoiceCurrencyAmount($invoiceItems->total, $invoice->currency_id, true) : __('messages.common.n/a')); ?>

                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-5">
                            <table class="w-100">
                                <tr>
                                    <td style="vertical-align:bottom; width:60%;">
                                        <?php if(!empty($invoice->paymentQrCode)): ?>
                                            <img class="mt-4" src="<?php echo e($qrImg); ?>"
                                                height="100" width="100" alt="qr-code-image">
                                        <?php endif; ?>
                                    </td>
                                    <td style="vertical-align:top; width:40%;">
                                        <table class="w-100 border-0">
                                            <tbody>
                                                <tr>
                                                    <td class="text-nowrap font-gray-900 pb-2 px-2">
                                                        <strong><?php echo e(__('messages.invoice.sub_total') . ':'); ?></strong>
                                                    </td>
                                                    <td class="text-nowrap text-end font-gray-600 pb-2 px-2 fw-bold">
                                                        <?php echo e(getInvoiceCurrencyAmount($invoice->amount, $invoice->currency_id, true)); ?>

                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap font-gray-900 pb-2 px-2">
                                                        <strong><?php echo e(__('messages.invoice.discount') . ':'); ?></strong>
                                                    </td>
                                                    <td class="text-nowrap text-end font-gray-600 pb-2 px-2 fw-bold">
                                                        <?php if($invoice->discount == 0): ?>
                                                            <span><?php echo e(__('messages.common.n/a')); ?></span>
                                                        <?php else: ?>
                                                            <?php if(isset($invoice) && $invoice->discount_type == \App\Models\Invoice::FIXED): ?>
                                                                <span
                                                                    class="euroCurrency"><?php echo e(isset($invoice->discount) ? getInvoiceCurrencyAmount($invoice->discount, $invoice->currency_id, true) : __('messages.common.n/a')); ?></span>
                                                            <?php else: ?>
                                                                <?php echo e($invoice->discount); ?><span
                                                                    style="font-family: DejaVu Sans">&#37;</span>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <?php
                                                        $itemTaxesAmount = $invoice->amount + array_sum($totalTax);
                                                        $invoiceTaxesAmount =
                                                            ($itemTaxesAmount * $invoice->invoiceTaxes->sum('value')) /
                                                            100;
                                                        $totalTaxes = array_sum($totalTax) + $invoiceTaxesAmount;
                                                    ?>
                                                    <td class="text-nowrap font-gray-900 pb-2 px-2">
                                                        <strong><?php echo e(__('messages.invoice.tax') . ':'); ?></strong>
                                                    </td>
                                                    <td class="text-nowrap text-end font-gray-600 pb-2 px-2 fw-bold">
                                                        <?php echo numberFormat($totalTaxes) != 0
                                                            ? '<span class="euroCurrency">' . getInvoiceCurrencyAmount($totalTaxes, $invoice->currency_id, true) . '</span>'
                                                            : __('messages.common.n/a'); ?>

                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap font-gray-900 pb-2 px-2">
                                                        <strong><?php echo e(__('messages.invoice.total') . ':'); ?></strong>
                                                    </td>
                                                    <td class="text-end font-gray-600 pb-2 px-2 fw-bold">
                                                        <?php echo e(getInvoiceCurrencyAmount($invoice->final_amount, $invoice->currency_id, true)); ?>

                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tfoot class="total-amount"
                                                style="background-color: <?php echo e($invoice_template_color); ?>">
                                                <tr>
                                                    <td class="text-nowrap p-10px">
                                                        <strong><?php echo e(__('messages.admin_dashboard.total_due') . ':'); ?></strong>
                                                    </td>
                                                    <td class="text-nowrap text-end p-10px">
                                                        <strong><?php echo e(getInvoiceCurrencyAmount(getInvoiceDueAmount($invoice->id), $invoice->currency_id, true)); ?></strong>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="mt-5 pt-5">
                            <?php if(!empty($invoice->note)): ?>
                            <div class="mb-5 pt-10">
                                <h4 class="font-gray-900 mb5">
                                    <b><?php echo e(__('messages.client.notes') . ':'); ?></b>
                                </h4>
                                <p class="font-gray-600">
                                    <?php echo nl2br($invoice->note ?? __('messages.common.not_available')); ?>

                                </p>
                            </div>
                            <?php endif; ?>
                            <?php if(!empty($invoice->term)): ?>
                            <div class="w-75">
                                <h4 class="font-gray-900 mb5">
                                    <b><?php echo e(__('messages.invoice.terms') . ':'); ?></b>
                                </h4>
                                <p class="font-gray-600 mb-0">
                                    <?php echo nl2br($invoice->term ?? __('messages.common.not_available')); ?>

                                </p>
                            </div>
                            <?php endif; ?>

                            
                            <?php if(!empty($invoice->description)): ?>
                                <div class="mb-4 w-100">
                                    <div class="p-3" style="background-color: #f8f9fa; border-left: 4px solid <?php echo e($invoice_template_color); ?>; border-radius: 4px;">
                                        <h4 class="mb-2 font-gray-900" style="color: <?php echo e($invoice_template_color); ?>;">
                                            <b><?php echo e(__('Invoice Description')); ?>:</b>
                                        </h4>
                                        <div class="font-gray-600" style="line-height: 1.6;">
                                            <?php echo nl2br(e($invoice->description)); ?>

                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="w-25 text-end">
                                <h2 class="mb-0" style="color:<?php echo e($invoice_template_color); ?>">
                                    <b><?php echo e(__('messages.setting.regards')); ?>:</b>
                                </h2>
                                <p class="mb-0"><?php echo e(html_entity_decode($setting['app_name'])); ?>

                                </p>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/invoices/invoice_template_pdf/hongKongTemplate.blade.php ENDPATH**/ ?>