<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add the missing 'total' column if it doesn't exist
            if (!Schema::hasColumn('invoices', 'total')) {
                $table->decimal('total', 15, 2)->default(0)->after('final_amount');
            }
            
            // Add other potentially missing columns
            if (!Schema::hasColumn('invoices', 'sub_total')) {
                $table->decimal('sub_total', 15, 2)->default(0)->after('amount');
            }
            
            if (!Schema::hasColumn('invoices', 'tax_amount')) {
                $table->decimal('tax_amount', 15, 2)->default(0)->after('sub_total');
            }
            
            // Ensure proper indexing
            $table->index(['status', 'created_at']);
            $table->index(['client_id', 'status']);
            $table->index(['due_date', 'status']);
        });

        // Update existing records to calculate total from final_amount
        DB::statement('UPDATE invoices SET total = COALESCE(final_amount, amount, 0) WHERE total = 0 OR total IS NULL');
        DB::statement('UPDATE invoices SET sub_total = COALESCE(amount, 0) WHERE sub_total = 0 OR sub_total IS NULL');
    }

    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn(['total', 'sub_total', 'tax_amount']);
            $table->dropIndex(['status', 'created_at']);
            $table->dropIndex(['client_id', 'status']);
            $table->dropIndex(['due_date', 'status']);
        });
    }
};
