<?php

namespace App\Filament\Widgets;

use App\Models\Client;
use App\Models\Invoice;
use App\Models\Service;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DashboardOverview extends BaseWidget
{

    protected static string $view = 'filament.widgets.dashboard';

    protected static ?int $sort = 1;

    protected int | string | array $columnSpan = 'full';

    // Enable polling for real-time updates (every 30 seconds)
    protected static ?string $pollingInterval = '30s';

    public static function canView(): bool
    {
        return auth()->user()->hasRole('admin');
    }
    protected function getViewData(): array
    {
        try {
            // Clear cache to ensure real-time data
            $cacheKey = 'dashboard_overview_' . auth()->id();
            \Cache::forget($cacheKey);

            // Get fresh data - use direct queries for real-time accuracy
            $totalInvoices = Invoice::where('status', '!=', Invoice::DRAFT)->count();
            $totalClients = Client::count();
            $totalProducts = Service::count();

            // Use direct database queries for real-time accuracy
            $paidInvoices = Invoice::where('status', Invoice::PAID)->count();
            $unpaidInvoices = Invoice::where('status', Invoice::UNPAID)->count();
            $partiallyPaidInvoices = Invoice::where('status', Invoice::PARTIALLY)->count();
            $overdueInvoices = Invoice::where('status', Invoice::OVERDUE)->count();
            $processingInvoices = Invoice::where('status', Invoice::PROCESSING)->count();

            return [
                'totalInvoices' => formatTotalAmount($totalInvoices),
                'totalClients' => formatTotalAmount($totalClients),
                'totalProducts' => formatTotalAmount($totalProducts),
                'paidInvoices' => formatTotalAmount($paidInvoices),
                'unpaidInvoices' => formatTotalAmount($unpaidInvoices),
                'partiallyPaidInvoices' => formatTotalAmount($partiallyPaidInvoices),
                'overdueInvoices' => formatTotalAmount($overdueInvoices),
                'processingInvoices' => formatTotalAmount($processingInvoices),
            ];
        } catch (\Exception $e) {
            \Log::error('DashboardOverview widget error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'totalInvoices' => formatTotalAmount(0),
                'totalClients' => formatTotalAmount(0),
                'totalProducts' => formatTotalAmount(0),
                'paidInvoices' => formatTotalAmount(0),
                'unpaidInvoices' => formatTotalAmount(0),
                'partiallyPaidInvoices' => formatTotalAmount(0),
                'overdueInvoices' => formatTotalAmount(0),
                'processingInvoices' => formatTotalAmount(0),
            ];
        }
    }
}
