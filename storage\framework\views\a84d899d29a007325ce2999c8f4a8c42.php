<!-- DCF UI/UX Fixes -->
<link rel="stylesheet" href="<?php echo e(asset('css/dcf-ui-fixes.css')); ?>">

<!-- DCF Dashboard Charts Enhancement -->
<script src="<?php echo e(asset('js/dashboard-charts.js')); ?>"></script>

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
    function copyURL(url) {
        const el = document.createElement('textarea');
        el.value = url;
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        new FilamentNotification()
            .title('URL Copied!')
            .icon('heroicon-o-clipboard-document-check')
            .iconColor('success')
            .send();
    }


    function razorPay(key, name, currency, amount, invoiceId, premail, prename) {
        let options = {
            key: key,
            amount: amount * 100,
            currency: currency,
            name: name,
            order_id: '',
            description: '',
            notes: {
                invoiceId: invoiceId
            },
            image: "<?php echo e(getLogoUrl()); ?>",
            callback_url: "<?php echo e(route('razorpay.success')); ?>",
            prefill: {
                email: premail,
                name: prename,
                invoiceId: invoiceId
            },
            readonly: {
                name: true,
                email: true
            },
            modal: {
                ondismiss: function() {
                    window.location.href = "<?php echo e(route('razorpay.failed')); ?>";
                }
            }
        };

        let razorPay = new Razorpay(options);
        razorPay.open();
        razorPay.on("payment.failed", function() {
            window.location.href = "<?php echo e(route('razorpay.failed')); ?>";
        });
    }

    //redirect to whatsapp
    document.addEventListener('open-whatsapp-link', function(event) {
        const url = event.detail[0];
        window.open(url, '_blank');
    });
</script>
<?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/layout/scripts.blade.php ENDPATH**/ ?>