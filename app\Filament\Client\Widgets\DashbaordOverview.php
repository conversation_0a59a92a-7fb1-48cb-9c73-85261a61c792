<?php

namespace App\Filament\Client\Widgets;

use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DashbaordOverview extends BaseWidget
{
    protected static string $view = 'client.widgets.dashboard';

    protected static ?int $sort = 1;

    protected int | string | array $columnSpan = 'full';

    // Enable polling for real-time updates (every 30 seconds)
    protected static ?string $pollingInterval = '30s';

    public static function canView(): bool
    {
        return auth()->user()->hasRole('client');
    }
    protected function getViewData(): array
    {
        try {
            $user = getLogInUser();

            // Add null check for user and client
            if (!$user || !$user->client) {
                throw new \Exception('User or client not found');
            }

            // Clear cache for real-time data
            $cacheKey = 'client_dashboard_' . $user->id;
            \Cache::forget($cacheKey);

            // Use direct queries for real-time accuracy
            $clientId = $user->client->id;

            $totalInvoices = Invoice::where('client_id', $clientId)
                ->where('status', '!=', Invoice::DRAFT)->count();
            $paidInvoices = Invoice::where('client_id', $clientId)
                ->where('status', Invoice::PAID)->count();
            $unpaidInvoices = Invoice::where('client_id', $clientId)
                ->where('status', Invoice::UNPAID)->count();
            $partiallyPaidInvoices = Invoice::where('client_id', $clientId)
                ->where('status', Invoice::PARTIALLY)->count();
            $overdueInvoices = Invoice::where('client_id', $clientId)
                ->where('status', Invoice::OVERDUE)->count();
            $processingInvoices = Invoice::where('client_id', $clientId)
                ->where('status', Invoice::PROCESSING)->count();

            // Calculate total amounts for better insights using direct queries
            $totalAmount = Invoice::where('client_id', $clientId)
                ->where('status', '!=', Invoice::DRAFT)->sum('final_amount');
            $paidAmount = Invoice::where('client_id', $clientId)
                ->where('status', Invoice::PAID)->sum('final_amount');
            $unpaidAmount = Invoice::where('client_id', $clientId)
                ->whereIn('status', [Invoice::UNPAID, Invoice::PARTIALLY, Invoice::OVERDUE])
                ->sum('final_amount');

            return [
                'totalInvoices' => formatTotalAmount($totalInvoices),
                'paidInvoices' => formatTotalAmount($paidInvoices),
                'unpaidInvoices' => formatTotalAmount($unpaidInvoices),
                'partiallyPaidInvoices' => formatTotalAmount($partiallyPaidInvoices),
                'overdueInvoices' => formatTotalAmount($overdueInvoices),
                'processingInvoices' => formatTotalAmount($processingInvoices),
                'totalAmount' => formatTotalAmount($totalAmount),
                'paidAmount' => formatTotalAmount($paidAmount),
                'unpaidAmount' => formatTotalAmount($unpaidAmount),
            ];
        } catch (\Exception $e) {
            \Log::error('Client DashboardOverview widget error: ' . $e->getMessage());

            // Return safe fallback data
            return [
                'totalInvoices' => formatTotalAmount(0),
                'paidInvoices' => formatTotalAmount(0),
                'unpaidInvoices' => formatTotalAmount(0),
                'partiallyPaidInvoices' => formatTotalAmount(0),
                'overdueInvoices' => formatTotalAmount(0),
                'processingInvoices' => formatTotalAmount(0),
                'totalAmount' => formatTotalAmount(0),
                'paidAmount' => formatTotalAmount(0),
                'unpaidAmount' => formatTotalAmount(0),
            ];
        }
    }
}
