<div class="preview-main client-preview pdf">
    <div class="d" id="boxes">
        <div>
            <div class="mb-8 p-5 bg-white-200">
                <table class="w-full">
                    <tr>
                        <td class="relative align-top" style="width: 50%">
                            <div class="logo-img w-full h-full">
                                <img src="<?php echo e(getLogoUrl()); ?>" class="img-logo h-full w-full" alt="logo">
                            </div>
                            <div class="absolute bottom-0 left-0 mb-5 qr-img w-full h-full">
                                <img class="mt-2 h-full w-full" src="<?php echo e(asset('images/qrcode.png')); ?>">
                            </div>
                        </td>
                        <td style="width: 50%;">
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th class="font-bold">
                                            <p class=" text-xl mb-4 text-start leading-12 fontColor" style="color: <?php echo e($invColor); ?>;"><strong><?php echo e(__('messages.common.invoice')); ?></strong></p>
                                        </th>
                                        <th class="">
                                            <p class=" text-xl mb-4 text-start leading-12 font-medium fontColor" style="color: <?php echo e($invColor); ?>;">
                                                #9CQ5X7</p>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <p class="m-0 font-bold text-sm text-black"><?php echo e(__('messages.invoice.invoice_date')); ?>

                                            </p>
                                            <p class="text-sm text-black mb-4">2022-01-01</p>
                                        </td>
                                        <td>
                                            <p class="m-0 font-bold text-sm text-black"><?php echo e(__('messages.invoice.due_date')); ?></p>
                                            <p class="text-sm text-black mb-4">2022-01-01</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-top">
                                            <span class="m-0 font-bold text-13 text-black"><strong><?php echo e(__('messages.common.from')); ?></strong></span><br>
                                            <span class="text-13 text-black"><?php echo e($companyName); ?></span><br>
                                            <span class="text-13 text-black"><?php echo e($companyAddress); ?></span><br>
                                            <span class="text-13 text-black"><?php echo e($companyPhone); ?></span><br>
                                            <span class="text-13 text-black"><?php echo e($gstNo); ?></span>
                                        </td>
                                        <td class="align-top">
                                            <span class="m-0 font-bold text-13"><strong><?php echo e(__('messages.common.to')); ?></strong></span><br>
                                            <span class="text-13 text-black">&lt;<?php echo e(__('messages.invoice.client_name')); ?>&gt;</span><br>
                                            <span class="text-13 text-black">&lt;<?php echo e(__('messages.invoice.client_email')); ?>&gt;</span><br>
                                            <span class="text-13 text-black">&lt;<?php echo e(__('messages.client_address')); ?>&gt;</span><br>
                                            <span class="text-13 text-black">&lt;<?php echo e(getVatNoLabel()); ?>&gt;</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="table-responsive-sm p-5">
                <table class="w-full">
                    <thead class="border-b borderColor" style="border-color: <?php echo e($invColor); ?>;">
                        <tr>
                            <th class="py-1 text-13 uppercase px-px fontColor" style="color: <?php echo e($invColor); ?>;"><strong>#</strong></th>
                            <th class="py-1 text-13 uppercase px-px text-start fontColor" style="color: <?php echo e($invColor); ?>;">
                                <strong><?php echo e(__('messages.item')); ?></strong>
                            </th>
                            <th class="py-1 text-13 uppercase px-px fontColor" style="color: <?php echo e($invColor); ?>;" ><strong><?php echo e(__('messages.invoice.qty')); ?></strong></th>
                            <th class="py-1 text-13 uppercase px-px text-cente text-nowrap fontColor" style="color: <?php echo e($invColor); ?>;">
                                <strong><?php echo e(__('messages.product.unit_price')); ?></strong>
                            </th>
                            <th class="py-1 text-13 uppercase px-px text-cente text-nowrap fontColor" style="color: <?php echo e($invColor); ?>;">
                                <strong><?php echo e(__('messages.invoice.tax') . '(in %)'); ?></strong>
                            </th>
                            <th class="py-1 text-13 uppercase px-px text-en text-nowrap fontColor" style="color: <?php echo e($invColor); ?>;">
                                <strong><?php echo e(__('messages.invoice.amount')); ?></strong>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="py-1 px-px text-13 align-center text-black"><span>1</span></td>
                            <td class="py-1 px-px text-13 align-center text-black">
                                <p class="font-bold mb-0"><?php echo e(__('messages.item')); ?> 1</p>
                                <?php echo e(__('messages.Description')); ?>

                            </td>
                            <td class="py-1 px-px text-13 align-center text-black">1</td>
                            <td class="py-1 px-px text-13 align-center text-black text-center text-nowrap"><?php echo e(getCurrencyAmount(100, true)); ?>

                            </td>
                            <td class="py-1 px-px text-13 align-center text-black text-center">N/A</td>
                            <td class="py-1 px-px text-13 align-center text-black text-end text-nowrap"><?php echo e(getCurrencyAmount(100,
                                true)); ?></td>
                        </tr>
                        <tr>
                            <td class="py-1 px-px text-13 align-center text-black"><span>2</span></td>
                            <td class="py-1 px-px text-13 align-center text-black">
                                <p class="font-bold mb-0"><?php echo e(__('messages.item')); ?> 2</p>
                                <?php echo e(__('messages.Description')); ?>

                            </td>
                            <td class="py-1 px-px text-13 align-center text-black">1</td>
                            <td class="py-1 px-px text-13 align-center text-black text-center text-nowrap"><?php echo e(getCurrencyAmount(100, true)); ?>

                            </td>
                            <td class="py-1 px-px text-13 align-center text-black text-center">N/A</td>
                            <td class="py-1 px-px text-13 align-center text-black text-end text-nowrap"><?php echo e(getCurrencyAmount(100,
                                true)); ?></td>
                        </tr>
                        <tr>
                            <td class="py-1 px-px text-13 align-center text-black"><span>3</span></td>
                            <td class="py-1 px-px text-13 align-center text-black">
                                <p class="font-bold mb-0"><?php echo e(__('messages.item')); ?> 3</p>
                                <?php echo e(__('messages.Description')); ?>

                            </td>
                            <td class="py-1 px-px text-13 align-center text-black">1</td>
                            <td class="py-1 px-px text-13 align-center text-black text-center text-nowrap"><?php echo e(getCurrencyAmount(100, true)); ?>

                            </td>
                            <td class="py-1 px-px text-13 align-center text-black text-center">N/A</td>
                            <td class="py-1 px-px text-13 align-center text-black text-end text-nowrap"><?php echo e(getCurrencyAmount(100,
                                true)); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <table class="ml-auto mb-5 mr-5 border-t borderColor" style="width: 50%;border-color: <?php echo e($invColor); ?>;">
                <tbody>
                    <tr>
                        <td class="py-1 px-px text-13 text-black">
                            <strong><?php echo e(__('messages.invoice.amount')); ?></strong>
                        </td>
                        <td class="text-end py-1 px-px text-13 text-black text-nowrap">
                            <?php echo e(getCurrencyAmount(300, true)); ?>

                        </td>
                    </tr>
                    <tr>
                        <td class="py-1 px-px text-13 text-black">
                            <strong><?php echo e(__('messages.invoice.discount')); ?></strong>
                        </td>
                        <td class="text-end py-1 px-px text-13 text-black text-nowrap">
                            <?php echo e(getCurrencyAmount(50, true)); ?>

                        </td>
                    </tr>
                    <tr>
                        <td class="font-bold py-1 px-px text-13 text-black">
                            <strong><?php echo e(__('messages.invoice.tax')); ?></strong>
                        </td>
                        <td class="text-end py-1 px-px text-13 text-black">
                            N/A
                        </td>
                    </tr>
                </tbody>
                <tfoot class="border-t borderColor" style="border-color: <?php echo e($invColor); ?>;">
                    <tr>
                        <td class="pt-2 px-px text-black text-13">
                            <strong><?php echo e(__('messages.invoice.total')); ?></strong>
                        </td>
                        <td class="text-end pt-2 px-px text-black text-13 text-nowrap">
                            <strong><?php echo e(getCurrencyAmount(250, true)); ?></strong>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="p-5">
                <div class="mb-8">
                    <h4 class="font-medium mb-5px text-base text-black"><?php echo e(__('messages.client.notes')); ?>:</h4>
                    <p class="text-gray-300 text-13 mb-4">
                        Paypal, Stripe & manual payment method accept. Net 10 – Payment due in 10 days from invoice
                        date. Net 30 – Payment due in 30 days from invoice date.
                    </p>
                </div>
                <div class="mb-8">
                    <h4 class="font-medium mb-5px text-base text-black"><?php echo e(__('messages.invoice.terms')); ?>:</h4>
                    <p class="text-gray-500 text-13 mb-4">
                        Invoice payment Total ; 1% 10 Net 30, 1% discount if payment received within 10 days otherwise
                        payment 30 days after invoice date.
                    </p>
                </div>
                <div>
                    <h5 class="font-bold mb-5px text-base text-black"><b><?php echo e(__('messages.setting.regards')); ?>:</b></h5>
                    <p class="font-bold text-13 mb-4 fontColor" style="color: <?php echo e($invColor); ?>;">
                        <b><?php echo e($companyName); ?></b>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH C:\xampp\htdocs\invoices_mod\resources\views/forms/components/invoiceTemplates/torontoTemplate.blade.php ENDPATH**/ ?>