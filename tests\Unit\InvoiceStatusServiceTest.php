<?php

namespace Tests\Unit;

use App\Models\Invoice;
use App\Services\InvoiceStatusService;
use PHPUnit\Framework\TestCase;

class InvoiceStatusServiceTest extends TestCase
{
    protected InvoiceStatusService $statusService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->statusService = new InvoiceStatusService();
    }

    /** @test */
    public function it_validates_status_transitions_correctly()
    {
        // Valid transitions
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::DRAFT, Invoice::UNPAID));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::UNPAID, Invoice::PAID));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::UNPAID, Invoice::PARTIALLY));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::UNPAID, Invoice::PROCESSING));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::PROCESSING, Invoice::PAID));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::PARTIALLY, Invoice::PAID));
        
        // Invalid transitions
        $this->assertFalse($this->statusService->isValidStatusTransition(Invoice::PAID, Invoice::DRAFT));
        $this->assertFalse($this->statusService->isValidStatusTransition(Invoice::DRAFT, Invoice::PAID));
        $this->assertFalse($this->statusService->isValidStatusTransition(Invoice::PAID, Invoice::PROCESSING));
    }

    /** @test */
    public function it_returns_correct_status_names()
    {
        $this->assertEquals('Draft', $this->statusService->getStatusName(Invoice::DRAFT));
        $this->assertEquals('Unpaid', $this->statusService->getStatusName(Invoice::UNPAID));
        $this->assertEquals('Paid', $this->statusService->getStatusName(Invoice::PAID));
        $this->assertEquals('Partially Paid', $this->statusService->getStatusName(Invoice::PARTIALLY));
        $this->assertEquals('Processing', $this->statusService->getStatusName(Invoice::PROCESSING));
        $this->assertEquals('Overdue', $this->statusService->getStatusName(Invoice::OVERDUE));
        $this->assertEquals('Unknown', $this->statusService->getStatusName(999));
    }

    /** @test */
    public function it_detects_overdue_invoices_correctly()
    {
        // Create a mock invoice with past due date
        $pastDueInvoice = new Invoice();
        $pastDueInvoice->due_date = '2023-01-01';
        
        $this->assertTrue($this->statusService->isOverdue($pastDueInvoice));
        
        // Create a mock invoice with future due date
        $futureDueInvoice = new Invoice();
        $futureDueInvoice->due_date = '2030-12-31';
        
        $this->assertFalse($this->statusService->isOverdue($futureDueInvoice));
    }
}
