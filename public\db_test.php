<?php
// Database connection test for web context
try {
    require_once '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    $connection = \Illuminate\Support\Facades\DB::connection();
    $pdo = $connection->getPdo();
    
    echo json_encode(['status' => 'success', 'connection' => get_class($pdo)]);
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
