<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ClientPerformanceExport implements FromArray, WithHeadings, WithStyles, WithTitle, ShouldAutoSize
{
    protected array $clientData;
    protected string $startDate;
    protected string $endDate;

    public function __construct(array $clientData, string $startDate, string $endDate)
    {
        $this->clientData = $clientData;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->clientData as $client) {
            $data[] = [
                $client['client_name'] ?? 'Unknown',
                $client['client_email'] ?? '<EMAIL>',
                $client['total_invoices'] ?? 0,
                number_format($client['total_invoice_amount'] ?? 0, 2),
                number_format($client['total_payments'] ?? 0, 2),
                number_format($client['outstanding_amount'] ?? 0, 2),
                number_format($client['payment_ratio'] ?? 0, 2) . '%',
                number_format($client['avg_invoice_value'] ?? 0, 2),
                $client['last_invoice_date'] ?? 'N/A',
                $client['last_payment_date'] ?? 'N/A',
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Client Name',
            'Email',
            'Total Invoices',
            'Total Invoice Amount',
            'Total Payments',
            'Outstanding Amount',
            'Payment Ratio',
            'Average Invoice Value',
            'Last Invoice Date',
            'Last Payment Date',
        ];
    }

    public function title(): string
    {
        return 'Client Performance';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }
}
