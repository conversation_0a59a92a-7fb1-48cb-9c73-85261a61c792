<?php
/**
 * 419 "Page Expired" Error Fix <PERSON>t for Shared Hosting
 * 
 * This script diagnoses and fixes CSRF token validation issues
 * that cause 419 errors in Laravel applications on shared hosting.
 * 
 * Usage: php fix_419_error.php
 */

echo "=== 419 ERROR DIAGNOSTIC AND FIX SCRIPT ===\n\n";

// Bootstrap Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Artisan;

$errors = [];
$warnings = [];
$fixes = [];

echo "🔍 STEP 1: ENVIRONMENT ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

// Check APP_KEY
$appKey = config('app.key');
if (empty($appKey)) {
    $errors[] = "APP_KEY is missing - CSRF tokens cannot be generated";
    echo "   ❌ APP_KEY: Missing\n";
} else {
    echo "   ✅ APP_KEY: Present\n";
}

// Check APP_URL
$appUrl = config('app.url');
echo "   📍 APP_URL: {$appUrl}\n";
if (strpos($appUrl, 'localhost') !== false) {
    $warnings[] = "APP_URL still set to localhost - update for production";
}

// Check Environment
$appEnv = config('app.env');
echo "   🌍 Environment: {$appEnv}\n";

echo "\n🔍 STEP 2: SESSION CONFIGURATION ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

// Check Session Driver
$sessionDriver = config('session.driver');
echo "   💾 Session Driver: {$sessionDriver}\n";

if ($sessionDriver !== 'database') {
    $errors[] = "Session driver should be 'database' for shared hosting reliability";
    echo "   ❌ Recommendation: Use database sessions for shared hosting\n";
} else {
    echo "   ✅ Database sessions configured\n";
}

// Check Session Domain
$sessionDomain = config('session.domain');
echo "   🌐 Session Domain: " . ($sessionDomain ?: 'null (recommended)') . "\n";

// Check Session Security Settings
$sessionSecure = config('session.secure');
$sessionHttpOnly = config('session.http_only');
$sessionSameSite = config('session.same_site');

echo "   🔒 Secure Cookie: " . ($sessionSecure ? 'true' : 'false') . "\n";
echo "   🔒 HTTP Only: " . ($sessionHttpOnly ? 'true' : 'false') . "\n";
echo "   🔒 Same Site: {$sessionSameSite}\n";

echo "\n🔍 STEP 3: DATABASE CONNECTIVITY TEST\n";
echo str_repeat("-", 50) . "\n";

try {
    DB::connection()->getPdo();
    echo "   ✅ Database connection: Successful\n";
    
    // Check if sessions table exists
    if (DB::getSchemaBuilder()->hasTable('sessions')) {
        echo "   ✅ Sessions table: Exists\n";
        
        // Check sessions table structure
        $columns = DB::getSchemaBuilder()->getColumnListing('sessions');
        $requiredColumns = ['id', 'user_id', 'ip_address', 'user_agent', 'payload', 'last_activity'];
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (empty($missingColumns)) {
            echo "   ✅ Sessions table structure: Valid\n";
        } else {
            $errors[] = "Sessions table missing columns: " . implode(', ', $missingColumns);
            echo "   ❌ Sessions table missing columns: " . implode(', ', $missingColumns) . "\n";
        }
        
        // Test session write capability
        try {
            $testId = 'test_' . time();
            DB::table('sessions')->insert([
                'id' => $testId,
                'user_id' => null,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Agent',
                'payload' => 'test_payload',
                'last_activity' => time()
            ]);
            
            DB::table('sessions')->where('id', $testId)->delete();
            echo "   ✅ Session write test: Successful\n";
        } catch (Exception $e) {
            $errors[] = "Cannot write to sessions table: " . $e->getMessage();
            echo "   ❌ Session write test: Failed - " . $e->getMessage() . "\n";
        }
        
    } else {
        $errors[] = "Sessions table does not exist";
        echo "   ❌ Sessions table: Missing\n";
        $fixes[] = "Run: php artisan session:table && php artisan migrate";
    }
    
} catch (Exception $e) {
    $errors[] = "Database connection failed: " . $e->getMessage();
    echo "   ❌ Database connection: Failed - " . $e->getMessage() . "\n";
}

echo "\n🔍 STEP 4: CSRF CONFIGURATION TEST\n";
echo str_repeat("-", 50) . "\n";

// Check CSRF middleware
try {
    $middleware = config('app.middleware', []);
    echo "   🛡️  CSRF Middleware: Configured in bootstrap/app.php\n";
    echo "   ✅ CSRF Protection: Active\n";
} catch (Exception $e) {
    $errors[] = "CSRF middleware configuration issue: " . $e->getMessage();
    echo "   ❌ CSRF Protection: Error - " . $e->getMessage() . "\n";
}

echo "\n🔍 STEP 5: FILE PERMISSIONS CHECK\n";
echo str_repeat("-", 50) . "\n";

// Check storage permissions
$storagePath = storage_path();
if (is_writable($storagePath)) {
    echo "   ✅ Storage directory: Writable\n";
} else {
    $errors[] = "Storage directory is not writable";
    echo "   ❌ Storage directory: Not writable\n";
    $fixes[] = "Set storage directory permissions to 755 or 775";
}

// Check bootstrap/cache permissions
$cachePath = base_path('bootstrap/cache');
if (is_writable($cachePath)) {
    echo "   ✅ Bootstrap cache: Writable\n";
} else {
    $errors[] = "Bootstrap cache directory is not writable";
    echo "   ❌ Bootstrap cache: Not writable\n";
    $fixes[] = "Set bootstrap/cache directory permissions to 755 or 775";
}

echo "\n📊 DIAGNOSTIC SUMMARY\n";
echo str_repeat("=", 50) . "\n";

if (empty($errors)) {
    echo "🎉 SUCCESS: No critical errors found!\n";
    echo "Your configuration should work properly on shared hosting.\n";
} else {
    echo "❌ ERRORS FOUND: " . count($errors) . "\n";
    foreach ($errors as $i => $error) {
        echo "   " . ($i + 1) . ". {$error}\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  WARNINGS: " . count($warnings) . "\n";
    foreach ($warnings as $i => $warning) {
        echo "   " . ($i + 1) . ". {$warning}\n";
    }
}

if (!empty($fixes)) {
    echo "\n🔧 RECOMMENDED FIXES:\n";
    foreach ($fixes as $i => $fix) {
        echo "   " . ($i + 1) . ". {$fix}\n";
    }
}

echo "\n📋 SHARED HOSTING CHECKLIST:\n";
echo str_repeat("-", 50) . "\n";
echo "□ Update .env with your shared hosting database credentials\n";
echo "□ Set APP_URL to your actual domain (https://yourdomain.com)\n";
echo "□ Set SESSION_DOMAIN=null for domain flexibility\n";
echo "□ Set SESSION_SECURE_COOKIE=true for HTTPS\n";
echo "□ Set APP_ENV=production and APP_DEBUG=false\n";
echo "□ Run 'php artisan config:cache' after changes\n";
echo "□ Run 'php artisan route:cache' for better performance\n";
echo "□ Ensure file permissions: storage (755), bootstrap/cache (755)\n";

echo "\n🚀 NEXT STEPS:\n";
echo str_repeat("-", 50) . "\n";
echo "1. Copy .env.shared-hosting to .env and update values\n";
echo "2. Run: php artisan config:clear\n";
echo "3. Run: php artisan config:cache\n";
echo "4. Test login functionality\n";
echo "5. Monitor error logs for any remaining issues\n";

echo "\n✅ Script completed successfully!\n";
