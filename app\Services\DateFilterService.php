<?php

namespace App\Services;

use Carbon\Carbon;
use Carbon\CarbonPeriod;

class DateFilterService
{
    /**
     * Get predefined date ranges
     */
    public static function getPredefinedRanges(): array
    {
        return [
            'today' => [
                'label' => 'Today',
                'start' => Carbon::today(),
                'end' => Carbon::today(),
            ],
            'yesterday' => [
                'label' => 'Yesterday',
                'start' => Carbon::yesterday(),
                'end' => Carbon::yesterday(),
            ],
            'this_week' => [
                'label' => 'This Week',
                'start' => Carbon::now()->startOfWeek(),
                'end' => Carbon::now()->endOfWeek(),
            ],
            'last_week' => [
                'label' => 'Last Week',
                'start' => Carbon::now()->subWeek()->startOfWeek(),
                'end' => Carbon::now()->subWeek()->endOfWeek(),
            ],
            'this_month' => [
                'label' => 'This Month',
                'start' => Carbon::now()->startOfMonth(),
                'end' => Carbon::now()->endOfMonth(),
            ],
            'last_month' => [
                'label' => 'Last Month',
                'start' => Carbon::now()->subMonth()->startOfMonth(),
                'end' => Carbon::now()->subMonth()->endOfMonth(),
            ],
            'this_quarter' => [
                'label' => 'This Quarter',
                'start' => Carbon::now()->startOfQuarter(),
                'end' => Carbon::now()->endOfQuarter(),
            ],
            'last_quarter' => [
                'label' => 'Last Quarter',
                'start' => Carbon::now()->subQuarter()->startOfQuarter(),
                'end' => Carbon::now()->subQuarter()->endOfQuarter(),
            ],
            'this_year' => [
                'label' => 'This Year',
                'start' => Carbon::now()->startOfYear(),
                'end' => Carbon::now()->endOfYear(),
            ],
            'last_year' => [
                'label' => 'Last Year',
                'start' => Carbon::now()->subYear()->startOfYear(),
                'end' => Carbon::now()->subYear()->endOfYear(),
            ],
            'last_7_days' => [
                'label' => 'Last 7 Days',
                'start' => Carbon::now()->subDays(6),
                'end' => Carbon::now(),
            ],
            'last_30_days' => [
                'label' => 'Last 30 Days',
                'start' => Carbon::now()->subDays(29),
                'end' => Carbon::now(),
            ],
            'last_90_days' => [
                'label' => 'Last 90 Days',
                'start' => Carbon::now()->subDays(89),
                'end' => Carbon::now(),
            ],
            'last_365_days' => [
                'label' => 'Last 365 Days',
                'start' => Carbon::now()->subDays(364),
                'end' => Carbon::now(),
            ],
        ];
    }

    /**
     * Get date range from predefined key
     */
    public static function getDateRange(string $key): array
    {
        $ranges = self::getPredefinedRanges();
        
        if (!isset($ranges[$key])) {
            throw new \InvalidArgumentException("Invalid date range key: {$key}");
        }

        return [
            'start' => $ranges[$key]['start'],
            'end' => $ranges[$key]['end'],
            'label' => $ranges[$key]['label'],
        ];
    }

    /**
     * Generate date periods for charts
     */
    public static function generateDatePeriods(Carbon $startDate, Carbon $endDate, string $interval = 'daily'): array
    {
        $periods = [];
        
        switch ($interval) {
            case 'hourly':
                $period = CarbonPeriod::create($startDate->startOfHour(), '1 hour', $endDate->endOfHour());
                foreach ($period as $date) {
                    $periods[] = [
                        'key' => $date->format('Y-m-d H:00'),
                        'label' => $date->format('H:00'),
                        'full_label' => $date->format('M j, Y H:00'),
                        'date' => $date,
                    ];
                }
                break;

            case 'daily':
                $period = CarbonPeriod::create($startDate->startOfDay(), '1 day', $endDate->endOfDay());
                foreach ($period as $date) {
                    $periods[] = [
                        'key' => $date->format('Y-m-d'),
                        'label' => $date->format('M j'),
                        'full_label' => $date->format('M j, Y'),
                        'date' => $date,
                    ];
                }
                break;

            case 'weekly':
                $start = $startDate->copy()->startOfWeek();
                $end = $endDate->copy()->endOfWeek();
                $period = CarbonPeriod::create($start, '1 week', $end);
                foreach ($period as $date) {
                    $weekEnd = $date->copy()->endOfWeek();
                    $periods[] = [
                        'key' => $date->format('Y-W'),
                        'label' => $date->format('M j') . ' - ' . $weekEnd->format('M j'),
                        'full_label' => $date->format('M j, Y') . ' - ' . $weekEnd->format('M j, Y'),
                        'date' => $date,
                    ];
                }
                break;

            case 'monthly':
                $start = $startDate->copy()->startOfMonth();
                $end = $endDate->copy()->endOfMonth();
                $period = CarbonPeriod::create($start, '1 month', $end);
                foreach ($period as $date) {
                    $periods[] = [
                        'key' => $date->format('Y-m'),
                        'label' => $date->format('M Y'),
                        'full_label' => $date->format('F Y'),
                        'date' => $date,
                    ];
                }
                break;

            case 'quarterly':
                $start = $startDate->copy()->startOfQuarter();
                $end = $endDate->copy()->endOfQuarter();
                $period = CarbonPeriod::create($start, '3 months', $end);
                foreach ($period as $date) {
                    $quarter = $date->quarter;
                    $periods[] = [
                        'key' => $date->format('Y') . '-Q' . $quarter,
                        'label' => 'Q' . $quarter . ' ' . $date->format('Y'),
                        'full_label' => 'Quarter ' . $quarter . ' ' . $date->format('Y'),
                        'date' => $date,
                    ];
                }
                break;

            case 'yearly':
                $start = $startDate->copy()->startOfYear();
                $end = $endDate->copy()->endOfYear();
                $period = CarbonPeriod::create($start, '1 year', $end);
                foreach ($period as $date) {
                    $periods[] = [
                        'key' => $date->format('Y'),
                        'label' => $date->format('Y'),
                        'full_label' => $date->format('Y'),
                        'date' => $date,
                    ];
                }
                break;

            default:
                throw new \InvalidArgumentException("Invalid interval: {$interval}");
        }

        return $periods;
    }

    /**
     * Get optimal interval based on date range
     */
    public static function getOptimalInterval(Carbon $startDate, Carbon $endDate): string
    {
        $diffInDays = $startDate->diffInDays($endDate);

        if ($diffInDays <= 1) {
            return 'hourly';
        } elseif ($diffInDays <= 31) {
            return 'daily';
        } elseif ($diffInDays <= 90) {
            return 'weekly';
        } elseif ($diffInDays <= 365) {
            return 'monthly';
        } elseif ($diffInDays <= 1095) { // 3 years
            return 'quarterly';
        } else {
            return 'yearly';
        }
    }

    /**
     * Format date for database queries
     */
    public static function formatForDatabase(Carbon $date, string $interval): string
    {
        return match($interval) {
            'hourly' => $date->format('Y-m-d H:00:00'),
            'daily' => $date->format('Y-m-d'),
            'weekly' => $date->startOfWeek()->format('Y-m-d'),
            'monthly' => $date->startOfMonth()->format('Y-m-d'),
            'quarterly' => $date->startOfQuarter()->format('Y-m-d'),
            'yearly' => $date->startOfYear()->format('Y-m-d'),
            default => $date->format('Y-m-d'),
        };
    }

    /**
     * Get MySQL date format for grouping
     */
    public static function getMySQLDateFormat(string $interval): string
    {
        return match($interval) {
            'hourly' => '%Y-%m-%d %H:00:00',
            'daily' => '%Y-%m-%d',
            'weekly' => '%Y-%u',
            'monthly' => '%Y-%m',
            'quarterly' => '%Y-Q%q',
            'yearly' => '%Y',
            default => '%Y-%m-%d',
        };
    }

    /**
     * Validate date range
     */
    public static function validateDateRange(Carbon $startDate, Carbon $endDate): array
    {
        $errors = [];

        if ($startDate->isAfter($endDate)) {
            $errors[] = 'Start date cannot be after end date';
        }

        if ($startDate->isFuture()) {
            $errors[] = 'Start date cannot be in the future';
        }

        if ($endDate->isFuture()) {
            $errors[] = 'End date cannot be in the future';
        }

        $maxRange = Carbon::now()->subYears(5);
        if ($startDate->isBefore($maxRange)) {
            $errors[] = 'Date range cannot exceed 5 years';
        }

        $diffInDays = $startDate->diffInDays($endDate);
        if ($diffInDays > 1095) { // 3 years
            $errors[] = 'Date range cannot exceed 3 years';
        }

        return $errors;
    }

    /**
     * Get comparison periods
     */
    public static function getComparisonPeriod(Carbon $startDate, Carbon $endDate): array
    {
        $diffInDays = $startDate->diffInDays($endDate);
        
        $comparisonStart = $startDate->copy()->subDays($diffInDays + 1);
        $comparisonEnd = $startDate->copy()->subDay();

        return [
            'start' => $comparisonStart,
            'end' => $comparisonEnd,
            'label' => 'Previous Period',
        ];
    }

    /**
     * Get year-over-year comparison
     */
    public static function getYearOverYearComparison(Carbon $startDate, Carbon $endDate): array
    {
        $comparisonStart = $startDate->copy()->subYear();
        $comparisonEnd = $endDate->copy()->subYear();

        return [
            'start' => $comparisonStart,
            'end' => $comparisonEnd,
            'label' => 'Same Period Last Year',
        ];
    }
}
