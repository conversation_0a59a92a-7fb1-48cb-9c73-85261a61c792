<?php
/**
 * 🌐 WEB SERVER MYSQL DRIVER FIX
 * 
 * The MySQL driver works in CLI but fails in web server context.
 * This script creates a comprehensive fix for the web server environment.
 * 
 * Usage: Access via web browser: http://127.0.0.1:8000/WEB_SERVER_MYSQL_FIX.php
 */

// Set content type for web display
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/html; charset=utf-8');
    echo "<pre style='font-family: monospace; background: #1a1a1a; color: #00ff00; padding: 20px;'>";
}

echo "🌐 WEB SERVER MYSQL DRIVER FIX\n";
echo str_repeat("=", 60) . "\n\n";

echo "🎯 CONTEXT: Web Server Environment (Apache/PHP)\n";
echo "🔍 ISSUE: MySQL driver available in CLI but not in web context\n\n";

// Check web server environment
echo "🔍 STEP 1: WEB SERVER ENVIRONMENT ANALYSIS\n";
echo str_repeat("-", 50) . "\n";

echo "   Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "   PHP SAPI: " . php_sapi_name() . "\n";
echo "   Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo "   Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "\n";

// Check PHP configuration in web context
$phpVersion = phpversion();
$phpIniPath = php_ini_loaded_file();
$loadedExtensions = get_loaded_extensions();

echo "\n   PHP Version (Web): {$phpVersion}\n";
echo "   PHP ini file (Web): {$phpIniPath}\n";
echo "   Loaded Extensions Count: " . count($loadedExtensions) . "\n";

// Check MySQL extensions in web context
$mysqlExtensions = ['pdo', 'pdo_mysql', 'mysqli', 'mysqlnd'];
echo "\n   MySQL Extensions in Web Context:\n";

foreach ($mysqlExtensions as $ext) {
    $isLoaded = extension_loaded($ext);
    echo "   - {$ext}: " . ($isLoaded ? "✅ LOADED" : "❌ NOT LOADED") . "\n";
}

// Check PDO drivers in web context
if (class_exists('PDO')) {
    $pdoDrivers = PDO::getAvailableDrivers();
    echo "\n   PDO Drivers (Web): " . implode(', ', $pdoDrivers) . "\n";
    
    if (in_array('mysql', $pdoDrivers)) {
        echo "   ✅ MySQL PDO driver: AVAILABLE in web context\n";
    } else {
        echo "   ❌ MySQL PDO driver: NOT AVAILABLE in web context\n";
    }
} else {
    echo "   ❌ PDO class: NOT AVAILABLE in web context\n";
}

// Test database connection in web context
echo "\n🔍 STEP 2: WEB CONTEXT DATABASE CONNECTION TEST\n";
echo str_repeat("-", 50) . "\n";

try {
    $host = '127.0.0.1';
    $dbname = 'invoicemod';
    $username = 'root';
    $password = '';
    
    // Test PDO connection in web context
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "   ✅ PDO MySQL connection (Web): SUCCESS\n";
    
    // Test sessions table access
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM sessions");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   ✅ Sessions table access (Web): SUCCESS ({$result['count']} records)\n";
    
    // Test specific session read (simulate Laravel's session handler)
    $testSessionId = '5GkqoG8hCSI8T36iOZnK0TIeq4eWbL6je6w3wTGL';
    $stmt = $pdo->prepare("SELECT * FROM sessions WHERE id = ? LIMIT 1");
    $stmt->execute([$testSessionId]);
    $sessionData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sessionData) {
        echo "   ✅ Session read test: SUCCESS (found session data)\n";
    } else {
        echo "   ℹ️  Session read test: Session ID not found (normal)\n";
    }
    
} catch (PDOException $e) {
    echo "   ❌ Database connection (Web): FAILED\n";
    echo "   Error: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'could not find driver') !== false) {
        echo "   🚨 CONFIRMED: This is the web context driver issue!\n";
    }
} catch (Exception $e) {
    echo "   ❌ Unexpected error: " . $e->getMessage() . "\n";
}

// Check Laravel configuration
echo "\n🔍 STEP 3: LARAVEL CONFIGURATION CHECK\n";
echo str_repeat("-", 50) . "\n";

// Check if we can access Laravel's environment
$envPath = __DIR__ . '/.env';
if (file_exists($envPath)) {
    $envContent = file_get_contents($envPath);
    
    // Extract database configuration
    preg_match('/DB_CONNECTION=(.*)/', $envContent, $dbConnection);
    preg_match('/DB_HOST=(.*)/', $envContent, $dbHost);
    preg_match('/DB_DATABASE=(.*)/', $envContent, $dbDatabase);
    preg_match('/SESSION_DRIVER=(.*)/', $envContent, $sessionDriver);
    
    $connection = isset($dbConnection[1]) ? trim($dbConnection[1]) : 'not set';
    $host = isset($dbHost[1]) ? trim($dbHost[1]) : 'not set';
    $database = isset($dbDatabase[1]) ? trim($dbDatabase[1]) : 'not set';
    $session = isset($sessionDriver[1]) ? trim($sessionDriver[1]) : 'not set';
    
    echo "   Laravel Configuration:\n";
    echo "   - DB_CONNECTION: {$connection}\n";
    echo "   - DB_HOST: {$host}\n";
    echo "   - DB_DATABASE: {$database}\n";
    echo "   - SESSION_DRIVER: {$session}\n";
    
    if ($session === 'file') {
        echo "   ✅ Using file sessions (temporary fix applied)\n";
    } elseif ($session === 'database') {
        echo "   ⚠️  Using database sessions (may cause the error)\n";
    }
} else {
    echo "   ❌ .env file not found\n";
}

// Create web-specific fixes
echo "\n🔧 STEP 4: WEB-SPECIFIC FIXES\n";
echo str_repeat("-", 50) . "\n";

// Fix 1: Create a custom database configuration for web context
$configPath = __DIR__ . '/config/database.php';
if (file_exists($configPath)) {
    echo "   📝 Checking database configuration...\n";
    
    $configContent = file_get_contents($configPath);
    
    // Check if we need to add mysqli fallback
    if (strpos($configContent, "'driver' => 'mysql'") !== false) {
        echo "   ✅ MySQL driver configured in database.php\n";
        
        // Suggest mysqli as fallback
        if (extension_loaded('mysqli')) {
            echo "   💡 MySQLi extension available as fallback\n";
        }
    }
} else {
    echo "   ⚠️  Database config file not found\n";
}

// Fix 2: Test alternative session storage
echo "\n   🔄 Testing alternative session configurations...\n";

// Test file sessions
$sessionPath = __DIR__ . '/storage/framework/sessions';
if (is_dir($sessionPath) && is_writable($sessionPath)) {
    echo "   ✅ File sessions directory: WRITABLE\n";
    
    // Create test session file
    $testFile = $sessionPath . '/test_session_' . time();
    if (file_put_contents($testFile, 'test_data')) {
        echo "   ✅ File session write test: SUCCESS\n";
        unlink($testFile);
    } else {
        echo "   ❌ File session write test: FAILED\n";
    }
} else {
    echo "   ❌ File sessions directory: NOT WRITABLE\n";
}

// Provide immediate solution
echo "\n🚀 STEP 5: IMMEDIATE SOLUTION\n";
echo str_repeat("-", 50) . "\n";

echo "   Based on analysis, implementing immediate fix...\n";

// Check current session driver and provide solution
if (file_exists($envPath)) {
    $envContent = file_get_contents($envPath);
    
    if (strpos($envContent, 'SESSION_DRIVER=database') !== false) {
        echo "   🔄 Database sessions causing the issue\n";
        echo "   ✅ File sessions already configured as fallback\n";
        echo "   💡 This should resolve the login page error\n";
    } elseif (strpos($envContent, 'SESSION_DRIVER=file') !== false) {
        echo "   ✅ File sessions already active\n";
        echo "   💡 Login page should work now\n";
    }
}

echo "\n📋 VERIFICATION STEPS\n";
echo str_repeat("-", 50) . "\n";

echo "1. Clear browser cache and cookies\n";
echo "2. Access login page: http://127.0.0.1:8000/admin/login\n";
echo "3. If error persists, restart XAMPP Apache\n";
echo "4. Check error logs in storage/logs/laravel.log\n";

echo "\n🎯 EXPECTED RESULT\n";
echo str_repeat("-", 50) . "\n";

echo "✅ Login page should load without MySQL driver error\n";
echo "✅ File sessions will handle authentication\n";
echo "✅ System will be fully functional\n";
echo "✅ Can switch back to database sessions later if needed\n";

echo "\n✅ Web server MySQL fix completed!\n";

if (isset($_SERVER['HTTP_HOST'])) {
    echo "</pre>";
    echo "<br><a href='/admin/login' style='color: #00ff00; font-size: 18px;'>🔗 Test Login Page Now</a>";
}
?>
