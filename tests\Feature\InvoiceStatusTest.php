<?php

namespace Tests\Feature;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Models\User;
use App\Services\InvoiceStatusService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InvoiceStatusTest extends TestCase
{
    use RefreshDatabase;

    protected InvoiceStatusService $statusService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->statusService = new InvoiceStatusService();
    }

    /** @test */
    public function it_can_validate_status_transitions()
    {
        // Valid transitions
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::DRAFT, Invoice::UNPAID));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::UNPAID, Invoice::PAID));
        $this->assertTrue($this->statusService->isValidStatusTransition(Invoice::UNPAID, Invoice::PARTIALLY));
        
        // Invalid transitions
        $this->assertFalse($this->statusService->isValidStatusTransition(Invoice::PAID, Invoice::DRAFT));
        $this->assertFalse($this->statusService->isValidStatusTransition(Invoice::DRAFT, Invoice::PAID));
    }

    /** @test */
    public function it_updates_status_based_on_payment_amount()
    {
        // Create test data
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::UNPAID
        ]);

        // Test partial payment
        $newStatus = $this->statusService->updateStatusFromPayment($invoice, 50.00);
        $this->assertEquals(Invoice::PARTIALLY, $newStatus);

        // Test full payment
        $newStatus = $this->statusService->updateStatusFromPayment($invoice, 50.00);
        $this->assertEquals(Invoice::PAID, $newStatus);
    }

    /** @test */
    public function it_calculates_current_status_correctly()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::UNPAID
        ]);

        // No payments - should be UNPAID
        $status = $this->statusService->calculateCurrentStatus($invoice);
        $this->assertEquals(Invoice::UNPAID, $status);

        // Add partial payment
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 50.00,
            'is_approved' => Payment::APPROVED
        ]);

        $status = $this->statusService->calculateCurrentStatus($invoice);
        $this->assertEquals(Invoice::PARTIALLY, $status);

        // Add remaining payment
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 50.00,
            'is_approved' => Payment::APPROVED
        ]);

        $status = $this->statusService->calculateCurrentStatus($invoice);
        $this->assertEquals(Invoice::PAID, $status);
    }

    /** @test */
    public function it_only_counts_approved_payments()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::UNPAID
        ]);

        // Add unapproved payment
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 100.00,
            'is_approved' => Payment::PENDING
        ]);

        $status = $this->statusService->calculateCurrentStatus($invoice);
        $this->assertEquals(Invoice::UNPAID, $status);

        // Approve the payment
        $invoice->payments()->update(['is_approved' => Payment::APPROVED]);
        
        $status = $this->statusService->calculateCurrentStatus($invoice);
        $this->assertEquals(Invoice::PAID, $status);
    }
}
