<?php

namespace App\Providers;

use App\Models\User;
use App\Policies\ReportPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => ReportPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define gates for report access
        Gate::define('viewFinancialReports', [ReportPolicy::class, 'viewFinancialReports']);
        Gate::define('viewInvoiceAnalytics', [ReportPolicy::class, 'viewInvoiceAnalytics']);
        Gate::define('viewClientReports', [ReportPolicy::class, 'viewClientReports']);
        Gate::define('viewProductAnalytics', [ReportPolicy::class, 'viewProductAnalytics']);
        Gate::define('exportReports', [ReportPolicy::class, 'exportReports']);
        Gate::define('viewSensitiveData', [ReportPolicy::class, 'viewSensitiveData']);
        Gate::define('viewAllClientData', [ReportPolicy::class, 'viewAllClientData']);
        Gate::define('viewDetailedAnalytics', [ReportPolicy::class, 'viewDetailedAnalytics']);
        Gate::define('viewDashboardMetrics', [ReportPolicy::class, 'viewDashboardMetrics']);
        Gate::define('viewOwnClientReports', [ReportPolicy::class, 'viewOwnClientReports']);

        // Super admin can do everything
        Gate::before(function (User $user, string $ability) {
            if ($user->hasRole('super_admin')) {
                return true;
            }
        });
    }
}
