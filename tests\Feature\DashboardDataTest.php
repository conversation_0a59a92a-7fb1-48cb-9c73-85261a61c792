<?php

namespace Tests\Feature;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Models\User;
use App\Repositories\DashboardRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DashboardDataTest extends TestCase
{
    use RefreshDatabase;

    protected DashboardRepository $dashboardRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardRepository = new DashboardRepository();
    }

    /** @test */
    public function it_only_includes_approved_payments_in_received_amount()
    {
        // Create test data
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::UNPAID
        ]);

        // Add approved payment
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 50.00,
            'is_approved' => Payment::APPROVED
        ]);

        // Add pending payment (should not be included)
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 30.00,
            'is_approved' => Payment::PENDING
        ]);

        $data = $this->dashboardRepository->getPaymentOverviewData();

        // Should only include the approved payment amount
        $this->assertEquals(50.00, $data['received_amount']);
        $this->assertEquals(100.00, $data['invoice_amount']);
        $this->assertEquals(50.00, $data['due_amount']);
    }

    /** @test */
    public function it_excludes_draft_invoices_from_invoice_amount()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        
        // Create draft invoice (should be excluded)
        Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 50.00,
            'status' => Invoice::DRAFT
        ]);

        // Create unpaid invoice (should be included)
        Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::UNPAID
        ]);

        $data = $this->dashboardRepository->getPaymentOverviewData();

        // Should only include non-draft invoices
        $this->assertEquals(100.00, $data['invoice_amount']);
    }

    /** @test */
    public function it_calculates_due_amount_correctly()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::UNPAID
        ]);

        // Add partial approved payment
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 30.00,
            'is_approved' => Payment::APPROVED
        ]);

        $data = $this->dashboardRepository->getPaymentOverviewData();

        $this->assertEquals(30.00, $data['received_amount']);
        $this->assertEquals(100.00, $data['invoice_amount']);
        $this->assertEquals(70.00, $data['due_amount']);
    }

    /** @test */
    public function it_handles_overpayment_scenarios()
    {
        $user = User::factory()->create();
        $client = Client::factory()->create(['user_id' => $user->id]);
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 100.00,
            'status' => Invoice::PAID
        ]);

        // Add overpayment
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 120.00,
            'is_approved' => Payment::APPROVED
        ]);

        $data = $this->dashboardRepository->getPaymentOverviewData();

        $this->assertEquals(120.00, $data['received_amount']);
        $this->assertEquals(100.00, $data['invoice_amount']);
        // Due amount should not be negative
        $this->assertEquals(0, $data['due_amount']);
    }
}
