<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Add performance indexes for invoice creation optimization
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Index for client dropdown search
            $table->index(['first_name', 'last_name']);
            $table->index('email');
        });

        Schema::table('clients', function (Blueprint $table) {
            // Index for company name search
            $table->index('company_name');
            $table->index('user_id');
        });

        Schema::table('products', function (Blueprint $table) {
            // Index for product search
            $table->index('name');
            $table->index(['name', 'description']);
        });

        Schema::table('taxes', function (Blueprint $table) {
            // Index for tax dropdown
            $table->index('name');
        });

        Schema::table('invoices', function (Blueprint $table) {
            // Index for invoice queries
            $table->index(['client_id', 'status']);
            $table->index('invoice_date');
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            // Index for invoice items
            $table->index('invoice_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['first_name', 'last_name']);
            $table->dropIndex(['email']);
        });

        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex(['company_name']);
            $table->dropIndex(['user_id']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['name']);
            $table->dropIndex(['name', 'description']);
        });

        Schema::table('taxes', function (Blueprint $table) {
            $table->dropIndex(['name']);
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['client_id', 'status']);
            $table->dropIndex(['invoice_date']);
        });

        Schema::table('invoice_items', function (Blueprint $table) {
            $table->dropIndex(['invoice_id']);
            $table->dropIndex(['product_id']);
        });
    }
};
