@echo off
echo =====================================================
echo DATABASE BACKUP SCRIPT FOR INVOICE APPLICATION
echo =====================================================
echo.

set BACKUP_DIR=database_backups
set DATE_STAMP=%date:~-4,4%-%date:~-10,2%-%date:~-7,2%
set TIME_STAMP=%time:~0,2%-%time:~3,2%-%time:~6,2%
set TIMESTAMP=%DATE_STAMP%_%TIME_STAMP%

echo Creating backup directory...
if not exist %BACKUP_DIR% mkdir %BACKUP_DIR%

echo.
echo Creating database backup...
echo Timestamp: %TIMESTAMP%
echo.

echo 1. Creating full backup (structure + data)...
C:\xampp\mysql\bin\mysqldump.exe -u root invoicemod > %BACKUP_DIR%\invoicemod_full_%TIMESTAMP%.sql
if %errorlevel% equ 0 (
    echo ✅ Full backup created: %BACKUP_DIR%\invoicemod_full_%TIMESTAMP%.sql
) else (
    echo ❌ Full backup failed!
)

echo.
echo 2. Creating structure-only backup...
C:\xampp\mysql\bin\mysqldump.exe -u root --no-data invoicemod > %BACKUP_DIR%\invoicemod_structure_%TIMESTAMP%.sql
if %errorlevel% equ 0 (
    echo ✅ Structure backup created: %BACKUP_DIR%\invoicemod_structure_%TIMESTAMP%.sql
) else (
    echo ❌ Structure backup failed!
)

echo.
echo 3. Creating data-only backup...
C:\xampp\mysql\bin\mysqldump.exe -u root --no-create-info invoicemod > %BACKUP_DIR%\invoicemod_data_%TIMESTAMP%.sql
if %errorlevel% equ 0 (
    echo ✅ Data backup created: %BACKUP_DIR%\invoicemod_data_%TIMESTAMP%.sql
) else (
    echo ❌ Data backup failed!
)

echo.
echo 4. Creating compressed backup...
if exist "C:\Program Files\7-Zip\7z.exe" (
    "C:\Program Files\7-Zip\7z.exe" a %BACKUP_DIR%\invoicemod_backup_%TIMESTAMP%.zip %BACKUP_DIR%\invoicemod_full_%TIMESTAMP%.sql
    echo ✅ Compressed backup created: %BACKUP_DIR%\invoicemod_backup_%TIMESTAMP%.zip
) else (
    echo ⚠️  7-Zip not found, skipping compression
)

echo.
echo =====================================================
echo BACKUP COMPLETE!
echo.
echo Files created:
dir %BACKUP_DIR%\*%TIMESTAMP%*
echo.
echo Ready for deployment to shared hosting!
echo =====================================================
pause
