<?php

namespace App\Services;

use App\Models\User;
use App\Models\Product;
use App\Models\Tax;
use App\Models\Client;
use Illuminate\Support\Facades\Cache;

class InvoicePerformanceService
{
    /**
     * Cache duration in seconds (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Get cached client options for dropdown
     */
    public static function getCachedClientOptions(int $limit = 100): array
    {
        return Cache::remember('invoice_client_options', self::CACHE_DURATION, function () use ($limit) {
            return User::whereHas('client')
                ->with('client:id,user_id,company_name')
                ->select('id', 'first_name', 'last_name', 'email')
                ->limit($limit)
                ->get()
                ->pluck('full_name', 'id')
                ->toArray();
        });
    }

    /**
     * Search clients efficiently
     */
    public static function searchClients(string $search, int $limit = 50): array
    {
        $cacheKey = 'client_search_' . md5($search);
        
        return Cache::remember($cacheKey, 60, function () use ($search, $limit) {
            return User::whereHas('client', function ($query) use ($search) {
                    $query->where('company_name', 'like', "%{$search}%");
                })
                ->orWhere('first_name', 'like', "%{$search}%")
                ->orWhere('last_name', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%")
                ->with('client:id,user_id,company_name')
                ->select('id', 'first_name', 'last_name', 'email')
                ->limit($limit)
                ->get()
                ->pluck('full_name', 'id')
                ->toArray();
        });
    }

    /**
     * Get cached product options
     */
    public static function getCachedProductOptions(int $limit = 20): array
    {
        return Cache::remember('invoice_product_options', self::CACHE_DURATION, function () use ($limit) {
            return Product::select('name')
                ->orderBy('name')
                ->limit($limit)
                ->pluck('name', 'name')
                ->toArray();
        });
    }

    /**
     * Search products efficiently
     */
    public static function searchProducts(string $search, int $limit = 50): array
    {
        $cacheKey = 'product_search_' . md5($search);
        
        return Cache::remember($cacheKey, 60, function () use ($search, $limit) {
            return Product::where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%")
                ->select('name')
                ->limit($limit)
                ->pluck('name', 'name')
                ->toArray();
        });
    }

    /**
     * Get cached tax options
     */
    public static function getCachedTaxOptions(): array
    {
        return Cache::remember('invoice_tax_options', self::CACHE_DURATION, function () {
            return Tax::select('id', 'name')
                ->orderBy('name')
                ->pluck('name', 'id')
                ->toArray();
        });
    }

    /**
     * Get product details efficiently
     */
    public static function getProductDetails(int $productId): ?array
    {
        $cacheKey = 'product_details_' . $productId;
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($productId) {
            $product = Product::select('id', 'name', 'price', 'description')
                ->find($productId);
                
            return $product ? $product->toArray() : null;
        });
    }

    /**
     * Clear all invoice-related caches
     */
    public static function clearCaches(): void
    {
        $patterns = [
            'invoice_client_options',
            'invoice_product_options', 
            'invoice_tax_options',
            'client_search_*',
            'product_search_*',
            'product_details_*'
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // For wildcard patterns, we'd need to implement cache tag clearing
                // For now, just clear the main caches
                continue;
            }
            Cache::forget($pattern);
        }
    }

    /**
     * Warm up caches with frequently accessed data
     */
    public static function warmUpCaches(): void
    {
        // Pre-load frequently accessed data
        self::getCachedClientOptions();
        self::getCachedProductOptions();
        self::getCachedTaxOptions();
    }
}
