<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Client;
use App\Services\ReportingService;
use App\Services\ExportService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ReportingSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $adminUser;
    protected User $clientUser;
    protected ReportingService $reportingService;
    protected ExportService $exportService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->adminUser = User::factory()->create();
        $this->adminUser->assignRole('admin');
        
        $this->clientUser = User::factory()->create();
        $this->clientUser->assignRole('client');
        
        $this->reportingService = app(ReportingService::class);
        $this->exportService = app(ExportService::class);
    }

    /** @test */
    public function admin_can_access_financial_reports()
    {
        $this->actingAs($this->adminUser);
        
        $response = $this->get('/admin/financial-reports');
        $response->assertStatus(200);
    }

    /** @test */
    public function client_cannot_access_financial_reports()
    {
        $this->actingAs($this->clientUser);
        
        $response = $this->get('/admin/financial-reports');
        $response->assertStatus(403);
    }

    /** @test */
    public function financial_summary_returns_correct_data()
    {
        // Create test data
        $client = Client::factory()->create();
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 1000,
            'status' => Invoice::PAID,
            'invoice_date' => Carbon::now(),
        ]);
        
        Payment::factory()->create([
            'invoice_id' => $invoice->id,
            'amount' => 1000,
            'is_approved' => Payment::APPROVED,
            'payment_date' => Carbon::now(),
        ]);

        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        $summary = $this->reportingService->getFinancialSummary($startDate, $endDate);
        
        $this->assertEquals(1, $summary['total_invoices']);
        $this->assertEquals(1000, $summary['total_invoice_amount']);
        $this->assertEquals(1000, $summary['total_payments_received']);
        $this->assertEquals(0, $summary['outstanding_amount']);
        $this->assertEquals(1, $summary['paid_invoices']);
        $this->assertEquals(100, $summary['payment_success_rate']);
    }

    /** @test */
    public function client_performance_report_works()
    {
        $client = Client::factory()->create();
        $invoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 500,
            'status' => Invoice::UNPAID,
            'invoice_date' => Carbon::now(),
        ]);

        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        $performance = $this->reportingService->getClientPerformance($startDate, $endDate);
        
        $this->assertCount(1, $performance);
        $clientData = $performance->first();
        $this->assertEquals($client->id, $clientData['client_id']);
        $this->assertEquals(500, $clientData['total_invoice_amount']);
        $this->assertEquals(0, $clientData['total_payments']);
        $this->assertEquals(500, $clientData['outstanding_amount']);
    }

    /** @test */
    public function overdue_invoices_report_works()
    {
        $client = Client::factory()->create();
        $overdueInvoice = Invoice::factory()->create([
            'client_id' => $client->id,
            'final_amount' => 300,
            'status' => Invoice::UNPAID,
            'due_date' => Carbon::now()->subDays(10),
            'invoice_date' => Carbon::now()->subDays(20),
        ]);

        $overdueInvoices = $this->reportingService->getOverdueInvoices();
        
        $this->assertCount(1, $overdueInvoices);
        $overdueData = $overdueInvoices->first();
        $this->assertEquals($overdueInvoice->invoice_id, $overdueData['invoice_id']);
        $this->assertEquals(300, $overdueData['outstanding_amount']);
        $this->assertEquals(10, $overdueData['days_overdue']);
    }

    /** @test */
    public function revenue_trends_calculation_works()
    {
        Payment::factory()->create([
            'amount' => 200,
            'is_approved' => Payment::APPROVED,
            'payment_date' => Carbon::now(),
        ]);

        Payment::factory()->create([
            'amount' => 300,
            'is_approved' => Payment::APPROVED,
            'payment_date' => Carbon::now(),
        ]);

        $startDate = Carbon::now()->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        $trends = $this->reportingService->getRevenueTrends($startDate, $endDate, 'daily');
        
        $this->assertCount(1, $trends);
        $todayTrend = $trends[0];
        $this->assertEquals(500, $todayTrend['total_revenue']);
        $this->assertEquals(2, $todayTrend['payment_count']);
    }

    /** @test */
    public function tax_summary_calculation_works()
    {
        // This test would require setting up tax data
        // For now, we'll test the basic structure
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        $taxSummary = $this->reportingService->getTaxSummary($startDate, $endDate);
        
        $this->assertIsArray($taxSummary);
        $this->assertArrayHasKey('tax_breakdown', $taxSummary);
        $this->assertArrayHasKey('total_tax_amount', $taxSummary);
        $this->assertArrayHasKey('total_invoices_with_tax', $taxSummary);
    }

    /** @test */
    public function payment_method_analysis_works()
    {
        Payment::factory()->create([
            'amount' => 100,
            'payment_mode' => 1, // Assuming 1 is cash
            'is_approved' => Payment::APPROVED,
            'payment_date' => Carbon::now(),
        ]);

        Payment::factory()->create([
            'amount' => 200,
            'payment_mode' => 2, // Assuming 2 is card
            'is_approved' => Payment::APPROVED,
            'payment_date' => Carbon::now(),
        ]);

        $startDate = Carbon::now()->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        $analysis = $this->reportingService->getPaymentMethodAnalysis($startDate, $endDate);
        
        $this->assertCount(2, $analysis);
        $this->assertEquals(300, $analysis->sum('total_amount'));
    }

    /** @test */
    public function csv_export_generates_valid_data()
    {
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        $csv = $this->reportingService->exportToCSV('financial', $startDate, $endDate);
        
        $this->assertIsString($csv);
        // Basic CSV validation - should contain headers
        $lines = explode("\n", $csv);
        $this->assertGreaterThan(0, count($lines));
    }

    /** @test */
    public function dashboard_metrics_compilation_works()
    {
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        $metrics = $this->reportingService->getDashboardMetrics($startDate, $endDate);
        
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('financial_summary', $metrics);
        $this->assertArrayHasKey('top_clients', $metrics);
        $this->assertArrayHasKey('top_products', $metrics);
        $this->assertArrayHasKey('overdue_summary', $metrics);
        $this->assertArrayHasKey('recent_trends', $metrics);
    }

    /** @test */
    public function invoice_status_distribution_works()
    {
        // Create invoices with different statuses
        Invoice::factory()->create(['status' => Invoice::PAID, 'final_amount' => 100]);
        Invoice::factory()->create(['status' => Invoice::UNPAID, 'final_amount' => 200]);
        Invoice::factory()->create(['status' => Invoice::PARTIALLY, 'final_amount' => 150]);

        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        
        $distribution = $this->reportingService->getInvoiceStatusDistribution($startDate, $endDate);
        
        $this->assertCount(3, $distribution);
        
        $totalAmount = array_sum(array_column($distribution, 'total_amount'));
        $this->assertEquals(450, $totalAmount);
    }

    /** @test */
    public function reporting_pages_load_correctly()
    {
        $this->actingAs($this->adminUser);
        
        // Test all reporting pages
        $pages = [
            '/admin/financial-reports',
            '/admin/invoice-analytics', 
            '/admin/client-reports',
            '/admin/product-analytics',
        ];

        foreach ($pages as $page) {
            $response = $this->get($page);
            $response->assertStatus(200);
        }
    }
}
